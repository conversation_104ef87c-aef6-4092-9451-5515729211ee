import {axios} from '../server.js';
import {Agent} from 'https';

// create https agent with expected ciphers to avoid 403 from cloudflare
const agent = new Agent({
    ciphers: ['TLS_CHACHA20_POLY1305_SHA256', 'TLS_AES_128_GCM_SHA256', 'TLS_AES_256_GCM_SHA384', 'TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256'].join(':'),
    honorCipherOrder: true,
    minVersion: 'TLSv1.3',
});
const parseTokensFromUrl = uri => {
    let url = new URL(uri);
    let params = new URLSearchParams(url.hash.substring(1));
    return {
        access_token: params.get('access_token'),
        id_token: params.get('id_token'),
    };
};

const auth_data = {
    user_agent: 'RiotClient/63.0.9.4909983.4789131 rso-auth (Windows; 10;;Professional, x64)',
    client_version: 'release-06.07-shipping-15-860725',
    client_platform: {
        platformType: 'PC',
        platformOS: 'Windows',
        platformOSVersion: '10.0.19043.1.256.64bit',
        platformChipset: 'Unknown',
    },
};

export const auth = async (username, password) => {
    // fetch session cookie
    const cookie = (
        await axios
            .post(
                'https://auth.riotgames.com/api/v1/authorization',
                {
                    client_id: 'valorant-client',
                    nonce: 1,
                    redirect_uri: 'http://localhost/redirect',
                    response_type: 'token id_token',
                    scope: 'account openid',
                },
                {
                    headers: {
                        'User-Agent': auth_data.user_agent,
                    },
                    httpsAgent: agent,
                }
            )
            .catch(e => e)
    ).headers['set-cookie'].find(elem => /^asid/.test(elem));
    // fetch auth tokens
    var access_tokens = await axios
        .put(
            'https://auth.riotgames.com/api/v1/authorization',
            {
                type: 'auth',
                username: username,
                password: password,
            },
            {
                headers: {
                    Cookie: cookie,
                    'User-Agent': auth_data.user_agent,
                },
                httpsAgent: agent,
            }
        )
        .catch(e => e);
    console.log(access_tokens.headers['set-cookie'].find(elem => /^ssid/.test(elem)));
    // throw exception for auth_failure
    if (access_tokens.data?.error === 'auth_failure') {
        return {error: 'auth_failure: username or password is incorrect.'};
    }

    // update access token
    var tokens = parseTokensFromUrl(access_tokens.data.response.parameters.uri);
    const access_token = tokens.access_token;

    // fetch entitlements token
    const entitlements_token = (
        await axios
            .post(
                'https://entitlements.auth.riotgames.com/api/token/v1',
                {},
                {
                    headers: {
                        Authorization: `Bearer ${tokens.access_token}`,
                    },
                }
            )
            .catch(e => e)
    ).data.entitlements_token;

    return {access_token, entitlements_token, error: false};
};
