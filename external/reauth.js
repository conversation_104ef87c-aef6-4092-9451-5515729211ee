import {Agent} from 'https';
import {createInterface} from 'readline';
import axios from 'axios';
import {MongoClient} from 'mongodb';
import fs from 'fs';
import {HttpsProxyAgent} from 'https-proxy-agent';
import {getCiphers} from 'tls';

const basedata = JSON.parse(fs.readFileSync('./basedata.json'));
const mongodb = new MongoClient(basedata.mongodb);

// login credentials
const USERNAME = 'AudiLover99';
const PASSWORD = 'xvfKAM*s@s#k3u2b@XgtrT4f';

let tokens = new Object();
// built headers
let headers = new Object();

let ssidCookie = new String();
let clientVersion = new String();
let region = new String();
let clientBuild = new String();

// standard input
let _stdin;
// initializes it if it's undefined
const stdin = () => {
    if (typeof _stdin === 'undefined')
        _stdin = createInterface({
            input: process.stdin,
            output: process.stdout,
        });
    return _stdin;
};

// custom cipher suites for bypassing cloudflare

// custom signature algorithms is a better alternative but axios
// does not expose https options, it can still be achieved with
// custom adapter but it's overkill for this script
const ciphers = ['TLS_AES_128_CCM_SHA256', 'TLS_CHACHA20_POLY1305_SHA256', 'TLS_AES_128_GCM_SHA256', 'TLS_AES_256_GCM_SHA384', 'TLS_AES_128_CCM_8_SHA256'];
// agent with custom cipher suites
const agent = new Agent({
    ciphers: getCiphers().filter(cipher => ciphers.includes("tls_")),
    honorCipherOrder: true,
    minVersion: 'TLSv1.3',
});
console.log(agent);
const httpsAgent = new HttpsProxyAgent('***********************************************');

const getDB = (col, db = 'INGAME-API') => {
    return mongodb.db(db).collection(col);
};

// 2fa logic -> fetch from standard input aka console
const input2faCode = login =>
    new Promise(resolve => stdin().question(`You have 2fa enabled, please input the 2fa code (sent to ${login.multifactor.email}):`, code => resolve(code)));

// parses the response url and returns the values we want
const parseUrl = uri => {
    const loginResponseURI = new URL(uri);
    const accessToken = loginResponseURI.searchParams.get('access_token');
    const idToken = loginResponseURI.searchParams.get('id_token');
    const expiresIn = parseInt(loginResponseURI.searchParams.get('expires_in'));

    return {accessToken, idToken, expiresIn};
};

// this object -> to json -> to base64 is the
// X-Riot-ClientPlatform header
const clientPlatform = {
    platformType: 'PC',
    platformOS: 'Windows',
    platformOSVersion: '10.0.19043.1.256.64bit',
    platformChipset: 'Unknown',
};

// builds headers used by pvp endpoints
const makeHeaders = () => {
    headers = {
        Authorization: `Bearer ${tokens.accessToken}`,
        'X-Riot-Entitlements-JWT': tokens.entitlementsToken,
        'X-Riot-ClientVersion': clientVersion,
        'X-Riot-ClientPlatform': Buffer.from(JSON.stringify(clientPlatform)).toString('base64'),
    };
};

let user_agent = `RiotClient/${clientBuild}`;

// this is the first request in authflow and the endpoint
// which can be used for reauth
const createSession = ssidCookie =>
    axios({
        url: 'https://auth.riotgames.com/api/v1/authorization',
        method: 'POST',
        headers: {
            ...(typeof ssidCookie === 'undefined' ? '' : {Cookie: ssidCookie}),
            'User-Agent': user_agent,
        },
        proxy: {
            host: 'p.webshare.io',
            port: 80,
            auth: {
                username: 'dmczebuk-FI-DE-GB-SE-AT-rotate',
                password: '1budhi56ns0m',
            },
            protocol: 'http',
        },
        data: {
            acr_values: '3',
            claims: '',
            client_id: 'riot-client',
            nonce: '*****************',
            redirect_uri: 'http://localhost/redirect',
            response_type: 'token id_token',
            // response params are returned as a query instead of hash
            // URL class can properly parse params this way
            //response_mode: 'query',
            // this gives us a bigger response on /userinfo + required
            // for auto detecting region
            scope: 'account openid offline_access link id offline',
        },
        httpsAgent: agent,
    }).catch(e => e);

// either returns access token or sends out a 2fa email
const login = (cookie, username, password) =>
    axios({
        url: 'https://auth.riotgames.com/api/v1/authorization',
        method: 'PUT',
        headers: {
            Cookie: cookie,
            'User-Agent': user_agent,
        },
        data: {
            type: 'auth',
            username,
            password,
        },
        proxy: {
            host: 'p.webshare.io',
            port: 80,
            auth: {
                username: 'dmczebuk-FI-DE-GB-SE-AT-rotate',
                password: '1budhi56ns0m',
            },
            protocol: 'http',
        },
    });

// if 2fa is enabled, we can send the code this way
const send2faCode = (cookie, code, rememberDevice = true) =>
    axios({
        url: 'https://auth.riotgames.com/api/v1/authorization',
        method: 'PUT',
        headers: {
            Cookie: cookie,
            'User-Agent': user_agent,
        },
        data: {
            type: 'multifactor',
            code,
            rememberDevice,
        },
        proxy: {
            host: 'p.webshare.io',
            port: 80,
            auth: {
                username: 'dmczebuk-FI-DE-GB-SE-AT-rotate',
                password: '1budhi56ns0m',
            },
            protocol: 'http',
        },
        httpsAgent: agent,
    });

const fetchEntitlements = accessToken =>
    axios({
        url: 'https://entitlements.auth.riotgames.com/api/token/v1',
        method: 'POST',
        headers: {
            Authorization: `Bearer ${accessToken}`,
        },
        data: {},
        httpsAgent: agent,
        proxy: {
            host: 'p.webshare.io',
            port: 80,
            auth: {
                username: 'dmczebuk-FI-DE-GB-SE-AT-rotate',
                password: '1budhi56ns0m',
            },
            protocol: 'http',
        },
    });

// pas token can be used for getting the account
// region automatically
const fetchPas = (accessToken, idToken) =>
    axios({
        url: 'https://riot-geo.pas.si.riotgames.com/pas/v1/product/valorant',
        method: 'PUT',
        headers: {
            Authorization: `Bearer ${accessToken}`,
        },
        data: {
            id_token: idToken,
        },
        httpsAgent: agent,
        proxy: {
            host: 'p.webshare.io',
            port: 80,
            auth: {
                username: 'dmczebuk-FI-DE-GB-SE-AT-rotate',
                password: '1budhi56ns0m',
            },
            protocol: 'http',
        },
    });

// the easiest way of getting the current client version
// apparently higher uptime than official api
const fetchValorantVersion = async () => {
    const val_fetch = await axios({
        url: 'https://valorant-api.com/v1/version',
        method: 'GET',
    });
    clientVersion = val_fetch.data.data.riotClientVersion;
    clientBuild = val_fetch.data.data.riotClientBuild;
    user_agent = `RiotClient/${clientBuild} rso-authenticator (Windows;10;;Professional, x64) xyz`;
};
// actual reauth part
const setupReauth = async () => {
    // access token -> every 1h | id token -> every 24h
    console.log('setup reauth', new Date());
    setInterval(async () => {
        try {
            await fetchValorantVersion();
            let response = await createSession(ssidCookie);
            let cycles = 0;
            while (!response.data) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                console.log('retrying ', cycles);
                console.log(response.response?.status ?? response.status ?? 'no status');
                response = await createSession(ssidCookie).catch(e => e);
                cycles++;
            }

            ssidCookie = response.headers['set-cookie'].find(elem => /^ssid/.test(elem));

            if (!response.data?.response?.parameters?.uri) return await createSession();
            tokens = {...tokens, ...parseUrl(response.data.response.parameters.uri)};

            const entitlement = (await fetchEntitlements(tokens.accessToken)).data.entitlements_token;
            await getDB('auth', 'API').updateOne(
                {source: 'val'},
                {
                    $set: {
                        access: tokens.accessToken,
                        entitlement: entitlement,
                    },
                },
                {upsert: true}
            );
            makeHeaders();
        } catch (err) {
            console.trace(err);
        }
        // reauth 5 min early as then there is no downtime
    }, (tokens.expiresIn - 300) * 1000);
};

class ValReauthScriptError extends Error {
    data;

    constructor(message, data) {
        super(message);
        this.data = data;
    }
}

(async function () {
    await mongodb.connect();
    try {
        await fetchValorantVersion();
        let session = await createSession();
        let cycles = 0;
        let auth = false;
        while (!Object.keys(tokens).length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('[AUTH] retrying', cycles);
            session = await createSession().catch(e => e);
            if (!session.data) {
                console.log('[SESSION] Error: ', session.response?.status ?? session.status ?? 'no status');
                cycles++;
                continue;
            }
            console.log(session);
            let asidCookie = session.headers['set-cookie'].find(cookie => /^asid/.test(cookie));

            // attempt to exchange username and password for an access token
            let loginResponse = await login(asidCookie, USERNAME, PASSWORD).catch(e => e);
            if (!loginResponse.data) {
                console.log('[LOGIN] Error: ', loginResponse.response?.status ?? loginResponse.status ?? 'no status');
                cycles++;
                continue;
            }

            // auth failed - most likely incorrect login info
            if (typeof loginResponse.data.error !== 'undefined') {
                console.dir(loginResponse.data);
                if (loginResponse.data.error === 'auth_failure') throw new ValReauthScriptError('invalid login credentials');
                throw new ValReauthScriptError('unknown error', loginResponse.data);
            }

            asidCookie = loginResponse.headers['set-cookie'].find(cookie => /^asid/.test(cookie));

            let response;

            // check if 2fa is enabled
            if (loginResponse.data.type === 'response') response = loginResponse;

            // if it is ask for code
            while (typeof response === 'undefined') {
                const inputCode = await input2faCode(loginResponse.data);
                const response2fa = await send2faCode(asidCookie, inputCode).catch(err => {
                    if (typeof err.response.data === 'undefined') throw new ValReauthScriptError('unknown error', err.response);
                    if (err.response.data.error === 'rate_limited') throw new ValReauthScriptError('too many 2fa requests');
                    throw new ValReauthScriptError('unknown error', err.response.data);
                });

                asidCookie = response2fa.headers['set-cookie'].find(cookie => /^asid/.test(cookie));

                if (response2fa.data.type === 'response') {
                    response = response2fa;
                    break;
                }
                // check response
                if (typeof response2fa.data.error !== 'undefined') {
                    if (response2fa.data.error === 'multifactor_attempt_failed') continue;
                    if (response2fa.data.error === 'rate_limited') throw new ValReauthScriptError('too many 2fa requests');
                    throw new ValReauthScriptError('unknown error', response2fa.data);
                }
            }

            // close handle
            // extract ssid cookie
            ssidCookie = response.headers['set-cookie'].find(cookie => /^ssid/.test(cookie));

            // extract tokens from the url
            tokens = parseUrl(response.data.response.parameters.uri);

            let entitlement = await fetchEntitlements(tokens.accessToken).catch(e => e);
            while (!entitlement.data) {
                console.log('[ENTITLEMENT] Error: ', entitlement.response?.status ?? entitlement.status ?? 'no status');
                console.log('[ENTITLEMENT] retrying ', cycles);
                cycles++;
                entitlement = await fetchEntitlements(tokens.accessToken).catch(e => e)
            }
        }

        await getDB('auth', 'API').updateOne(
            {source: 'val'},
            {
                $set: {
                    access: tokens.accessToken,
                    entitlement: tokens.entitlementsToken,
                },
            },
            {upsert: true}
        );

        // parse access token and extract puuid
        const puuid = JSON.parse(Buffer.from(tokens.accessToken.split('.')[1], 'base64').toString()).sub;

        // fetch pas token - not required, instead we only want the region
        // since we already fetched it let's save it, because why not
        const pasTokenResponse = await fetchPas(tokens.accessToken, tokens.idToken);
        tokens.pasToken = pasTokenResponse.data.token;

        region = pasTokenResponse.data.affinities.live;

        makeHeaders();
        setupReauth();

        console.log({...headers, puuid, region});
    } catch (err) {
        // close handle
        throw err;
    }
})();
