use std::fmt::format;
use reqwest::Client;
use std::net::{Ip<PERSON>dd<PERSON>, SocketAddr, SocketAddrV6};
use std::str::FromStr;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let riot_bearer = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    let riot_clientplatform = "ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9";
    let riot_entitlements_jwt = "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
    let mut ip_adresses: Vec<i32> = vec![
        2
    ];

    let addr = SocketAddrV6::new("2606:4700:4400::6812:22D2".parse().unwrap(), 443, 0, 0);
    let addr = SocketAddr::V6(addr);


    loop {
        for i_ip in ip_adresses.clone().into_iter() {
            let ip = format!("2a01:4f8:1c17:607b::{}", i_ip);
            let ipv6_address: IpAddr = IpAddr::from_str(&ip)?;
            let client = Client::builder()
                .resolve("pd.eu.a.pvp.net", addr)
                .local_address(ipv6_address)
                .build()?;
            let response = client.get("https://pd.eu.a.pvp.net/mmr/v1/players/54942ced-1967-5f66-8a16-1e0dae875641")
                .header("Authorization", format!("Bearer {}", riot_bearer))
                .header("X-Riot-ClientPlatform", riot_clientplatform)
                .header("X-Riot-ClientVersion", "release-08.08-shipping-4-2493420")
                .header("X-Riot-Entitlements-JWT", riot_entitlements_jwt).send().await?;
            if response.status().as_u16() == 200 {
                let body = response.text().await?;
                println!("[{}][200]", ip);
            } else if response.status().as_u16() == 429 || response.status().as_u16() == 1015 {
                println!("[{}][429]", ip);
                if i_ip - 1 == ip_adresses.len() as i32 && i_ip + 1 < 6 {
                    ip_adresses.push((i_ip + 1) as i32);
                }
            }
        }
        tokio::time::sleep(std::time::Duration::from_millis(250)).await;
    }
    Ok(())
}
