// Function to generate a random IPv6 segment (four hexadecimal digits)
function randomIPv6Segment() {
	return Math.floor(Math.random() * 65536).toString(16);
}

// Function to generate a random IPv6 address within the specified subnet
function generateRandomIPv6Address(baseSubnet) {
	return `${baseSubnet}${randomIPv6Segment()}:${randomIPv6Segment()}:${randomIPv6Segment()}:${randomIPv6Segment()}`;
}

// Function to generate configurations for a specified number of interfaces
function generateInterfaces(baseSubnet, totalInterfaces) {
	let configurations = [];
	for (let i = 1; i <= totalInterfaces; i++) {
		const address = generateRandomIPv6Address(baseSubnet.slice(0, -1)); // Remove the last character ":" for proper formatting
		const config = `# Virtual Interface ${i}
auto eth0:${i}
iface eth0:${i} inet6 static
    address ${address}/64

`;
		configurations.push(config);
	}
	return configurations;
}

// Generate X interfaces from the specified subnet
const subnet = '2a01:4f8:1c17:607b::';
const totalInterfaces = 750;
const interfaces = generateInterfaces(subnet, totalInterfaces);

// Print out all interface configurations
console.log(interfaces.join(''));
require('fs').writeFileSync('interfaces', interfaces.join(''));
