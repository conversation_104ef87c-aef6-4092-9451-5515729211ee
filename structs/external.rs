use serde::{ Deserialize, Serialize };

#[derive(<PERSON>erialize, <PERSON><PERSON><PERSON>, Debug, <PERSON><PERSON>)]
pub struct Proxys {
	pub count: i32,
	pub next: Option<String>,
	pub previous: Option<String>,
	pub results: Vec<Proxy>,
}

#[derive(Deserialize, Serialize, Debug, <PERSON>lone)]
pub struct Proxy {
	pub id: String,
	pub username: String,
	pub password: String,
	pub proxy_address: String,
	pub port: i32,
	pub valid: bool,
	pub last_verification: String,
	pub country_code: String,
	pub city_name: String,
	pub asn_name: String,
	pub asn_number: i32,
	pub high_country_confidence: bool,
	pub created_at: String,
}

#[derive(Deserialize, Serialize, Debug, <PERSON>lone)]
#[serde(rename_all = "camelCase")]
pub struct B2Auth {
	pub authorization_token: String,
	pub api_info: B2ApiInfo,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct B2ApiInfo {
	pub storage_api: B2ApiStorageApi,
}

#[derive(Deserialize, Serial<PERSON>, Debu<PERSON>, <PERSON><PERSON>)]
#[serde(rename_all = "camelCase")]
pub struct B2ApiStorageApi {
	pub api_url: String,
	pub s3_api_url: String,
	pub bucket_id: String,
	pub bucket_name: String,
}
