use crate::structs::pvp_api::{ PremierConference, PremierSeason };
use mongodb::bson::DateTime;
use serde::{ de, Deserialize, Deserializer, Serialize };
use serde_json::Value;
use std::collections::HashMap;
use uuid::Uuid;
use valorant_api::response_types::leaderboard_v1::LeaderboardTierDetail;
use valorant_api::response_types::matchdetails_v1::{ MatchInfo, MatchTeam, Player, PlayerRoundDamage };

pub fn c_deserialize<'de, D>(deserializer: D) -> Result<DateTime, D::Error> where D: Deserializer<'de> {
	let value = Value::deserialize(deserializer)?;
	match value {
		Value::String(s) => {
			let date = DateTime::parse_rfc3339_str(&s).map_err(|_| { de::Error::custom(format!("cannot parse RFC 3339 datetime from \"{}\"", s)) })?;
			Ok(date)
		}
		Value::Object(map) => {
			// Assuming the map has a specific key we are interested in, like "username"
			if let Some(Value::Object(date)) = map.get("$date") {
				if let Some(Value::String(number_long)) = date.get("$numberLong") {
					let date = DateTime::from_millis(number_long.parse::<i64>().unwrap());
					Ok(date)
				} else {
					Err(serde::de::Error::custom("Expected object to have a '$numberLong' key"))
				}
			} else {
				Err(serde::de::Error::custom("Expected object to have a '$date' key"))
			}
		}
		_ => Err(serde::de::Error::custom("Expected string or object")),
	}
}

//API Tokens
#[derive(Deserialize, Serialize, Debug)]
pub struct APITokens {
	pub userid: String,
	pub token: String,
	pub name: String,
	pub details: String,
	pub info: String,
	pub limit: f32,
	#[serde(rename = "type")]
	pub type_: String,
	pub admin: bool,
}

//Matches
#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct MatchesStoredProjection {
	pub match_info: MatchInfo,
	pub players: Vec<Player>,
	pub teams: Option<Vec<MatchTeam>>,
	pub round_results: Option<Vec<RoundResultLite>>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct RoundResultLite {
	pub player_stats: Vec<PlayerRoundStatsLite>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PlayerRoundStatsLite {
	pub subject: Uuid,
	pub damage: Vec<PlayerRoundDamage>,
}

//Leaderboard
#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct LeaderboardPlayerDB {
	pub player: LeaderboardPlayerInDB,
	pub affinity: String,
	pub season: String,
	pub updated_at: DateTime,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Hash, Clone)]
#[serde(rename_all = "camelCase")]
pub struct LeaderboardPlayerInDB {
	#[serde(rename = "PlayerCardID")]
	pub player_card_id: String,
	#[serde(rename = "TitleID")]
	pub title_id: String,
	#[serde(rename = "IsBanned")]
	pub is_banned: bool,
	#[serde(rename = "IsAnonymized")]
	pub is_anonymized: bool,
	pub puuid: Option<String>,
	pub game_name: String,
	pub tag_line: String,
	pub leaderboard_rank: u32,
	pub ranked_rating: u32,
	pub number_of_wins: u32,
	pub competitive_tier: u32,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct LeaderboardMetadataDB {
	pub thresholds: HashMap<String, LeaderboardTierDetail>,
	pub affinity: String,
	pub season: String,
	pub updated_at: DateTime,
}

//Premier Team
#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamDB {
	pub id: String,
	pub name: String,
	pub tag: String,
	pub season: String,
	pub region: String,
	pub affinity: String,
	pub anonymous: bool,
	pub conference: String,
	pub customization: PremierTeamCustomization,
	pub division: i32,
	pub games: PremierTeamGames,
	pub stats: PremierTeamStats,
	pub member: Vec<PremierTeamMember>,
	//#[serde(serialize_with = "serialize_bson_datetime_as_rfc3339_string")]
	#[serde(deserialize_with = "c_deserialize")]
	pub updated_at: DateTime,
	#[serde(deserialize_with = "c_deserialize")]
	pub last_played: DateTime,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamMember {
	pub puuid: String,
	pub name: Option<String>,
	pub tag: Option<String>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamStats {
	pub wins: i32,
	pub losses: i32,
	pub rounds_won: i32,
	pub rounds_lost: i32,
	pub ranking: i32,
	pub score: i32,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamCustomization {
	pub icon: String,
	pub primary: String,
	pub secondary: String,
	pub tertiary: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamGames {
	pub league: Vec<PremierTeamGamesLeague>,
	pub tournament: Vec<PremierTeamGamesTournament>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamGamesLeague {
	pub id: String,
	pub points_before: i32,
	pub points_after: i32,
	#[serde(deserialize_with = "c_deserialize")]
	pub started_at: DateTime,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamGamesLeagueString {
	pub id: String,
	pub points_before: i32,
	pub points_after: i32,
	pub started_at: String,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierTeamGamesTournament {
	pub tournament_id: String,
	pub placement: i32,
	pub placement_league_bonus: i32,
	pub points_before: i32,
	pub points_after: i32,
	pub matches: Vec<String>,
}

//Premier Seasons
#[derive(Deserialize, Serialize, Debug)]
pub struct PremierSeasonDB {
	pub id: String,
	pub affinity: String,
	pub region: String,
	pub season: PremierSeason,
}

//Premier Conferences
#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct PremierConferencesDB {
	pub id: String,
	pub season: String,
	pub affinity: String,
	pub region: String,
	pub conference: PremierConference,
}

//Auth
#[derive(Deserialize, Serialize, Debug)]
pub struct Auth {
	pub access: String,
	pub entitlement: String,
	pub source: String,
}

//Versions
#[derive(Deserialize, Serialize, Debug)]
pub struct Versions {
	pub region: String,
	pub branch: String,
	pub build_date: String,
	pub build_ver: String,
	pub last_checked: String,
	pub patch_url: String,
	pub version: u32,
	pub version_for_api: String,
}

//Website
#[derive(Deserialize, Serialize, Debug)]
pub struct WebsiteTags {
	pub name: String,
	pub tech_name: String,
}

#[derive(Deserialize, Serialize, Debug)]
#[allow(non_snake_case)]
pub struct Website {
	pub id: String,
	pub content_stack_id: String,
	pub title: String,
	pub date: DateTime,
	pub article_type: String,
	pub external_link: Option<String>,
	pub tags: Vec<WebsiteTags>,
	pub category: Vec<WebsiteTags>,
	pub banner: Option<String>,
	pub path: String,
	pub language: String,
}

//Account
#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct Account {
	pub puuid: String,
	pub name: String,
	pub tag: String,
	pub region: String,
	pub card: String,
	pub title: String,
	pub account_level: i32,
	pub platforms: Vec<String>,
	pub created_at: DateTime,
	pub updated_at: DateTime,
	pub last_match: DateTime,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct LifetimeMMRHistory {
	pub m_id: String,
	pub p_id: String,
	pub t: i32,
	pub map_id: String,
	pub s_id: String,
	pub r: i32,
	pub m_c: i32,
	pub d: i32,
	pub p: Option<String>,
	pub r_a: Option<i32>,
	pub r_p: Option<i32>,
	pub w_d_p: Option<bool>,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct B2Match {
	pub m_id: String,
	pub meta: B2MatchMeta,
	pub players: Vec<B2MatchPlayer>,
	pub teams: Vec<B2MatchTeam>,
	pub b2_error: bool,
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct B2MatchMeta {
	pub map_id: String, //map id
	pub pod_id: String, //pod id
	pub v: String, //version
	pub l_ms: u64, //length ms
	pub s_ms: u64, //start ms
	pub q: String, //queue id
	pub g: String, //game mode
	pub s_id: String, //season id
	pub p: String, // platform
	pub p_s: Option<String>, //premier season
	pub p_e: Option<String>, //premier event
	pub p_t: Option<String>, //premier tournament
	pub p_m: Option<String>, //premier matchup
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct B2MatchPlayer {
	pub id: String,
	pub t: String, //team
	pub c: String, //character
	pub p: String, //party
	pub s: i32, //score
	pub k: u32, //kills
	pub d: u32, //deaths
	pub a: u32, //assists
	pub head: u32, //headshots
	pub body: u32, //body shots
	pub leg: u32, //leg shots
	pub a_g: u32, //ability granade
	pub a_1: u32, //ability 1
	pub a_2: u32, //ability 2
	pub a_u: u32, //ability ultimate
	pub tier: i32, //competitive tier
	pub l: u32, //level
	pub d_d: i32, //damage dealt
	pub d_r: i32, //damage received
}

#[derive(Deserialize, Serialize, Debug, Clone)]
pub struct B2MatchTeam {
	pub id: String,
	pub p: u8, //points
	pub r_id: Option<String>, // roster id
}
