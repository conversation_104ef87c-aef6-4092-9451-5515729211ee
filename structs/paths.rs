use std::collections::HashMap;
use serde::{Deserialize, Deserializer, Serialize};

fn deserialize_multiple_leagues<'de, D>(deserializer: D) -> Result<Option<Vec<String>>, D::Error>
where
    D: Deserializer<'de>,
{
    let map: HashMap<String, Vec<String>> = HashMap::deserialize(deserializer)?;
    if let Some(leagues) = map.get("league") {
        return Ok(Some(leagues.clone()));
    }
    Ok(None)
}

#[derive(Deserialize, Serialize, Debug)]
pub struct AccountV2Query {
    pub force: Option<bool>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct AccountV2ByIDPath {
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct AccountV2Path {
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct AccountV1Query {
    pub force: Option<bool>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct AccountV1ByIDPath {
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct AccountV1Path {
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct ContentQuery {
    pub locale: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct CrosshairQuery {
    pub id: Option<String>,
    pub size: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct EsportsScheduleQuery {
    pub region: Option<String>,
    pub league: Option<String>,
}


#[derive(Deserialize, Serialize, Debug)]
pub struct LeaderboardV3Query {
    pub page: Option<String>,
    pub size: Option<String>,
    pub season_short: Option<String>,
    pub season_id: Option<String>,
    pub name: Option<String>,
    pub tag: Option<String>,
    pub puuid: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct LeaderboardV3Path {
    pub platform: String,
    pub affinity: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct LeaderboardV2Query {
    pub season: Option<String>,
    pub name: Option<String>,
    pub tag: Option<String>,
    pub puuid: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct LeaderboardV2Path {
    pub affinity: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct LeaderboardV1Query {
    pub season: Option<String>,
    pub name: Option<String>,
    pub tag: Option<String>,
    pub puuid: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct LeaderboardV1Path {
    pub affinity: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryPath {
    pub affinity: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryV2Path {
    pub affinity: String,
    pub platform: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryQuery {
    pub size: Option<String>,
    pub page: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryV2Query {
    pub size: Option<String>,
    pub page: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryByIDPath {
    pub affinity: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryV2ByIDPath {
    pub affinity: String,
    pub platform: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryByIDQuery {
    pub size: Option<String>,
    pub page: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMMRHistoryV2ByIDQuery {
    pub size: Option<String>,
    pub page: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMatchesPath {
    pub affinity: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMatchesByIDPath {
    pub affinity: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoredMatchesQuery {
    pub mode: Option<String>,
    pub map: Option<String>,
    pub size: Option<String>,
    pub page: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MatchesV4Query {
    pub mode: Option<String>,
    pub map: Option<String>,
    pub size: Option<String>,
    pub start: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MatchesV4Path {
    pub affinity: String,
    pub platform: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MatchesV4ByIDPath {
    pub affinity: String,
    pub platform: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MatchesV3Query {
    pub mode: Option<String>,
    pub map: Option<String>,
    pub size: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MatchesV3Path {
    pub affinity: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MatchesV3ByIDPath {
    pub affinity: String,
    pub puuid: String,
}


#[derive(Deserialize, Serialize, Debug)]
pub struct MatchV2Path {
    pub match_id: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MatchV4Path {
    pub affinity: String,
    pub match_id: String,
}


#[derive(Deserialize, Serialize, Debug)]
pub struct AdminV1Jobs {
    #[serde(rename = "type")]
    pub type_: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRV3Path {
    pub affinity: String,
    pub platform: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRV3ByIDPath {
    pub affinity: String,
    pub platform: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRV1Path {
    pub affinity: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRV1ByIDPath {
    pub affinity: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRHistoryV1Path {
    pub affinity: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRHistoryV1ByIDPath {
    pub affinity: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRHistoryV2Path {
    pub affinity: String,
    pub platform: String,
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct MMRHistoryV2ByIDPath {
    pub affinity: String,
    pub platform: String,
    pub puuid: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct PremierLeaderboardPath {
    pub affinity: String,
    pub conference: Option<String>,
    pub division: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct PremierSeasonPath {
    pub affinity: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct PremierSearchQuery {
    pub name: Option<String>,
    pub tag: Option<String>,
    pub division: Option<String>,
    pub conference: Option<String>,
    pub id: Option<String>,
}


#[derive(Deserialize, Serialize, Debug)]
pub struct PremierByNamePath {
    pub name: String,
    pub tag: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct PremierByIDPath {
    pub id: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StatusPath {
    pub affinity: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct StoreFeaturedPath {
    pub version: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct VersionPath {
    pub affinity: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct WebsitePath {
    pub country_code: String,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct WebsiteQuery {
    pub category: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct McDachQuery {
    pub name: Option<String>,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct ValorantLabsMatchesPath {
    pub affinity: String,
    pub puuid: String,
}