use crate::structs::database::c_deserialize;
use mongodb::bson::DateTime;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPISeasons {
    pub status: u32,
    pub data: Vec<ValorantAPISeasonsData>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPISeasonsData {
    pub uuid: String,
    pub displayName: String,
    #[serde(rename = "type")]
    pub type_: Option<String>,
    #[serde(deserialize_with = "c_deserialize")]
    pub startTime: DateTime,
    #[serde(deserialize_with = "c_deserialize")]
    pub endTime: DateTime,
    pub parentUuid: Option<String>,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIGamePods {
    pub status: u32,
    pub data: ValorantAPIGamePodsData,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIGamePodsData {
    pub UI_GamePodStrings: HashMap<String, String>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIGamemodes {
    pub status: u32,
    pub data: Vec<ValorantAPIGamemodesData>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIGamemodesData {
    pub uuid: String,
    pub displayName: String,
    pub duration: Option<String>,
    pub economyType: Option<String>,
    pub allowsMatchTimeouts: bool,
    pub isTeamVoiceAllowed: bool,
    pub isMinimapHidden: bool,
    pub orbCount: i32,
    pub roundsPerHalf: i32,
    // Assuming -1 is used as a placeholder for unspecified or infinite
    pub teamRoles: Option<Vec<String>>,
    pub gameFeatureOverrides: Option<Vec<GameFeatureOverride>>,
    pub gameRuleBoolOverrides: Option<Vec<GameRuleBoolOverride>>,
    pub displayIcon: Option<String>,
    pub listViewIconTall: Option<String>,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct GameFeatureOverride {
    pub featureName: String,
    pub state: bool,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct GameRuleBoolOverride {
    pub ruleName: String,
    pub state: bool,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIGamemodeQueues {
    pub status: u32,
    pub data: Vec<ValorantAPIGamemodeQueuesData>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIGamemodeQueuesData {
    pub uuid: String,
    pub queueId: String,
    pub displayName: Option<String>,
    pub description: Option<String>,
    pub dropdownText: String,
    pub selectedText: String,
    pub isBeta: bool,
    pub displayIcon: Option<String>,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIVersionData {
    pub manifestId: String,
    pub branch: String,
    pub version: String,
    pub buildVersion: String,
    pub engineVersion: String,
    pub riotClientVersion: String,
    pub riotClientBuild: String,
    pub buildDate: DateTime,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIVersion {
    pub status: u32,
    pub data: ValorantAPIVersionData,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct AgentRole {
    pub uuid: String,
    pub displayName: String,
    pub description: String,
    pub displayIcon: String,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct AgentAbility {
    pub slot: String,
    pub displayName: String,
    pub description: String,
    pub displayIcon: Option<String>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIAgent {
    pub uuid: String,
    pub displayName: String,
    pub description: String,
    pub developerName: String,
    pub characterTags: Option<Vec<String>>,
    pub displayIcon: String,
    pub displayIconSmall: Option<String>,
    pub bustPortrait: String,
    pub fullPortrait: String,
    pub fullPortraitV2: String,
    pub killfeedPortrait: String,
    pub background: String,
    pub backgroundGradientColors: Vec<String>,
    pub assetPath: String,
    pub isFullPortraitRightFacing: bool,
    pub isPlayableCharacter: bool,
    pub isAvailableForTest: bool,
    pub isBaseContent: bool,
    pub role: AgentRole,
    pub recruitmentData: Option<serde_json::Value>,
    pub abilities: Vec<AgentAbility>,
    pub voiceLine: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIAgents {
    pub status: u32,
    pub data: Vec<ValorantAPIAgent>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIMap {
    pub uuid: String,
    pub displayName: String,
    pub narrativeDescription: Option<String>,
    pub tacticalDescription: Option<String>,
    pub coordinates: Option<String>,
    pub displayIcon: Option<String>,
    pub listViewIcon: String,
    pub splash: String,
    pub assetPath: String,
    pub mapUrl: String,
    pub xMultiplier: f64,
    pub yMultiplier: f64,
    pub xScalarToAdd: f64,
    pub yScalarToAdd: f64,
    pub callouts: Option<Vec<ValorantAPIMapCallout>>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIMapCallout {
    pub regionName: String,
    pub superRegionName: String,
    pub location: ValorantAPIMapLocation,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ValorantAPIMapLocation {
    pub x: f64,
    pub y: f64,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIMaps {
    pub status: u32,
    pub data: Vec<ValorantAPIMap>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIWeaponSkins {
    pub status: u32,
    pub data: Vec<ValorantAPIWeaponSkin>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIWeaponSkin {
    pub uuid: String,
    pub displayName: String,
    pub themeUuid: String,
    pub contentTierUuid: Option<String>,
    pub displayIcon: Option<String>,
    pub wallpaper: Option<String>,
    pub assetPath: String,
    pub chromas: Vec<ValorantAPIWeaponChroma>,
    pub levels: Vec<ValorantAPIWeaponLevel>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIWeaponChroma {
    pub uuid: String,
    pub displayName: String,
    pub displayIcon: Option<String>,
    pub fullRender: String,
    pub swatch: Option<String>,
    pub streamedVideo: Option<String>,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIWeaponLevel {
    pub uuid: String,
    pub displayName: String,
    pub levelItem: Option<String>,
    pub displayIcon: Option<String>,
    pub streamedVideo: Option<String>,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIBuddys {
    pub status: u32,
    pub data: Vec<ValorantAPIBuddy>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIBuddy {
    pub uuid: String,
    pub displayName: String,
    pub isHiddenIfNotOwned: bool,
    pub themeUuid: Option<String>,
    pub displayIcon: String,
    pub assetPath: String,
    pub levels: Vec<ValorantAPIBuddyLevel>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIBuddyLevel {
    pub uuid: String,
    pub charmLevel: i32,
    pub hideIfNotOwned: bool,
    pub displayName: String,
    pub displayIcon: String,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPISprays {
    pub status: u32,
    pub data: Vec<ValorantAPISpray>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPISpray {
    pub uuid: String,
    pub displayName: String,
    pub category: Option<String>,
    pub themeUuid: Option<String>,
    pub isNullSpray: bool,
    pub hideIfNotOwned: bool,
    pub displayIcon: String,
    pub fullIcon: Option<String>,
    pub fullTransparentIcon: Option<String>,
    pub animationPng: Option<String>,
    pub animationGif: Option<String>,
    pub assetPath: String,
    pub levels: Vec<ValorantAPISprayLevel>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPISprayLevel {
    pub uuid: String,
    pub sprayLevel: i32,
    pub displayName: String,
    pub displayIcon: Option<String>,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIPlayerCards {
    pub status: u32,
    pub data: Vec<ValorantAPIPlayerCard>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIPlayerCard {
    pub uuid: String,
    pub displayName: String,
    pub isHiddenIfNotOwned: bool,
    pub themeUuid: Option<String>,
    pub displayIcon: String,
    pub smallArt: String,
    pub wideArt: String,
    pub largeArt: Option<String>,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIPlayerTitles {
    pub status: u32,
    pub data: Vec<ValorantAPIPlayerTitle>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIPlayerTitle {
    pub uuid: String,
    pub displayName: Option<String>,
    pub titleText: Option<String>,
    pub isHiddenIfNotOwned: bool,
    pub assetPath: String,
}

// Custom structs
#[derive(Debug, Deserialize, Clone)]
#[allow(non_snake_case)]
pub struct CustomValorantAPIWeaponLevel {
    pub uuid: String,
    pub displayName: String,
    pub levelItem: Option<String>,
    pub displayIcon: Option<String>,
    pub streamedVideo: Option<String>,
    pub assetPath: String,
    pub parent: String,
}

#[allow(non_snake_case)]
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ValorantAPIContentTier {
    pub uuid: String,
    pub displayName: String,
    pub devName: String,
    pub rank: i32,
    pub juiceValue: i32,
    pub juiceCost: i32,
    pub highlightColor: String,
    pub displayIcon: String,
    pub assetPath: String,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIContentTiers {
    pub status: u32,
    pub data: Vec<ValorantAPIContentTier>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIFlex {
    pub status: u32,
    pub data: Vec<ValorantAPIFlexItem>,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
#[allow(non_snake_case)]
pub struct ValorantAPIFlexItem {
    pub uuid: String,
    pub displayName: Option<String>,
    pub displayNameAllCaps: Option<String>,
    pub displayIcon: Option<String>,
    pub assetPath: String,
}
