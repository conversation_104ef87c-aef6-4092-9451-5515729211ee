use std::fmt::Display;

#[derive(Debug)]
pub enum RiotPlatforms {
    PC,
    XBOX,
    PLAYSTATION,
    CONSOLE,
}

impl Display for RiotPlatforms {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        match self {
            RiotPlatforms::PC => write!(f, "PC"),
            RiotPlatforms::XBOX => write!(f, "XBOX"),
            RiotPlatforms::PLAYSTATION => write!(f, "PLAYSTATION"),
            RiotPlatforms::CONSOLE => write!(f, "CONSOLE")
        }
    }
}

impl RiotPlatforms {
    pub fn from_str(s: &str) -> Option<RiotPlatforms> {
        match s.to_uppercase().as_str() {
            "PC" => Some(RiotPlatforms::PC),
            "XBOX" => Some(RiotPlatforms::XBOX),
            "PLAYSTATION" => Some(RiotPlatforms::PLAYSTATION),
            "CONSOLE" => Some(RiotPlatforms::CONSOLE),
            _ => None
        }
    }
}
