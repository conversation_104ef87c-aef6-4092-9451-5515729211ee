use crate::structs::database::{
	LeaderboardPlayerDB,
	LeaderboardPlayerInDB,
	PremierConferencesDB,
	PremierSeasonDB,
	PremierTeamCustomization,
	PremierTeamDB,
	PremierTeamGames,
	PremierTeamStats,
};
use crate::structs::enums::RiotPlatforms;
use crate::structs::helper::{ get_c_season, get_c_season_premier, get_premier_conferences, premier_extract_hex_color };
use crate::structs::http_clients::{ fetch_ipv6, FetchIPv6Options, FetchOptions };
use crate::structs::pvp_api::{ PVPPremierConferences, PVPPremierLeaderboard, PVPPremierSeasons, PremierLeaderboardRanking };
use crate::structs::website::{ RitoMobileNews, RitoMobileNewsElement };
use crate::{ build_riot_headers, fetch, get_db, AFFINITIES, PREMIER_URLS, VALORANT_WEBSITE_COUNTRIES };
use futures::future::join_all;
use futures::TryStreamExt;
use mongodb::bson::{ doc, DateTime };
use mongodb::bson::{ to_bson, to_document, Document };
use mongodb::Client;
use serde::{ Deserialize, Serialize };
use std::collections::HashMap;
use tokio::time::Instant;
use valorant_api::response_types::leaderboard_v1::LeaderboardV1;

pub async fn load_website(client: &Client) {
	let futures: Vec<_> = VALORANT_WEBSITE_COUNTRIES.iter()
		.map(|x| {
			fetch::<RitoMobileNews>(FetchOptions {
				url: format!("https://content.publishing.riotgames.com/publishing-content/v1.0/public/newsfeed?multigamePromoChannelId=riot_mobile_news_feeds&multigameContentGroupId=valorant&locale={}&from=1&to=100", x),
				user_agent: String::from(
					"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.102 Safari/537.36 Edg/104.0.1293.70"
				),
				..FetchOptions::default()
			})
		})
		.collect();
	let results: Vec<_> = futures
		.into_iter()
		.map(|f| tokio::spawn(f))
		.collect();
	for result in results {
		let value = result.await.unwrap();
		match value {
			Ok(v) => {
				let mut elements = v.data.data.items;
				let mut index = 1;
				let copy_url = v.url.clone();
				while index < v.data.metadata.total_pages {
					let from = index * 100 + 1;
					let to = index * 100 + 100;
					let url = copy_url.replace("from=1", format!("from={}", from).as_str()).replace("to=100", format!("to={}", to).as_str());
					let next_page = fetch::<RitoMobileNews>(FetchOptions {
						url,
						user_agent: String::from(
							"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.102 Safari/537.36 Edg/104.0.1293.70"
						),
						..FetchOptions::default()
					}).await;
					match next_page {
						Ok(v) => {
							elements.extend(v.data.data.items);
						}
						Err(e) => {
							eprintln!("[JOBS][WEBSITE] NextPage: {:?}", e);
						}
					}
					index += 1;
				}
				let db = get_db::<RitoMobileNewsElement>(client, "website", None);
				let all_ids = elements
					.iter()
					.map(|i| i.id.clone())
					.collect::<Vec<String>>();
				let all_ids_in_db = get_db::<IDsOnly>(client, "website", None)
					.find(doc! { "id": doc! {"$in": all_ids}, "cms.locale": v.data.metadata.locale.to_lowercase() })
					.projection(doc! { "id": 1 }).await;
				if all_ids_in_db.is_err() {
					eprintln!("[JOBS][WEBSITE] DB: {:?}", all_ids_in_db.unwrap_err());
					continue;
				}

				#[derive(Deserialize, Serialize, Debug, Clone)]
				struct IDsOnly {
					id: String,
				}

				let collected: Vec<IDsOnly> = all_ids_in_db.unwrap().try_collect().await.unwrap();
				let all_ids_not_in_db = elements
					.iter()
					.filter(|x| !collected.iter().any(|obj| obj.id == x.id))
					.collect::<Vec<_>>();

				for node in all_ids_not_in_db {
					let _ = db
						.update_one(
							doc! { "id": node.id.clone(), "cms.locale": v.data.metadata.locale.to_lowercase() },
							doc! { "$setOnInsert": to_bson(&node.clone()).unwrap() }
						)
						.upsert(true).await;
				}
			}
			Err(e) => {
				eprintln!("[JOBS][WEBSITE]: {:?}", e);
			}
		}
	}
}

pub async fn update_premier_metadata_database_job(client: Client) {
	loop {
		let before = Instant::now();
		println!("[JOBS][UPDATE_PREMIER_DATABASE] Running Job...");
		{
			update_premier_metadata_database(&client).await;
		}
		println!("[JOBS][UPDATE_PREMIER_DATABASE] Took {}ms", before.elapsed().as_millis());
		tokio::time::sleep(tokio::time::Duration::from_secs(60 * 60)).await;
	}
}

pub async fn update_premier_teams_database_job(client: Client) {
	tokio::time::sleep(tokio::time::Duration::from_secs(3 * 60 * 60)).await;
	loop {
		let before = Instant::now();
		println!("[JOBS][UPDATE_PREMIER_DATABASE_TEAMS] Running Job...");
		{
			update_premier_leaderboard(&client).await;
		}
		println!("[JOBS][UPDATE_PREMIER_DATABASE_TEAMS] Took {}ms", before.elapsed().as_millis());
		tokio::time::sleep(tokio::time::Duration::from_secs(3 * 60 * 60)).await;
	}
}

pub async fn update_player_leaderboard_job(client: Client) {
	tokio::time::sleep(tokio::time::Duration::from_secs(30 * 60)).await;
	loop {
		let before = Instant::now();
		println!("[JOBS][UPDATE_PLAYER_LEADERBOARD] Running Job...");
		{
			update_player_leaderboard(&client, get_c_season().await.to_string()).await;
		}
		println!("[JOBS][UPDATE_PLAYER_LEADERBOARD] Took {}ms", before.elapsed().as_millis());
		tokio::time::sleep(tokio::time::Duration::from_secs(30 * 60)).await;
	}
}

pub async fn update_premier_metadata_database(client: &Client) {
	let fetch_conferences = join_all(
		vec![
			fetch_ipv6::<PVPPremierConferences>(FetchIPv6Options {
				url: String::from("https://pd.eu.a.pvp.net/premier/v1/affinities/eu/conferences"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("eu")),
						(String::from("region"), String::from("eu")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierConferences>(FetchIPv6Options {
				url: String::from("https://pd.kr.a.pvp.net/premier/v1/affinities/kr/conferences"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("kr")),
						(String::from("region"), String::from("kr")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierConferences>(FetchIPv6Options {
				url: String::from("https://pd.ap.a.pvp.net/premier/v1/affinities/ap/conferences"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("ap")),
						(String::from("region"), String::from("ap")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierConferences>(FetchIPv6Options {
				url: String::from("https://pd.na.a.pvp.net/premier/v1/affinities/na/conferences"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("na")),
						(String::from("region"), String::from("na")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierConferences>(FetchIPv6Options {
				url: String::from("https://pd.na.a.pvp.net/premier/v1/affinities/latam/conferences"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("latam")),
						(String::from("region"), String::from("na")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierConferences>(FetchIPv6Options {
				url: String::from("https://pd.na.a.pvp.net/premier/v1/affinities/br/conferences"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("br")),
						(String::from("region"), String::from("na")),
					])
				),
				..FetchIPv6Options::default()
			})
		]
	).await;
	let fetch_seasons = join_all(
		vec![
			fetch_ipv6::<PVPPremierSeasons>(FetchIPv6Options {
				url: String::from("https://pd.kr.a.pvp.net/premier/v1/affinities/kr/premier-seasons"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("kr")),
						(String::from("region"), String::from("kr")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierSeasons>(FetchIPv6Options {
				url: String::from("https://pd.ap.a.pvp.net/premier/v1/affinities/ap/premier-seasons"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("ap")),
						(String::from("region"), String::from("ap")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierSeasons>(FetchIPv6Options {
				url: String::from("https://pd.eu.a.pvp.net/premier/v1/affinities/eu/premier-seasons"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("eu")),
						(String::from("region"), String::from("eu")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierSeasons>(FetchIPv6Options {
				url: String::from("https://pd.na.a.pvp.net/premier/v1/affinities/na/premier-seasons"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("na")),
						(String::from("region"), String::from("na")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierSeasons>(FetchIPv6Options {
				url: String::from("https://pd.na.a.pvp.net/premier/v1/affinities/latam/premier-seasons"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("latam")),
						(String::from("region"), String::from("na")),
					])
				),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPPremierSeasons>(FetchIPv6Options {
				url: String::from("https://pd.na.a.pvp.net/premier/v1/affinities/br/premier-seasons"),
				pass_data: Some(
					HashMap::from([
						(String::from("affinity"), String::from("br")),
						(String::from("region"), String::from("na")),
					])
				),
				..FetchIPv6Options::default()
			})
		]
	).await;
	let mut active_season = "".to_string();
	for season_fetch in fetch_seasons.into_iter() {
		match season_fetch {
			Ok(mut v) => {
				let pass_data = v.pass_data.unwrap();
				let affinity = pass_data.get("affinity").unwrap();
				let region = pass_data.get("region").unwrap();
				let mut seasons = v.data.PremierSeasons.unwrap_or_else(|| vec![]);
				let length = &seasons.len();
				if affinity == "eu" && length > &0 {
					seasons.sort_by(|a, b| b.EndTime.cmp(&a.EndTime));
					active_season = if seasons.len() > 0 { seasons[0].ID.clone() } else { "".to_string() };
					println!("Active Season: {}/{}", active_season, length);
				}
				for season in seasons.iter() {
					let db = get_db::<PremierSeasonDB>(client, "premier_seasons", None);
					let _ = db
						.update_one(doc! { "id": &season.ID, "affinity": affinity }, doc! { "$set": {"season": to_document(season).unwrap(), "region": region} })
						.upsert(true).await;
				}
			}
			Err(e) => {
				eprintln!("[JOBS][UPDATE_PREMIER_CONFERENCES_DATABASE]: {:?}", e);
			}
		}
	}
	for conference_fetch in fetch_conferences.into_iter() {
		match conference_fetch {
			Ok(v) => {
				let pass_data = v.pass_data.unwrap();
				let affinity = pass_data.get("affinity").unwrap();
				let region = pass_data.get("region").unwrap();
				for conference in v.data.PremierConferences {
					let db = get_db::<PremierConferencesDB>(client, "premier_conferences", None);
					let _ = db
						.update_one(
							doc! { "id": &conference.id, "season": &active_season },
							doc! { "$set": doc! {"conference": to_document(&conference).unwrap(), "affinity": affinity, "region": region} }
						)
						.upsert(true).await;
				}
			}
			Err(e) => {
				eprintln!("[JOBS][UPDATE_PREMIER_CONFERENCES_DATABASE]: {:?}", e);
			}
		}
	}
}

pub async fn update_premier_leaderboard(client: &Client) {
	#[derive(Serialize, Deserialize)]
	struct SingleID {
		id: String,
	}
	let conferences = get_premier_conferences().await;
	let c_season = get_c_season_premier().await.to_string();
	let db = get_db::<PremierTeamDB>(client, "premier_teams", None);
	for conference in conferences.iter() {
		let region = match conference.conference.key.split("_").collect::<Vec<&str>>()[0].to_lowercase().as_str() {
			"eu" => "eu",
			"na" => "na",
			"latam" => "na",
			"br" => "na",
			"ap" => "ap",
			"kr" => "kr",
			_ => "eu",
		};
		let affinity = conference.conference.key.split("_").collect::<Vec<&str>>()[0].to_lowercase();
		for div in (if conference.conference.key.contains("_SUPER") { 22..=22 } else { 1..=21 }) {
			let premier_url = PREMIER_URLS.get(&region).unwrap();
			let leaderboard_url = format!(
				"{}/leaderboard/v1/name/val-premier/region/{}/season/{}/grouping/{}-{}/?startRank=0&endRank=19",
				premier_url,
				affinity,
				c_season.clone(),
				conference.conference.key,
				div
			);
			let leaderboard = fetch::<PVPPremierLeaderboard>(FetchOptions {
				url: leaderboard_url,

				alternative_proxy: true,
				..FetchOptions::default()
			}).await;
			match leaderboard {
				Ok(v) => {
					let mut fetch_all = Vec::new();
					let mut clusterdata: Vec<PremierLeaderboardRanking> = Vec::new();
					clusterdata.extend(v.data.rankings);
					let size = v.data.size;
					let mut lower_limit = 20;
					let mut higher_limit = 39;
					while lower_limit < size {
						let leaderboard_url = format!(
							"{}/leaderboard/v1/name/val-premier/region/{}/season/{}/grouping/{}-{}/?startRank={}&endRank={}",
							premier_url,
							affinity,
							c_season,
							conference.conference.key,
							div,
							lower_limit,
							higher_limit
						);
						fetch_all.push(
							fetch::<PVPPremierLeaderboard>(FetchOptions {
								url: leaderboard_url,
								alternative_proxy: true,
								..FetchOptions::default()
							})
						);
						lower_limit += 20;
						higher_limit += 20;
					}
					let results: Vec<_> = join_all(fetch_all).await;
					for result in results {
						match result {
							Ok(v) => {
								clusterdata.extend(v.data.rankings);
							}
							Err(e) => {
								eprintln!("[JOBS][UPDATE_PREMIER_LEADERBOARD]: {:?}", e);
							}
						}
					}
					let query = doc! { "conference": conference.conference.key.clone(), "division": div, "season": &c_season };
					let get_db_results = get_db::<SingleID>(client, "premier_teams", None)
						.find(query)
						.projection(doc! { "id": 1 }).await;

					let all_db_vec = get_db_results.unwrap().try_collect::<Vec<_>>().await.unwrap();
					let all_map_db: HashMap<String, SingleID> = all_db_vec
						.into_iter()
						.map(|team| (team.id.clone(), team))
						.collect();
					let all_map_response: HashMap<String, PremierLeaderboardRanking> = clusterdata
						.into_iter()
						.map(|team| (team.entityId.clone(), team.clone()))
						.collect();

					let teams_to_add = all_map_response
						.keys()
						.filter(|team| !all_map_db.contains_key(*team))
						.collect::<Vec<_>>();
					let teams_to_remove = all_map_db
						.keys()
						.filter(|team| !all_map_response.contains_key(*team))
						.collect::<Vec<_>>();
					let teams_to_update = all_map_db
						.keys()
						.filter(|team| all_map_response.contains_key(*team))
						.collect::<Vec<_>>();

					let teams_to_add_length = teams_to_add.len();
					let teams_to_remove_length = teams_to_remove.len();
					let teams_to_update_length = teams_to_update.len();

					if !teams_to_remove.is_empty() {
						let _ = db.delete_many(
							doc! { "conference": conference.conference.key.clone(), "division": div, "season": &c_season, "id": doc! {"$in": teams_to_remove} }
						).await;
					}
					if !teams_to_add.is_empty() {
						let mut docs_to_add: Vec<Document> = vec![];
						for team in teams_to_add.iter() {
							let team = all_map_response.get(*team).unwrap();
							let primary = premier_extract_hex_color(&team.additionalInfo.customization.primaryColor).await;
							let secondary = premier_extract_hex_color(&team.additionalInfo.customization.secondaryColor).await;
							let tertiary = premier_extract_hex_color(&team.additionalInfo.customization.tertiaryColor).await;
							let team = PremierTeamDB {
								id: team.entityId.clone(),
								name: team.additionalInfo.rosterName.trim().to_string(),
								tag: team.additionalInfo.tag.trim().to_string(),
								region: region.to_string(),
								affinity: affinity.clone(),
								anonymous: team.anonymous,
								conference: conference.conference.key.clone(),
								division: div,
								customization: PremierTeamCustomization {
									icon: team.additionalInfo.customization.icon.clone(),
									primary,
									secondary,
									tertiary,
								},
								stats: PremierTeamStats {
									wins: team.additionalInfo.wins,
									losses: team.additionalInfo.losses,
									rounds_won: team.additionalInfo.roundWins,
									rounds_lost: team.additionalInfo.roundLosses,
									ranking: team.ranking,
									score: team.score,
								},
								season: c_season.clone(),
								updated_at: DateTime::now(),
								last_played: DateTime::parse_rfc3339_str(&team.lastPlayedDate).unwrap(),
								games: PremierTeamGames {
									league: vec![],
									tournament: vec![],
								},
								member: vec![],
							};
							docs_to_add.push(to_document(&team).unwrap());
						}
						let _ = get_db::<Document>(client, "premier_teams", None).insert_many(docs_to_add).await;
					}

					for team in teams_to_update.iter() {
						let team = all_map_response.get(*team).unwrap();
						let primary = premier_extract_hex_color(&team.additionalInfo.customization.primaryColor).await;
						let secondary = premier_extract_hex_color(&team.additionalInfo.customization.secondaryColor).await;
						let tertiary = premier_extract_hex_color(&team.additionalInfo.customization.tertiaryColor).await;
						let _ = db.update_one(
							doc! { "id": team.entityId.clone(), "conference": conference.conference.key.clone(),"division": div, "season": &c_season },
							doc! {
                                    "$set": doc! {
                                        "id": team.entityId.clone(),
                                        "name": team.additionalInfo.rosterName.trim(),
                                        "tag": team.additionalInfo.tag.trim(),
                                        "region": region,
                                        "affinity": &affinity,
                                        "anonymous": team.anonymous,
                                        "conference": conference.conference.key.clone(),
                                        "division": div,
                                        "customization": doc! {
                                            "icon": team.additionalInfo.customization.icon.clone(),
                                            "primary": primary,
                                            "secondary": secondary,
                                            "tertiary": tertiary,
                                        },
                                        "stats": doc! {
                                            "wins": team.additionalInfo.wins,
                                            "losses": team.additionalInfo.losses,
                                            "rounds_won": team.additionalInfo.roundWins,
                                            "rounds_lost": team.additionalInfo.roundLosses,
                                            "ranking": team.ranking,
                                            "score": team.score,
                                        },
                                        "season": c_season.clone(),
                                        "updated_at": DateTime::now(),
                                        "last_played": DateTime::parse_rfc3339_str(&team.lastPlayedDate).unwrap(),
                                    },
                                }
						).await;
					}
					//Inserted, deleted, updated, conference, division, season
					println!(
						"[JOBS][UPDATE_PREMIER_LEADERBOARD] Inserted: {}, Deleted: {}, Updated: {}, Conference: {}, Division: {}, Season: {}",
						teams_to_add_length,
						teams_to_remove_length,
						teams_to_update_length,
						conference.conference.key.clone(),
						div,
						c_season.clone()
					);
				}
				Err(e) => {
					eprintln!("[JOBS][UPDATE_PREMIER_LEADERBOARD_NO_FIRST_DATA]: {:?}", e);
				}
			}
		}
	}
}

pub async fn update_player_leaderboard(client: &Client, c_season: String) {
	let before = Instant::now();
	let riot_headers = build_riot_headers(&RiotPlatforms::PC).await;

	for affinity in AFFINITIES.iter() {
		let base_url = format!(
			"https://pd.{}.a.pvp.net/mmr/v1/leaderboards/affinity/{}/queue/competitive/season/{}",
			if affinity == "br" || affinity == "latam" {
				"na"
			} else {
				affinity
			},
			affinity,
			c_season
		);
		let first_req = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
			url: format!("{}?startIndex=0&size=1000", base_url),
			headers: Some(riot_headers.clone()),
			..FetchIPv6Options::default()
		}).await;
		match first_req {
			Ok(v) => {
				let mut players = if v.data.players.is_some() { v.data.players.unwrap() } else { vec![] };
				let iterations = ((v.data.total_players / 1000) as f32).floor() as i32;
				let mut index = 1;
				while iterations > index {
					let start_index = index * 1000;
					let next_page = fetch_ipv6::<LeaderboardV1>(FetchIPv6Options {
						url: format!("{}?startIndex={}&size=1000", base_url, start_index),
						headers: Some(riot_headers.clone()),
						..FetchIPv6Options::default()
					}).await;
					match next_page {
						Ok(v) => {
							players.extend(if v.data.players.is_some() { v.data.players.unwrap() } else { vec![] });
						}
						Err(e) => {
							eprintln!("[JOBS][LEADERBOARD] NextPage: {:?}", e);
						}
					}
					index += 1;
				}
				let db = get_db::<LeaderboardPlayerDB>(client, "leaderboard_players", None);

				for (i, player) in players.iter().enumerate() {
					if i % 100 == 0 {
						println!("[JOBS][LEADERBOARD_PLAYER] Updating players {}/{}", i, players.len());
					}
					let player = LeaderboardPlayerInDB {
						player_card_id: player.player_card_id.to_string(),
						title_id: player.title_id.to_string(),
						is_banned: player.is_banned,
						is_anonymized: player.is_anonymized,
						puuid: if let Some(id) = player.puuid {
							Some(id.to_string())
						} else {
							None
						},
						game_name: player.game_name.to_string(),
						tag_line: player.tag_line.to_string(),
						leaderboard_rank: player.leaderboard_rank,
						ranked_rating: player.ranked_rating,
						number_of_wins: player.number_of_wins,
						competitive_tier: player.competitive_tier,
					};
					let _ = db
						.update_one(
							doc! { "player.leaderboardRank": player.leaderboard_rank, "affinity": affinity, "season": c_season.clone() },
							doc! {
                            "$set": {
                                "player": to_bson(&player).unwrap(),
                                "updated_at": DateTime::now()
                            },
                            "$setOnInsert": {
                                "affinity": affinity,
                                "season": c_season.clone(),
                            }
                        }
						)
						.upsert(true).await;
				}
				let tier_details = v.data.tier_details;
				let _ = get_db::<LeaderboardPlayerDB>(client, "leaderboard_metadata", None)
					.update_one(
						doc! { "affinity": affinity, "season": c_season.clone() },
						doc! { "$set": {"updated_at": DateTime::now(), "thresholds": to_bson(&tier_details).unwrap()} }
					)
					.upsert(true).await;
			}
			Err(e) => {
				eprintln!("[JOBS][LEADERBOARD_PLAYER]: {:?}", e);
			}
		}
	}
	println!("[JOBS][LEADERBOARD_PLAYER] Synced in {}ms", before.elapsed().as_millis());
}

pub async fn update_premier_leaderboard_wrapper(client: Client) {
	update_premier_leaderboard(&client).await;
}

pub async fn update_player_leaderboard_wrapper(client: Client, c_season: String) {
	update_player_leaderboard(&client, c_season).await;
}

pub async fn load_website_wrapper(client: Client) {
	load_website(&client).await;
}

pub async fn mongo_to_s3_migration_wrapper(client: Client, ssd_client: Client) {
	use crate::methods::migration::run_mongo_to_s3_migration;

	println!("[JOBS][MONGO_TO_S3_MIGRATION] Starting migration job...");
	let before = Instant::now();

	match run_mongo_to_s3_migration(&client, &ssd_client, Some(1000), Some(10)).await {
		Ok(_) => {
			println!("[JOBS][MONGO_TO_S3_MIGRATION] Migration completed successfully in {}ms", before.elapsed().as_millis());
		}
		Err(e) => {
			eprintln!("[JOBS][MONGO_TO_S3_MIGRATION] Migration failed after {}ms: {}", before.elapsed().as_millis(), e);
		}
	}
}
