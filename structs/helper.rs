use crate::methods::utils::{ get_match_b2, get_match_mongo, RateLimitingWrapper };
use crate::structs::database::{ Account, LifetimeMMRHistory, PremierConferencesDB, PremierTeamDB, PremierTeamGamesLeague, PremierTeamGamesTournament, PremierTeamMember };
use crate::structs::enums::RiotPlatforms;
use crate::structs::http_clients::{ fetch_ipv6, redis_fetch_ipv6, FetchIPv6Options, FetchOptions, FetchResponse, RedisFetchIPv6Options, RedisFetchResponse };
use crate::structs::pvp_api::{ PVPMMRCompetitiveUpdatesV1, PVPMMRCompetitiveUpdatesV1Match, PVPMMRPlayerV1, PVPMatchHistoryV1, PVPNameServiceV2, PVPPremierMatches };
use crate::structs::responses::MatchesV4Response;
use crate::structs::valorant_api_officer::{
	<PERSON><PERSON><PERSON><PERSON>IA<PERSON>,
	<PERSON><PERSON>tAPIBudd<PERSON>,
	<PERSON>orantAPIContentTier,
	ValorantAPIFlex,
	ValorantAPIFlexItem,
	ValorantAPIGamemodeQueuesData,
	ValorantAPIGamemodesData,
	ValorantAPIMap,
	ValorantAPIPlayerCard,
	ValorantAPIPlayerTitle,
	ValorantAPISeasonsData,
	ValorantAPISpray,
	ValorantAPIWeaponSkin,
};
use crate::{
	build_official_riot_headers,
	build_official_riot_headers_hash_map,
	build_riot_headers,
	fetch,
	get_db,
	ErrorCodes,
	HDDClient,
	SeasonShortIdCombo,
	OFFICER_API_AGENTS,
	OFFICER_API_BUDDIES,
	OFFICER_API_CONTENT_GAMEMODES,
	OFFICER_API_CONTENT_GAMEMODES_QUEUES,
	OFFICER_API_CONTENT_TIERS,
	OFFICER_API_FLEX,
	OFFICER_API_GAME_PODS,
	OFFICER_API_GEARS,
	OFFICER_API_MAPS,
	OFFICER_API_PLAYER_CARDS,
	OFFICER_API_PLAYER_TITLES,
	OFFICER_API_SEASONS,
	OFFICER_API_SKINS,
	OFFICER_API_SPRAYS,
	OFFICER_API_WEAPONS,
	REGIONS,
	VALORANT_PREMIER_COLORS,
	VALORANT_PREMIER_CONFERENCES,
	VALORANT_PREMIER_CURRENT_SEASON,
	VALORANT_REGULAR_CURRENT_SEASON,
	VALORANT_SEASON_SHORT_IDS,
	VALORANT_TIERS,
	VALORANT_TIERS_OLD,
};
use deadpool_redis::Connection;
use futures::future::join_all;
use log::{ error, info };
use mongodb::bson::{ doc, to_bson, DateTime };
use mongodb::options::{ Collation, CollationAlternate, CollationStrength, ReturnDocument };
use mongodb::{ bson, Client };
use redis::aio::MultiplexedConnection;
use serde::{Deserialize, Deserializer, Serialize, Serializer};
use serde_json::Value;
use std::collections::HashMap;
use std::fmt::Debug;
use std::sync::Arc;
use urlencoding::encode;
use valorant_api::response_types::account_aliases_v1::AccountAliasOtherV1;
use valorant_api::response_types::competitive_updates_v1::{
    CompetitiveUpdateMatches, CompetitiveUpdatesV1,
};
use valorant_api::response_types::matchhistory_v1::MatchHistoryV1;
use valorant_api_official::response_types::account_v1::AccountV1;
use valorant_api_official::response_types::activeshards_v1::ActiveShardsV1;
use valorant_assets_api::models::gear::Gear;
use valorant_assets_api::models::weapon::Weapon;

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct UserDataCandidate {
	pub game_name: String,
	pub tag_line: String,
	pub region: String,
	pub is_current: bool, // true if DisplayName is empty (current data)
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct ReliableUserData {
	pub game_name: String,
	pub tag_line: String,
	pub region: String,
	pub confidence_score: f32, // 0.0 to 1.0, higher is more reliable
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CustomQueues {
	pub id: String,
	pub name: String,
	pub api: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct PremierTeamGamesFetchMatches {
	pub redis_cache_ttl: isize,
	pub background_requests: usize,
	pub league: Vec<PremierTeamGamesLeague>,
	pub tournament: Vec<PremierTeamGamesTournament>,
	pub members: Vec<PremierTeamMember>,
}

pub async fn get_queues() -> Vec<CustomQueues> {
	let queues = OFFICER_API_CONTENT_GAMEMODES_QUEUES.load_full();
	queues
		.iter()
		.map(|i| {
			if i.queueId == "newmap" {
				return CustomQueues {
					id: i.queueId.clone(),
					name: String::from("New Map"),
					api: String::from("newmap"),
				};
			}
			CustomQueues {
				id: i.queueId.clone(),
				name: i.dropdownText.clone(),
				api: i.selectedText.to_lowercase(),
			}
		})
		.collect()
}

pub fn get_tier(tier: i32) -> String {
	match VALORANT_TIERS.get(&(tier as usize)) {
		Some(&v) => String::from(v.0),
		None => String::from("Unknown"),
	}
}

pub fn get_tier_protection_status(tier: i32) -> bool {
	match VALORANT_TIERS.get(&(tier as usize)) {
		Some(&v) => v.1,
		None => false,
	}
}

pub fn get_old_tier(tier: i32) -> String {
	match VALORANT_TIERS_OLD.get(&(tier as usize)) {
		Some(&v) => String::from(v),
		None => String::from("Unknown"),
	}
}

pub async fn get_maps() -> Arc<Vec<ValorantAPIMap>> {
	OFFICER_API_MAPS.load_full()
}

pub async fn get_sprays() -> Arc<Vec<ValorantAPISpray>> {
	OFFICER_API_SPRAYS.load_full()
}

pub async fn get_player_cards() -> Arc<Vec<ValorantAPIPlayerCard>> {
	OFFICER_API_PLAYER_CARDS.load_full()
}

pub async fn get_player_titles() -> Arc<Vec<ValorantAPIPlayerTitle>> {
	OFFICER_API_PLAYER_TITLES.load_full()
}

pub async fn get_content_tiers() -> Arc<Vec<ValorantAPIContentTier>> {
	OFFICER_API_CONTENT_TIERS.load_full()
}

pub async fn get_buddies() -> Arc<Vec<ValorantAPIBuddy>> {
	OFFICER_API_BUDDIES.load_full()
}

pub async fn get_weapons() -> Arc<Vec<Weapon>> {
	OFFICER_API_WEAPONS.load_full()
}

pub async fn get_agents() -> Arc<Vec<ValorantAPIAgent>> {
	OFFICER_API_AGENTS.load_full()
}

pub async fn get_gears() -> Arc<Vec<Gear>> {
	OFFICER_API_GEARS.load_full()
}

pub async fn get_skins() -> Arc<Vec<ValorantAPIWeaponSkin>> {
	OFFICER_API_SKINS.load_full()
}

pub async fn get_flex() -> Arc<Vec<ValorantAPIFlexItem>> {
	OFFICER_API_FLEX.load_full()
}

pub async fn get_c_season() -> Arc<String> {
	VALORANT_REGULAR_CURRENT_SEASON.load_full().clone()
}

pub async fn get_c_season_premier() -> Arc<String> {
	VALORANT_PREMIER_CURRENT_SEASON.load_full()
}

pub async fn get_premier_conferences() -> Arc<Vec<PremierConferencesDB>> {
	VALORANT_PREMIER_CONFERENCES.load_full()
}

pub fn roman_to_number(roman: &str) -> Option<String> {
	match roman {
		"I" => Some("1".to_string()),
		"II" => Some("2".to_string()),
		"III" => Some("3".to_string()),
		"IV" => Some("4".to_string()),
		"V" => Some("5".to_string()),
		"VI" => Some("6".to_string()),
		_ => None, // Handle cases where the act_id is not a Roman numeral
	}
}

pub async fn get_short_season_by_id(id: &str) -> Option<SeasonShortIdCombo> {
	let seasons = VALORANT_SEASON_SHORT_IDS.load_full();
	let season = seasons.iter().find(|i| i.season == id);
	match season {
		Some(v) => Some(v.clone()),
		None => None,
	}
}

pub async fn get_short_ids() -> Arc<Vec<SeasonShortIdCombo>> {
	VALORANT_SEASON_SHORT_IDS.load_full()
}

pub async fn get_season_by_id(id: &str) -> Option<ValorantAPISeasonsData> {
	let seasons = OFFICER_API_SEASONS.load_full();
	let season = seasons.iter().find(|i| i.uuid == id);
	match season {
		Some(v) => Some(v.clone()),
		None => None,
	}
}

pub async fn get_map_by_id(map: &str) -> Option<ValorantAPIMap> {
	let maps = OFFICER_API_MAPS.load_full();
	maps.iter()
		.find(|i| i.uuid == map)
		.cloned()
}

pub async fn get_map_by_asset(map: &str) -> Option<ValorantAPIMap> {
	let maps = OFFICER_API_MAPS.load_full();
	maps.iter()
		.find(|i| i.mapUrl == map)
		.cloned()
}

pub async fn get_map_by_name(map: &str) -> Option<ValorantAPIMap> {
	let maps = OFFICER_API_MAPS.load_full();
	maps.iter()
		.find(|i| i.displayName.to_lowercase() == map.to_lowercase())
		.cloned()
}

pub async fn get_queue_by_queue_id(queue_id: &str) -> Option<ValorantAPIGamemodeQueuesData> {
	let queues = OFFICER_API_CONTENT_GAMEMODES_QUEUES.load_full();
	let queue = queues.iter().find(|i| i.queueId == queue_id);
	match queue {
		Some(v) => Some(v.clone()),
		None => {
			if queue_id == "" {
				let custom = queues.iter().find(|i| i.queueId == "custom");
				match custom {
					Some(v) => Some(v.clone()),
					None => None,
				}
			} else {
				None
			}
		}
	}
}

pub async fn get_queue_by_text(queue_id: &str) -> Option<ValorantAPIGamemodeQueuesData> {
	let queues = OFFICER_API_CONTENT_GAMEMODES_QUEUES.load_full();
	let queue = queues.iter().find(|i| { !i.queueId.contains("console") && i.selectedText.to_lowercase().replace(" ", "") == queue_id });
	match queue {
		Some(v) => Some(v.clone()),
		None => None,
	}
}

pub async fn get_gamemode_by_partial_asset(mode: &str) -> Option<ValorantAPIGamemodesData> {
	let modes = OFFICER_API_CONTENT_GAMEMODES.load_full();
	let mode = modes.iter().find(|i| {
		let split = i.assetPath.split("/").collect::<Vec<&str>>();
		mode.contains(split[2]) && mode.contains(split[3])
	});
	match mode {
		Some(v) => Some(v.clone()),
		None => None,
	}
}

pub fn get_region_by_pod_id(pod_id: &str) -> String {
	match pod_id {
		x if x.contains("ap") => String::from("ap"),
		x if x.contains("eu") => String::from("eu"),
		x if x.contains("kr") => String::from("kr"),
		x if x.contains("na") => String::from("na"),
		x if x.contains("latam") => String::from("latam"),
		x if x.contains("br") => String::from("br"),
		_ => String::from("eu"),
	}
}

pub async fn get_gamepod_by_id(id: &str) -> Option<String> {
	let gamepods = OFFICER_API_GAME_PODS.load_full();
	let pod = gamepods.get(id);
	match pod {
		Some(v) => Some(v.clone()),
		None => None,
	}
}

pub async fn get_agent_by_id(id: &str) -> Option<ValorantAPIAgent> {
	let agents = OFFICER_API_AGENTS.load_full();
	let agent = agents.iter().find(|i| i.uuid == id);
	match agent {
		Some(v) => Some(v.clone()),
		None => None,
	}
}

pub fn calculate_elo(tier: i32, progress: i32) -> i32 {
	if tier >= 24 { 2100 + progress } else if tier == 0 { 0 } else { tier * 100 - 300 + progress }
}

pub async fn fetch_premier_team_by_name(name: &str, tag: &str, c_season: &str, client: &Client) -> Option<PremierTeamDB> {
	let team = get_db::<PremierTeamDB>(client, "premier_teams", None)
		.find_one(doc! {
            "name": name.trim(),
            "tag": tag.trim(),
            "season": c_season.trim(),
        })
		.collation(Collation::builder().locale("en").strength(CollationStrength::Secondary).build()).await;
	team.unwrap_or_else(|_| None)
}

pub async fn fetch_premier_team_by_id(id: &str, c_season: &str, client: &Client) -> Option<PremierTeamDB> {
	let team = get_db::<PremierTeamDB>(client, "premier_teams", None).find_one(doc! {
            "id": id.trim(),
            "season": c_season.trim(),
        }).await;
	team.unwrap_or_else(|_| None)
}

pub async fn fetch_premier_matches_by_team_id(team: PremierTeamDB, redis: MultiplexedConnection) -> PremierTeamGamesFetchMatches {
	let mut premier_team_games = PremierTeamGamesFetchMatches {
		redis_cache_ttl: -1,
		background_requests: 0,
		league: team.games.league.clone(),
		tournament: team.games.tournament.clone(),
		members: team.member,
	};
	let fetch_games = redis_fetch_ipv6::<PVPPremierMatches>(RedisFetchIPv6Options {
		redis_client: Some(redis),
		url: format!("https://pd.{}.a.pvp.net/premier/v1/rosters/{}/matchhistory", team.region, team.id),
		store: format!("premier;team;{};history", team.id),
		..RedisFetchIPv6Options::default()
	}).await;
	match fetch_games {
		Ok(v) => {
			premier_team_games.redis_cache_ttl = v.ttl as isize;
			if !v.is_from_redis {
				premier_team_games.background_requests += 1;
			}
			let unknown_league_games = v.data.leagueMatchHistory
				.iter()
				.filter(
					|i|
						!team.games.league
							.iter()
							.clone()
							.any(|k| k.id == i.matchId)
				)
				.collect::<Vec<_>>();
			let unknown_tournament_games = v.data.tournamentMatchHistory
				.iter()
				.filter(|i| { !team.games.tournament.iter().any(|k| k.tournament_id == i.tournamentId) })
				.collect::<Vec<_>>();
			let mut fetch_games = unknown_league_games
				.iter()
				.map(|i|
					fetch::<MatchesV4Response>(FetchOptions {
						url: format!("http://127.0.0.1:60000/valorant/v4/match/{}/{}?api_key=HDEV-c9af515d-87ae-4056-8cf7-93801efcee80", team.region, i.matchId),
						headers: None,
						..FetchOptions::default()
					})
				)
				.collect::<Vec<_>>();
			for league_game in unknown_league_games {
				premier_team_games.league.push(PremierTeamGamesLeague {
					id: league_game.matchId.clone(),
					points_before: league_game.leaguePointsBefore,
					points_after: league_game.leaguePointsAfter,
					started_at: DateTime::from_millis(league_game.startTime),
				});
			}
			for tournament_game in unknown_tournament_games {
				let matches = tournament_game.matchEntries.keys();
				for game in matches.clone() {
					fetch_games.push(
						fetch::<MatchesV4Response>(FetchOptions {
							url: format!("http://127.0.0.1:60000/valorant/v4/match/{}/{}?api_key=HDEV-c9af515d-87ae-4056-8cf7-93801efcee80", team.region, game),
							headers: None,
							..FetchOptions::default()
						})
					);
					premier_team_games.tournament.push(PremierTeamGamesTournament {
						tournament_id: tournament_game.tournamentId.clone(),
						placement: tournament_game.finalPlacement,
						placement_league_bonus: tournament_game.finalPlacementLeaguePointsBonus,
						points_before: tournament_game.leaguePointsBefore,
						points_after: tournament_game.leaguePointsAfter,
						matches: Vec::from_iter(matches.clone().cloned()),
					});
				}
			}
			if fetch_games.is_empty() {
				return premier_team_games;
			}
			let fetched_games: Vec<_> = join_all(fetch_games).await;
			let mut fetched_games_okay = fetched_games
				.iter()
				.filter(|i| i.is_ok())
				.collect::<Vec<_>>();
			if fetched_games_okay.is_empty() {
				return premier_team_games;
			}
			fetched_games_okay.sort_by(|a, b| {
				DateTime::parse_rfc3339_str(b.as_ref().unwrap().data.data.metadata.started_at.clone())
					.unwrap()
					.timestamp_millis()
					.cmp(&DateTime::parse_rfc3339_str(a.as_ref().unwrap().data.data.metadata.started_at.clone()).unwrap().timestamp_millis())
			});
			let first_game = fetched_games_okay[0].clone().unwrap().data;
			let member_ids = if
				let Some(roster) = first_game.data.teams
					.iter()
					.find(|i| i.premier_roster.clone().unwrap().id == team.id)
					.unwrap()
					.premier_roster.clone()
			{
				roster.members
			} else {
				vec![]
			};

			let member_names = fetch_ipv6::<Vec<PVPNameServiceV2>>(FetchIPv6Options {
				url: format!("https://pd.{}.a.pvp.net/name-service/v2/players", team.region),
				method: String::from("PUT"),
				data: Value::from(member_ids),
				..FetchIPv6Options::default()
			}).await;
			if let Ok(names) = member_names {
				premier_team_games.background_requests += 1;
				premier_team_games.members.clear();
				for member in names.data.iter().filter(|i| !i.Subject.is_empty()) {
					premier_team_games.members.push(PremierTeamMember {
						puuid: member.Subject.clone(),
						name: Some(member.GameName.clone()),
						tag: Some(member.TagLine.clone()),
					});
				}
			}
			premier_team_games.league.sort_by(|a, b| a.started_at.cmp(&b.started_at));
			premier_team_games
		}
		Err(_) => premier_team_games,
	}
}

pub fn premier_extract_rgb_values(rgba_str: &str) -> (f32, f32, f32) {
	// Using regex to extract RGBA values from the input string
	let re = regex::Regex::new(r"R=(.*?),G=(.*?),B=(.*?),A=(.*?)\)").unwrap();
	if let Some(captures) = re.captures(rgba_str) {
		return (captures[1].parse().unwrap(), captures[2].parse().unwrap(), captures[3].parse().unwrap());
	}
	(0.5, 0.5, 0.5)
}

pub async fn premier_extract_hex_color(rgba_str: &str) -> String {
	let (r, g, b) = premier_extract_rgb_values(rgba_str);
	let colors = VALORANT_PREMIER_COLORS.load_full();
	let color_id = colors.iter().find(|i| &&i.Color.R == &&r && &&i.Color.G == &&g && &&i.Color.B == &&b);
	match color_id {
		Some(color) => color.Color.Hex[2..].to_string().to_lowercase(),
		None => String::from("000000"),
	}
}

pub async fn get_valorant_account_by_name(
	client: &Client,
	redis_client: MultiplexedConnection,
	name: &str,
	tag: &str,
	force: bool,
	check_time: bool
) -> Result<RateLimitingWrapper<Account>, ErrorCodes> {
	let account_db = get_db::<Account>(client, "accounts", None);
	let collation = Collation::builder().strength(CollationStrength::Secondary).locale("en").build();
	let account_f = account_db.find_one(doc! { "name": sanitize_name(name), "tag": sanitize_name(tag) }).collation(collation).await;
	if account_f.is_err() {
		return Err(ErrorCodes::DatabaseError);
	}
	let account = account_f.unwrap();
	if account.is_none() {
		return fetch_valorant_account_by_name(client, redis_client, name, tag).await;
	}
	let account = account.unwrap();
	if check_time && account.updated_at.timestamp_millis() + 3600000 < DateTime::now().timestamp_millis() {
		let update = update_valorant_account_by_id(client, redis_client, &account.puuid, &account.region, account.clone()).await;
		if update.is_err() {
			return Ok(RateLimitingWrapper {
				data: account,
				background_requests: 0,
			});
		}
		return Ok(RateLimitingWrapper {
			data: update?,
			background_requests: 1,
		});
	}
	if force {
		let update = update_valorant_account_by_id(client, redis_client, &account.puuid, &account.region, account.clone()).await;
		if update.is_err() {
			return Ok(RateLimitingWrapper {
				data: account,
				background_requests: 2,
			});
		}
		return Ok(RateLimitingWrapper {
			data: update?,
			background_requests: 2,
		});
	}
	Ok(RateLimitingWrapper {
		data: account,
		background_requests: 0,
	})
}

pub async fn get_valorant_account_by_id(
	client: &Client,
	redis_client: MultiplexedConnection,
	puuid: &str,
	force: bool,
	check_time: bool
) -> Result<RateLimitingWrapper<Account>, ErrorCodes> {
	let account_db = get_db::<Account>(client, "accounts", None);
	let account_f = account_db.find_one(doc! { "puuid": puuid }).await;
	if account_f.is_err() {
		return Err(ErrorCodes::DatabaseError);
	}
	let account = account_f.unwrap();
	if account.is_none() {
		return fetch_valorant_account_by_id(client, redis_client, puuid).await;
	}
	let account = account.unwrap();
	if check_time && account.updated_at.timestamp_millis() + 5400000 < DateTime::now().timestamp_millis() {
		let update = update_valorant_account_by_id(client, redis_client, puuid, account.region.as_str(), account.clone()).await;
		if update.is_err() {
			return Ok(RateLimitingWrapper {
				data: account,
				background_requests: 0,
			});
		}
		return Ok(RateLimitingWrapper {
			data: update?,
			background_requests: 1,
		});
	}
	if force {
		let update = update_valorant_account_by_id(client, redis_client, puuid, account.region.as_str(), account.clone()).await;
		if update.is_err() {
			return Ok(RateLimitingWrapper {
				data: account,
				background_requests: 2,
			});
		}
		return Ok(RateLimitingWrapper {
			data: update?,
			background_requests: 2,
		});
	}
	Ok(RateLimitingWrapper {
		data: account,
		background_requests: 0,
	})
}

pub async fn fetch_valorant_account_by_name(
	client: &Client,
	redis_client: MultiplexedConnection,
	name: &str,
	tag: &str
) -> Result<RateLimitingWrapper<Account>, ErrorCodes> {
	let url = format!("https://eu.api.account.riotgames.com/aliases/v1/aliases?gameName={}&tagLine={}", encode(name), encode(tag));
	let riot_headers = build_riot_headers(&RiotPlatforms::PC).await;
	let account = fetch_ipv6::<Vec<AccountAliasOtherV1>>(FetchIPv6Options {
		url,
		method: "GET".to_string(),
		headers: Some(riot_headers),
		..FetchIPv6Options::default()
	}).await;
	if account.is_err() {
		error!("Error: {:?}", account.unwrap_err());
		return Err(ErrorCodes::InternalError);
	}
	let account = account.unwrap();
	if account.data.len() == 0 {
		return Err(ErrorCodes::AccountNotFound);
	}
	let account = account.data[0].clone();
	fetch_valorant_account_by_id(client, redis_client, &account.puuid.to_string()).await
}

pub async fn fetch_valorant_account_by_id_old(client: &Client, redis_client: MultiplexedConnection, puuid: &str) -> Result<RateLimitingWrapper<Account>, ErrorCodes> {
	let region = search_region(puuid).await;
	if region.is_err() {
		return Err(region.unwrap_err());
	}
	let region_history = region?;
	let region = region_history.headers.get("url").unwrap().to_str().unwrap().split("/").collect::<Vec<&str>>()[2].split(".").collect::<Vec<&str>>()[1].to_string();
	let get_last_match_search = region_history.data.History.first();
	if get_last_match_search.is_none() {
		return Err(ErrorCodes::RegionNotFound);
	}
	let get_last_match = get_last_match_search.unwrap();
	let last_match = get_match_b2(client, redis_client, get_last_match.MatchID.clone(), region.clone()).await;
	if last_match.error {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let players = last_match.data.clone().unwrap().players;
	let last_match_player_search = players.iter().find(|i| i.subject.to_string().as_str() == puuid);
	if last_match_player_search.is_none() {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let last_match_player = last_match_player_search.unwrap();

	let mut platforms = vec![RiotPlatforms::PC.to_string()];
	let console_check = fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
		url: format!("https://pd.{}.a.pvp.net/match-history/v1/history/{}", region, puuid),
		headers: Some(build_riot_headers(&RiotPlatforms::CONSOLE).await),
		..FetchIPv6Options::default()
	}).await;

	if let Ok(console_check) = console_check {
		if console_check.data.History.len() > 0 {
			platforms.push(RiotPlatforms::CONSOLE.to_string());
		}
	}

	let account = Account {
		name: last_match_player.game_name.clone(),
		tag: last_match_player.tag_line.clone(),
		puuid: puuid.to_string(),
		region,
		card: last_match_player.player_card.clone().to_string(),
		title: last_match_player.player_title.clone().to_string(),
		account_level: last_match_player.account_level.unwrap_or(0) as i32,
		platforms,
		created_at: DateTime::now(),
		updated_at: DateTime::now(),
		last_match: DateTime::from_millis(get_last_match.GameStartTime),
	};
	let account_db = get_db::<Account>(client, "accounts", None);
	let _ = account_db.update_one(doc! { "puuid": account.puuid.clone() }, doc! { "$set": to_bson(&account).unwrap() }).upsert(true).await;
	Ok(RateLimitingWrapper {
		data: account,
		background_requests: 2,
	})
}

pub async fn fetch_valorant_account_by_id(client: &Client, redis_client: MultiplexedConnection, puuid: &str) -> Result<RateLimitingWrapper<Account>, ErrorCodes> {
	let mut region = None;
	let region_fetch = search_region_riot_official(None, None, puuid.to_string()).await;
	if region_fetch.is_err() {
		let alternative = search_region(puuid).await;
		if alternative.is_ok() {
			region = Some(alternative?.url.split("/").collect::<Vec<&str>>()[2].split(".").collect::<Vec<&str>>()[1].to_string());
		} else {
			return Err(ErrorCodes::RegionNotFound);
		}
		return Err(region_fetch.unwrap_err());
	} else {
		region = Some(region_fetch?);
	}
	let region = region.unwrap();
	let mut all_games = vec![];
	let mut platforms = vec![];
	let riot_headers_pc = build_riot_headers(&RiotPlatforms::PC).await;
	let riot_headers_console = build_riot_headers(&RiotPlatforms::CONSOLE).await;
	let fetch = join_all([
		fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
			url: format!("https://pd.{}.a.pvp.net/match-history/v1/history/{}", region, puuid),
			headers: Some(riot_headers_pc.clone()),
			pass_data: Some(HashMap::from([("platform".to_string(), "pc".to_string())])),
			..FetchIPv6Options::default()
		}),
		fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
			url: format!("https://pd.{}.a.pvp.net/match-history/v1/history/{}", region, puuid),
			headers: Some(riot_headers_console.clone()),
			pass_data: Some(HashMap::from([("platform".to_string(), "console".to_string())])),
			..FetchIPv6Options::default()
		}),
	]).await;
	if fetch.iter().all(|i| i.is_err()) {
		return Err(ErrorCodes::RegionNotFound);
	}
	for i in fetch {
		if i.is_ok() {
			let i = i.unwrap();
			if i.data.History.len() > 0 {
				platforms.push(i.pass_data.unwrap().get("platform").unwrap().to_string().to_uppercase());
			}
			all_games.extend(i.data.History);
		}
	}
	all_games.sort_by(|a, b| b.GameStartTime.cmp(&a.GameStartTime));
	let get_last_match = all_games.first();
	if get_last_match.is_none() {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let get_last_match = get_last_match.unwrap();
	let last_match = get_match_b2(client, redis_client.clone(), get_last_match.MatchID.clone(), region.clone()).await;
	if last_match.error {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let players = last_match.data.clone().unwrap().players;
	let last_match_player_search = players.iter().find(|i| i.subject.to_string().as_str() == puuid);
	if last_match_player_search.is_none() {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let last_match_player = last_match_player_search.unwrap();

	let account = Account {
		name: last_match_player.game_name.clone(),
		tag: last_match_player.tag_line.clone(),
		puuid: puuid.to_string(),
		region,
		card: last_match_player.player_card.clone().to_string(),
		title: last_match_player.player_title.clone().to_string(),
		account_level: last_match_player.account_level.unwrap_or(0) as i32,
		platforms,
		created_at: DateTime::now(),
		updated_at: DateTime::now(),
		last_match: DateTime::from_millis(get_last_match.GameStartTime),
	};
	let account_db = get_db::<Account>(client, "accounts", None);
	let _ = account_db.update_one(doc! { "puuid": account.puuid.clone() }, doc! { "$set": to_bson(&account).unwrap() }).upsert(true).await;
	Ok(RateLimitingWrapper {
		data: account,
		background_requests: 2,
	})
}

pub async fn update_valorant_account_by_id(
	client: &Client,
	redis_client: MultiplexedConnection,
	puuid: &str,
	region: &str,
	old_account: Account
) -> Result<Account, ErrorCodes> {
	let riot_headers_pc = build_riot_headers(&RiotPlatforms::PC).await;
	let riot_headers_xbox = build_riot_headers(&RiotPlatforms::XBOX).await;
	let matches = join_all(
		vec![
			fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
				url: format!("https://pd.{}.a.pvp.net/match-history/v1/history/{}", region, puuid),
				headers: Some(riot_headers_pc.clone()),
				pass_data: Some(HashMap::from([("platform".to_string(), "pc".to_string())])),
				..FetchIPv6Options::default()
			}),
			fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
				url: format!("https://pd.{}.a.pvp.net/match-history/v1/history/{}", region, puuid),
				headers: Some(riot_headers_xbox.clone()),
				pass_data: Some(HashMap::from([("platform".to_string(), "console".to_string())])),
				..FetchIPv6Options::default()
			})
		]
	).await;
	if matches.iter().all(|i| i.is_err()) {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let matches_ok = matches.iter().filter(|i| i.is_ok());
	let mut all_match_ids = vec![];
	let mut platforms = vec![];
	for match_data in matches_ok {
		let match_data = match_data.clone().unwrap();
		all_match_ids.extend(match_data.data.History);
		if match_data.pass_data.unwrap().get("platform").unwrap() == "pc" {
			platforms.push(RiotPlatforms::PC.to_string());
		} else {
			platforms.push(RiotPlatforms::CONSOLE.to_string());
		}
	}
	all_match_ids.sort_by(|a, b| b.GameStartTime.cmp(&a.GameStartTime));
	if all_match_ids.is_empty() {
		return Err(ErrorCodes::RegionNotFound);
	}
	let last_match = get_match_b2(client, redis_client.clone(), all_match_ids.first().unwrap().MatchID.clone(), region.to_string()).await;
	if last_match.error {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let players = last_match.data.clone().unwrap().players;
	let last_match_player_search = players.iter().find(|i| i.subject.to_string() == puuid);
	if last_match_player_search.is_none() {
		return Err(ErrorCodes::AccountMatchFetchError);
	}
	let last_match_player = last_match_player_search.unwrap();
	let account = Account {
		puuid: puuid.to_string(),
		name: last_match_player.game_name.clone(),
		tag: last_match_player.tag_line.clone(),
		region: String::from(region),
		card: String::from(last_match_player.player_card.clone()),
		title: String::from(last_match_player.player_title.clone()),
		account_level: last_match_player.account_level.unwrap_or(0) as i32,
		platforms,
		created_at: old_account.created_at,
		updated_at: DateTime::now(),
		last_match: DateTime::from_millis(all_match_ids.first().unwrap().GameStartTime),
	};
	let account_db = get_db::<Account>(client, "accounts", None);
	let new = account_db.find_one_and_update(doc! { "puuid": puuid }, doc! { "$set": bson::to_document(&account).unwrap() }).return_document(ReturnDocument::After).await;
	if new.is_err() {
		return Err(ErrorCodes::DatabaseError);
	}
	Ok(new.unwrap().unwrap())
}

pub async fn search_region(puuid: &str) -> Result<FetchResponse<PVPMatchHistoryV1>, ErrorCodes> {
	let riot_headers = build_riot_headers(&RiotPlatforms::PC).await;
	let fetch = join_all([
		fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
			url: format!("https://pd.eu.a.pvp.net/match-history/v1/history/{}", puuid),
			headers: Some(riot_headers.clone()),
			..FetchIPv6Options::default()
		}),
		fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
			url: format!("https://pd.na.a.pvp.net/match-history/v1/history/{}", puuid),
			headers: Some(riot_headers.clone()),
			..FetchIPv6Options::default()
		}),
		fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
			url: format!("https://pd.kr.a.pvp.net/match-history/v1/history/{}", puuid),
			headers: Some(riot_headers.clone()),
			..FetchIPv6Options::default()
		}),
		fetch_ipv6::<PVPMatchHistoryV1>(FetchIPv6Options {
			url: format!("https://pd.ap.a.pvp.net/match-history/v1/history/{}", puuid),
			headers: Some(riot_headers.clone()),
			..FetchIPv6Options::default()
		}),
	]).await;
	if fetch.iter().all(|i| i.is_err()) {
		return Err(ErrorCodes::RegionNotFound);
	}
	let region_find = fetch
		.into_iter()
		.filter(|i| i.is_ok())
		.collect::<Vec<_>>();
	let len = region_find.len();
	if len < 1 {
		return Err(ErrorCodes::RegionNotFound);
	}
	if len > 1 {
		let real_region = region_find.into_iter().find(|i| i.clone().unwrap().data.History.len() > 0);
		if real_region.is_none() {
			return Err(ErrorCodes::RegionNotFound);
		}
		let real_region = real_region.unwrap().unwrap();
		return Ok(real_region);
	}
	let region = region_find.first().unwrap().clone().unwrap();
	Ok(region)
}

/// Fetches user data from all regions and determines the most reliable GameName/TagLine using majority voting
pub async fn get_reliable_user_data(puuid: String) -> Result<ReliableUserData, ErrorCodes> {
	let riot_headers_pc = build_riot_headers(&RiotPlatforms::PC).await;

	// Fetch data from all regions concurrently
	let region_requests = REGIONS.iter().map(|region| {
		let puuid_clone = puuid.clone();
		let headers_clone = riot_headers_pc.clone();
		let region_clone = region.clone();

		async move {
			let result = fetch_ipv6::<Vec<PVPNameServiceV2>>(FetchIPv6Options {
				url: format!("https://pd.{}.a.pvp.net/name-service/v2/players", region_clone),
				method: "PUT".to_string(),
				headers: Some(headers_clone),
				data: Value::from(vec![puuid_clone]),
				..FetchIPv6Options::default()
			}).await;

			match result {
				Ok(response) => {
					if let Some(user_data) = response.data.first() {
						if !user_data.Subject.is_empty() {
							return Some(UserDataCandidate {
								game_name: user_data.GameName.clone(),
								tag_line: user_data.TagLine.clone(),
								region: region_clone,
								is_current: user_data.DisplayName.is_empty(),
							});
						}
					}
				}
				Err(_) => {}
			}
			None
		}
	});

	let results: Vec<Option<UserDataCandidate>> = join_all(region_requests).await;
	let candidates: Vec<UserDataCandidate> = results
		.into_iter()
		.filter_map(|x| x)
		.collect();

	if candidates.is_empty() {
		return Err(ErrorCodes::RegionNotFound);
	}

	// Prioritize current data (DisplayName is empty)
	let current_candidates: Vec<&UserDataCandidate> = candidates
		.iter()
		.filter(|c| c.is_current)
		.collect();

	let working_candidates = if !current_candidates.is_empty() {
		current_candidates
	} else {
		// If no current data available, use all candidates but with lower confidence
		candidates.iter().collect()
	};

	// Count occurrences of each GameName/TagLine combination
	let mut name_tag_counts: HashMap<(String, String), Vec<&UserDataCandidate>> = HashMap::new();
	for candidate in &working_candidates {
		let key = (candidate.game_name.clone(), candidate.tag_line.clone());
		name_tag_counts.entry(key).or_insert_with(Vec::new).push(candidate);
	}

	// Find the most common GameName/TagLine combination
	let (most_common_name_tag, supporting_candidates) = name_tag_counts
		.iter()
		.max_by_key(|(_, candidates)| candidates.len())
		.ok_or(ErrorCodes::RegionNotFound)?;

	// Calculate confidence score
	let total_candidates = working_candidates.len() as f32;
	let supporting_count = supporting_candidates.len() as f32;
	let base_confidence = supporting_count / total_candidates;

	// Boost confidence if we have current data
	let has_current_data = supporting_candidates.iter().any(|c| c.is_current);
	let confidence_score = if has_current_data {
		base_confidence * 0.8 + 0.2 // Minimum 0.2 for current data
	} else {
		base_confidence * 0.6 // Lower confidence for outdated data
	};

	// Determine the most likely region (prefer regions with current data)
	let best_region = supporting_candidates
		.iter()
		.find(|c| c.is_current)
		.or_else(|| supporting_candidates.first())
		.map(|c| c.region.clone())
		.unwrap_or_else(|| "eu".to_string());

	Ok(ReliableUserData {
		game_name: most_common_name_tag.0.clone(),
		tag_line: most_common_name_tag.1.clone(),
		region: best_region,
		confidence_score,
	})
}

pub async fn search_region_riot_official(name: Option<String>, tag: Option<String>, puuid: String) -> Result<String, ErrorCodes> {
	let mut name = name;
	let mut tag = tag;
	let official_headers = build_official_riot_headers().await;

	// If name/tag not provided, get reliable user data from all regions
	if name.is_none() || tag.is_none() {
		let reliable_data = get_reliable_user_data(puuid.clone()).await?;

		// Only use the data if confidence is reasonable (> 0.3)
		if reliable_data.confidence_score > 0.3 {
			name = Some(reliable_data.game_name);
			tag = Some(reliable_data.tag_line);
		} else {
			// Fallback to old method if confidence is too low
			return Err(ErrorCodes::RegionNotFound);
		}
	}

	// Use official Riot API to get the authoritative region
	let riot_account = fetch::<AccountV1>(FetchOptions {
		url: format!("https://europe.api.riotgames.com/riot/account/v1/accounts/by-riot-id/{}/{}", name.unwrap(), tag.unwrap()),
		headers: Some(official_headers.clone()),
		..FetchOptions::default()
	}).await;
	if riot_account.is_err() {
		return Err(ErrorCodes::RegionNotFound);
	}
	let riot_account = riot_account.unwrap();
	let riot_region = fetch::<ActiveShardsV1>(FetchOptions {
		url: format!("https://europe.api.riotgames.com/riot/account/v1/active-shards/by-game/val/by-puuid/{}", riot_account.data.puuid),
		headers: Some(official_headers),
		..FetchOptions::default()
	}).await;
	if riot_region.is_err() {
		return Err(ErrorCodes::RegionNotFound);
	}
	let riot_region = riot_region.unwrap();
	let region = riot_region.data.active_shard.to_string();
	let region = if ["br", "latam"].iter().any(|x| x == &region.to_lowercase()) { String::from("na") } else { region.to_lowercase() };
	Ok(region)
}

/// Get the current region for a user with improved reliability
/// This function uses the new reliable user data fetching and falls back to official API
pub async fn get_current_region_reliable(puuid: &str) -> Result<String, ErrorCodes> {
	// First try to get reliable user data
	match get_reliable_user_data(puuid.to_string()).await {
		Ok(reliable_data) => {
			// If confidence is high, use the region from reliable data
			if reliable_data.confidence_score > 0.7 {
				return Ok(reliable_data.region);
			}
			// Otherwise, use the reliable name/tag to get official region
			search_region_riot_official(Some(reliable_data.game_name), Some(reliable_data.tag_line), puuid.to_string()).await
		}
		Err(_) => {
			// Fallback to the original method
			search_region_riot_official(None, None, puuid.to_string()).await
		}
	}
}

/// Get reliable user name and tag with confidence scoring
/// Returns the most reliable GameName/TagLine combination across all regions
pub async fn get_reliable_user_name_tag(puuid: &str) -> Result<(String, String, f32), ErrorCodes> {
	let reliable_data = get_reliable_user_data(puuid.to_string()).await?;
	Ok((reliable_data.game_name, reliable_data.tag_line, reliable_data.confidence_score))
}

pub fn sanitize_name(name: &str) -> String {
	name.replace("$", "").trim().to_string()
}

pub async fn fetch_mmr_history_by_puuid(
	redis_client: MultiplexedConnection,
	puuid: &str,
	platform: &str,
	region: &str
) -> Result<RedisFetchResponse<CompetitiveUpdatesV1>, ErrorCodes> {
	let riot_headers = build_riot_headers(&RiotPlatforms::from_str(platform).unwrap()).await;
	let history = redis_fetch_ipv6::<CompetitiveUpdatesV1>(RedisFetchIPv6Options {
		url: format!("https://pd.{}.a.pvp.net/mmr/v1/players/{}/competitiveupdates?queue={}&startIndex=0&endIndex=20", region, puuid, if platform.to_lowercase() == "pc" {
			"competitive"
		} else {
			"console_competitive"
		}),
		headers: riot_headers,
		store: format!("mmr-history;v1;{};{};{};?queue=competitive", region, platform.to_lowercase(), puuid),
		redis_client: Some(redis_client),
		..RedisFetchIPv6Options::default()
	}).await;
	if history.is_err() {
		return Err(ErrorCodes::FetchingResource);
	}
	let history = history.unwrap();
	Ok(history)
}

pub async fn fetch_mmr_by_puuid(
	redis_client: MultiplexedConnection,
	puuid: &str,
	platform: &str,
	region: &str
) -> Result<RedisFetchResponse<PVPMMRPlayerV1>, ErrorCodes> {
	let riot_headers = build_riot_headers(&RiotPlatforms::from_str(platform).unwrap()).await;
	let player_data = redis_fetch_ipv6::<PVPMMRPlayerV1>(RedisFetchIPv6Options {
		url: format!("https://pd.{}.a.pvp.net/mmr/v1/players/{}", region, puuid),
		headers: riot_headers,
		store: format!("mmr-player;v1;{};{};{}", region, platform.to_lowercase(), puuid),
		redis_client: Some(redis_client),
		..RedisFetchIPv6Options::default()
	}).await;
	if player_data.is_err() {
		return Err(ErrorCodes::FetchingResource);
	}
	let player_data = player_data.unwrap();
	Ok(player_data)
}

pub async fn update_lifetime_mmr_history(client: &Client, puuid: &str, mmr_history: Vec<CompetitiveUpdateMatches>, platform: &str) {
	let mmr_history_lifetime = get_db::<LifetimeMMRHistory>(client, "mmr_history", None);
	for match_ in mmr_history {
		let map = get_map_by_asset(match_.map_id.as_str()).await;
		let value = LifetimeMMRHistory {
			m_id: match_.match_id.to_string(),
			p_id: puuid.to_string(),
			map_id: if map.is_some() {
				let map = map.unwrap();
				map.uuid
			} else {
				match_.map_id.clone()
			},
			s_id: if match_.season_id.is_some() {
				match_.season_id.unwrap().to_string()
			} else {
				String::from("0")
			},
			r: match_.ranked_rating_after_update as i32,
			m_c: match_.ranked_rating_earned,
			t: match_.tier_after_update as i32,
			d: (match_.match_start_time.timestamp_millis() / 1000) as i32,
			p: Some(platform.to_string()),
			r_a: Some(match_.ranked_rating_refund_applied),
			r_p: Some(match_.ranked_rating_performance_bonus as i32),
			w_d_p: Some(match_.was_derank_protected),
		};
		let _ = mmr_history_lifetime
			.update_one(doc! { "m_id": match_.match_id.to_string(), "p_id": puuid.to_string() }, doc! { "$setOnInsert": bson::to_document(&value).unwrap() })
			.upsert(true).await;
	}
}

pub async fn fetch_matches_by_puuid(
	redis_client: MultiplexedConnection,
	puuid: &str,
	region: &str,
	queries: &str,
	platform: &str
) -> Result<RedisFetchResponse<CompetitiveUpdatesV1>, ErrorCodes> {
	let riot_headers = build_riot_headers(&RiotPlatforms::from_str(platform).unwrap()).await;
	let matches = redis_fetch_ipv6::<CompetitiveUpdatesV1>(RedisFetchIPv6Options {
		headers: riot_headers,
		url: format!("https://pd.{}.a.pvp.net/mmr/v1/players/{}/competitiveupdates{}", region, puuid, queries),
		store: if queries.is_empty() {
			format!("mmr-history;v1;{};{};{}", region, platform, puuid)
		} else {
			format!("mmr-history;v1;{};{};{};{}", region, platform, puuid, queries)
		},
		redis_client: Some(redis_client),
		..RedisFetchIPv6Options::default()
	}).await;
	if matches.is_err() {
		return Err(ErrorCodes::FetchingResource);
	}
	let matches = matches.unwrap();
	Ok(matches)
}

pub async fn fetch_matches_history_by_puuid(
	redis_client: MultiplexedConnection,
	puuid: &str,
	region: &str,
	queries: &str,
	platform: &str
) -> Result<RedisFetchResponse<MatchHistoryV1>, ErrorCodes> {
	let riot_headers = build_riot_headers(&RiotPlatforms::from_str(platform).unwrap()).await;
	let matches = redis_fetch_ipv6::<MatchHistoryV1>(RedisFetchIPv6Options {
		headers: riot_headers,
		url: format!("https://pd.{}.a.pvp.net/match-history/v1/history/{}{}", region, puuid, queries),
		store: if queries.len() > 0 {
			format!("match-history;v1;{};{};{};{}", region, &platform, puuid, queries)
		} else {
			format!("match-history;v1;{};{};{}", region, &platform, puuid)
		},
		redis_client: Some(redis_client),
		..RedisFetchIPv6Options::default()
	}).await;
	if let Err(e) = matches {
		eprintln!("Error: {:?}", e.value_data);
		if e.status == 400 {
			if let Some(v) = e.value_data.get("errorCode") {
				if v.as_str().unwrap() == "INVALID_INDICES" {
					return Ok(RedisFetchResponse {
						status: e.status,
						headers: e.headers,
						url: e.url,
						data: MatchHistoryV1 {
							subject: puuid.to_string().parse().unwrap(),
							begin_index: 0,
							end_index: 0,
							total: 0,
							history: vec![],
						},
						ttl: 0,
						is_from_redis: false,
						pass_data: None,
					});
				}
			}
		}
		return Err(ErrorCodes::FetchingResource);
	}
	let matches = matches.unwrap();
	Ok(matches)
}

pub fn format_iso_date(date: DateTime) -> String {
	let chrono = chrono::DateTime::from_timestamp_millis(date.timestamp_millis()).unwrap();
	chrono.to_rfc3339()
}

pub fn format_old_api_date(date: DateTime) -> String {
	let chrono = chrono::DateTime::from_timestamp_millis(date.timestamp_millis()).unwrap();
	chrono.format("%A, %B %d, %Y %I:%M %p").to_string()
}

pub async fn is_old(season: &str) -> bool {
	let seasons = OFFICER_API_SEASONS.load_full();
	let season = seasons.iter().find(|i| i.uuid == season);
	if season.is_none() {
		return false;
	}
	let season = season.unwrap();
	let max_season = seasons.iter().find(|i| i.displayName == "EPISODE 5");
	if max_season.is_none() {
		return false;
	}
	let max_season = max_season.unwrap();
	return season.endTime.timestamp_millis() <= max_season.startTime.timestamp_millis();
}

pub fn query_string_builder(query: HashMap<String, String>) -> String {
	let mut query_string = String::new();
	for (key, value) in query.iter().enumerate() {
		if key == 0 {
			query_string.push_str("?");
		}
		query_string.push_str(&format!("{}={}&", value.0, value.1));
	}
	query_string.pop();
	query_string
}

pub fn deserialize_date<'de, D>(deserializer: D) -> Result<DateTime, D::Error> where D: Deserializer<'de> {
	let date_str = String::deserialize(deserializer);
	match date_str {
		Ok(date) => DateTime::parse_rfc3339_str(&date).map_err(serde::de::Error::custom),
		Err(v) => {
			error!("Error: {:?}", v);
			Err(serde::de::Error::custom("Invalid date"))
		}
	}
}

pub fn serialize_date<S>(date: &DateTime, serializer: S) -> Result<S::Ok, S::Error> where S: Serializer {
	let date_str = date.try_to_rfc3339_string();
	serializer.serialize_str(&date_str.unwrap())
}
