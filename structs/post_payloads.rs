use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize, Serialize)]
#[serde(untagged)]
pub enum RawV1PayloadValues {
    String(String),
    Vector(Vec<String>),
}

#[derive(Debug, Deserialize, Serialize)]
#[allow(non_snake_case)]
pub struct RawV1Payload {
    #[serde(rename = "type")]
    pub type_: String,
    pub value: RawV1PayloadValues,
    pub region: String,
    pub queries: Option<String>,
    pub platform: Option<String>,
}

#[derive(Debug, Deserialize, Serialize)]
#[allow(non_snake_case)]
pub struct AssistV1NamesPayload {
    pub puuids: Vec<String>,
}
