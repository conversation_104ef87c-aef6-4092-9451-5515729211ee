[package]
name = "cdn_server"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
actix-web = "4"
actix-multipart = "0.6"
tokio = "1"
serde = { version = "1", features = ["derive"] }
mime_guess = "2.0.4"
log = "0.4"
image = "0.25"
imageproc = "0.25.0"
env_logger = "0.10"
mail-send = "0.4"
futures = { version = "0.3", default-features = false }
serde_json = "1.0"
tiny-skia = "0.11.4"


[[bin]]
name = "cdn"
path = "main.rs"

[profile.release]
lto = "fat"
strip = true
codegen-units = 1
debug = false
opt-level = 3