use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TeamIconQuery};
use actix_web::{web, HttpResponse};
use std::str::FromStr;
use tiny_skia::{
    BlendMode, Color, FillRule, <PERSON>t, PathBuilder, Pixmap, Pixmap<PERSON>aint, Rect, Shader, Stroke,
    Transform,
};
//
// ───── CROSSHAIR CONFIG STRUCTS ─────────────────────────────────────────
// (same as before)
//

#[derive(Debug, Clone)]
pub struct GeneralConfig {
    pub advanced_options: bool,
    pub ads_use_primary: bool,
    pub overwrite_all_primary: bool,
    pub hide_on_fire: bool,
    pub follow_spectating: bool,
}

#[derive(Debug, <PERSON>lone)]
pub struct HexColor {
    pub enabled: bool,
    pub value: String, // 8 hex digits, e.g. "FFFFFFFF"
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Outlines {
    pub enabled: bool,
    pub width: u8,
    pub alpha: f32,
}

#[derive(Debug, Clone)]
pub struct Dot {
    pub enabled: bool,
    pub width: u8,
    pub alpha: f32,
}

#[derive(Debug, Clone)]
pub struct FireMul {
    pub enabled: bool,
    pub mul: u8,
}

#[derive(Debug, Clone)]
pub struct MoveMul {
    pub enabled: bool,
    pub mul: u8,
}

#[derive(Debug, Clone)]
pub struct VerticalSegment {
    pub enabled: bool,
    pub length: u8,
}

#[derive(Debug, Clone)]
pub struct Segment {
    pub enabled: bool,
    pub width: u8,
    pub length: u8,
    pub alpha: f32,
    pub offset: i32,
    pub fire_mul: FireMul,
    pub move_mul: MoveMul,
    pub vertical: Option<VerticalSegment>,
}

#[derive(Debug, Clone)]
pub struct PrimaryConfig {
    pub color: u8, // 0..8 (8 => "custom")
    pub use_custom_color: bool,
    pub hex_color: HexColor,
    pub outlines: Outlines,
    pub dot: Dot,
    pub overwrite_fire_mul: bool,
    pub inner: Segment,
    pub outer: Segment,
}

#[derive(Debug, Clone)]
pub struct SniperConfig {
    pub color: u8,
    pub use_custom_color: bool,
    pub hex_color: HexColor,
    pub dot: Dot,
}

#[derive(Debug, Clone)]
pub struct CrosshairConfig {
    pub general: GeneralConfig,
    pub primary: PrimaryConfig,
    pub ads: PrimaryConfig,
    pub sniper: SniperConfig,
}

// ─── Defaults ─────────────────────────────────────────────────────────

pub fn default_crosshair_config() -> CrosshairConfig {
    CrosshairConfig {
        general: GeneralConfig {
            advanced_options: false,
            ads_use_primary: true,
            overwrite_all_primary: false,
            hide_on_fire: false,
            follow_spectating: true,
        },
        primary: PrimaryConfig {
            color: 0,
            use_custom_color: false,
            hex_color: HexColor {
                enabled: false,
                value: "FFFFFFFF".to_string(),
            },
            outlines: Outlines {
                enabled: true,
                width: 1,
                alpha: 0.5,
            },
            dot: Dot {
                enabled: false,
                width: 2,
                alpha: 1.0,
            },
            overwrite_fire_mul: false,
            inner: Segment {
                enabled: true,
                width: 2,
                length: 6,
                alpha: 0.8,
                offset: 3,
                fire_mul: FireMul {
                    enabled: true,
                    mul: 1,
                },
                move_mul: MoveMul {
                    enabled: false,
                    mul: 1,
                },
                vertical: Some(VerticalSegment {
                    enabled: false,
                    length: 6,
                }),
            },
            outer: Segment {
                enabled: true,
                width: 2,
                length: 2,
                alpha: 0.35,
                offset: 10,
                fire_mul: FireMul {
                    enabled: true,
                    mul: 1,
                },
                move_mul: MoveMul {
                    enabled: true,
                    mul: 1,
                },
                vertical: Some(VerticalSegment {
                    enabled: false,
                    length: 2,
                }),
            },
        },
        ads: PrimaryConfig {
            color: 0,
            use_custom_color: false,
            hex_color: HexColor {
                enabled: false,
                value: "FFFFFFFF".to_string(),
            },
            outlines: Outlines {
                enabled: true,
                width: 1,
                alpha: 0.5,
            },
            dot: Dot {
                enabled: false,
                width: 2,
                alpha: 1.0,
            },
            overwrite_fire_mul: false,
            inner: Segment {
                enabled: true,
                width: 2,
                length: 6,
                alpha: 0.8,
                offset: 3,
                fire_mul: FireMul {
                    enabled: true,
                    mul: 1,
                },
                move_mul: MoveMul {
                    enabled: false,
                    mul: 1,
                },
                vertical: Some(VerticalSegment {
                    enabled: false,
                    length: 6,
                }),
            },
            outer: Segment {
                enabled: true,
                width: 2,
                length: 2,
                alpha: 0.35,
                offset: 10,
                fire_mul: FireMul {
                    enabled: true,
                    mul: 1,
                },
                move_mul: MoveMul {
                    enabled: true,
                    mul: 1,
                },
                vertical: Some(VerticalSegment {
                    enabled: false,
                    length: 2,
                }),
            },
        },
        sniper: SniperConfig {
            color: 7,
            use_custom_color: false,
            hex_color: HexColor {
                enabled: false,
                value: "FFFFFFFF".to_string(),
            },
            dot: Dot {
                enabled: true,
                width: 1,
                alpha: 0.75,
            },
        },
    }
}

// ─── Code Parsing (same as your JS logic) ─────────────────────────────

fn check_bounds(key: &str, value: f32, min: f32, max: f32) -> bool {
    if value < min || value > max {
        eprintln!("Ignoring out of bounds value: {}={}", key, value);
        false
    } else {
        true
    }
}

fn check_integer(key: &str, value: f32) -> bool {
    if value.floor() != value {
        eprintln!("Ignoring non-int value: {}={}", key, value);
        false
    } else {
        true
    }
}

fn update_config_from_code(config: &mut CrosshairConfig, group: &str, key: &str, value: f32) {
    let full_key = format!("{}:{}", group, key);
    match full_key.as_str() {
        // general config
        "0:p" => {
            if check_integer("0:p", value) && check_bounds("0:p", value, 0.0, 1.0) {
                config.general.ads_use_primary = value != 0.0;
            }
        }
        "0:c" => {
            if check_integer("0:c", value) && check_bounds("0:c", value, 0.0, 1.0) {
                config.general.overwrite_all_primary = value != 0.0;
            }
        }
        "0:s" => {
            if check_integer("0:s", value) && check_bounds("0:s", value, 0.0, 1.0) {
                config.general.advanced_options = value != 0.0;
            }
        }

        // primary color & outlines
        "P:c" => {
            if check_integer("P:c", value) && check_bounds("P:c", value, 0.0, 8.0) {
                config.primary.color = value as u8;
            }
        }
        "P:u" => {
            if check_integer("P:u", value) && check_bounds("P:u", value, 0.0, 4294967295.0) {
                config.primary.hex_color.value = format!("{:08X}", value as u32);
            }
        }
        "P:h" => {
            if check_integer("P:h", value) && check_bounds("P:h", value, 0.0, 1.0) {
                config.primary.outlines.enabled = value != 0.0;
            }
        }
        "P:t" => {
            if check_integer("P:t", value) && check_bounds("P:t", value, 1.0, 6.0) {
                config.primary.outlines.width = value as u8;
            }
        }
        "P:o" => {
            if check_bounds("P:o", value, 0.0, 1.0) {
                config.primary.outlines.alpha = value;
            }
        }

        // primary dot
        "P:d" => {
            if check_integer("P:d", value) && check_bounds("P:d", value, 0.0, 1.0) {
                config.primary.dot.enabled = value != 0.0;
            }
        }
        "P:b" => {
            if check_integer("P:b", value) && check_bounds("P:b", value, 0.0, 1.0) {
                config.primary.hex_color.enabled = value != 0.0;
            }
        }
        "P:z" => {
            if check_integer("P:z", value) && check_bounds("P:z", value, 1.0, 6.0) {
                config.primary.dot.width = value as u8;
            }
        }
        "P:a" => {
            if check_bounds("P:a", value, 0.0, 1.0) {
                config.primary.dot.alpha = value;
            }
        }

        // general booleans
        "P:f" => {
            if check_integer("P:f", value) && check_bounds("P:f", value, 0.0, 1.0) {
                config.general.hide_on_fire = value != 0.0;
            }
        }
        "P:s" => {
            if check_integer("P:s", value) && check_bounds("P:s", value, 0.0, 1.0) {
                config.general.follow_spectating = value != 0.0;
            }
        }

        // primary overwrite
        "P:m" => {
            if check_integer("P:m", value) && check_bounds("P:m", value, 0.0, 1.0) {
                config.primary.overwrite_fire_mul = value != 0.0;
            }
        }

        // primary inner
        "P:0b" => {
            if check_integer("P:0b", value) && check_bounds("P:0b", value, 0.0, 1.0) {
                config.primary.inner.enabled = value != 0.0;
            }
        }
        "P:0t" => {
            if check_integer("P:0t", value) && check_bounds("P:0t", value, 0.0, 10.0) {
                config.primary.inner.width = value as u8;
            }
        }
        "P:0l" => {
            if check_integer("P:0l", value) && check_bounds("P:0l", value, 0.0, 20.0) {
                config.primary.inner.length = value as u8;
            }
        }
        "P:0v" => {
            if check_integer("P:0v", value) && check_bounds("P:0v", value, 0.0, 20.0) {
                if let Some(ref mut vert) = config.primary.inner.vertical {
                    vert.length = value as u8;
                }
            }
        }
        "P:0g" => {
            if check_integer("P:0g", value) && check_bounds("P:0g", value, 0.0, 1.0) {
                if let Some(ref mut vert) = config.primary.inner.vertical {
                    vert.enabled = value != 0.0;
                }
            }
        }
        "P:0o" => {
            if check_integer("P:0o", value) && check_bounds("P:0o", value, 0.0, 20.0) {
                config.primary.inner.offset = value as i32;
            }
        }
        "P:0a" => {
            if check_bounds("P:0a", value, 0.0, 1.0) {
                config.primary.inner.alpha = value;
            }
        }
        "P:0m" => {
            if check_integer("P:0m", value) && check_bounds("P:0m", value, 0.0, 1.0) {
                config.primary.inner.move_mul.enabled = value != 0.0;
            }
        }
        "P:0f" => {
            if check_integer("P:0f", value) && check_bounds("P:0f", value, 0.0, 1.0) {
                config.primary.inner.fire_mul.enabled = value != 0.0;
            }
        }
        "P:0s" => {
            if check_bounds("P:0s", value, 0.0, 3.0) {
                config.primary.inner.move_mul.mul = value as u8;
            }
        }
        "P:0e" => {
            if check_bounds("P:0e", value, 0.0, 3.0) {
                config.primary.inner.fire_mul.mul = value as u8;
            }
        }

        // primary outer
        "P:1b" => {
            if check_integer("P:1b", value) && check_bounds("P:1b", value, 0.0, 1.0) {
                config.primary.outer.enabled = value != 0.0;
            }
        }
        "P:1t" => {
            if check_integer("P:1t", value) && check_bounds("P:1t", value, 0.0, 10.0) {
                config.primary.outer.width = value as u8;
            }
        }
        "P:1l" => {
            if check_integer("P:1l", value) && check_bounds("P:1l", value, 0.0, 10.0) {
                config.primary.outer.length = value as u8;
            }
        }
        "P:1v" => {
            if check_integer("P:1v", value) && check_bounds("P:1v", value, 0.0, 20.0) {
                if let Some(ref mut vert) = config.primary.outer.vertical {
                    vert.length = value as u8;
                }
            }
        }
        "P:1g" => {
            if check_integer("P:1g", value) && check_bounds("P:1g", value, 0.0, 1.0) {
                if let Some(ref mut vert) = config.primary.outer.vertical {
                    vert.enabled = value != 0.0;
                }
            }
        }
        "P:1o" => {
            if check_integer("P:1o", value) && check_bounds("P:1o", value, 0.0, 40.0) {
                config.primary.outer.offset = value as i32;
            }
        }
        "P:1a" => {
            if check_bounds("P:1a", value, 0.0, 1.0) {
                config.primary.outer.alpha = value;
            }
        }
        "P:1m" => {
            if check_integer("P:1m", value) && check_bounds("P:1m", value, 0.0, 1.0) {
                config.primary.outer.move_mul.enabled = value != 0.0;
            }
        }
        "P:1f" => {
            if check_integer("P:1f", value) && check_bounds("P:1f", value, 0.0, 1.0) {
                config.primary.outer.fire_mul.enabled = value != 0.0;
            }
        }
        "P:1s" => {
            if check_bounds("P:1s", value, 0.0, 3.0) {
                config.primary.outer.move_mul.mul = value as u8;
            }
        }
        "P:1e" => {
            if check_bounds("P:1e", value, 0.0, 3.0) {
                config.primary.outer.fire_mul.mul = value as u8;
            }
        }

        // ads color & outlines
        "A:c" => {
            if check_integer("A:c", value) && check_bounds("A:c", value, 0.0, 8.0) {
                config.ads.color = value as u8;
            }
        }
        "A:u" => {
            if check_integer("A:u", value) && check_bounds("A:u", value, 0.0, 4294967295.0) {
                config.ads.hex_color.value = format!("{:08X}", value as u32);
            }
        }
        "A:h" => {
            if check_integer("A:h", value) && check_bounds("A:h", value, 0.0, 1.0) {
                config.ads.outlines.enabled = value != 0.0;
            }
        }
        "A:t" => {
            if check_integer("A:t", value) && check_bounds("A:t", value, 1.0, 6.0) {
                config.ads.outlines.width = value as u8;
            }
        }
        "A:o" => {
            if check_bounds("A:o", value, 0.0, 1.0) {
                config.ads.outlines.alpha = value;
            }
        }

        // ads dot
        "A:d" => {
            if check_integer("A:d", value) && check_bounds("A:d", value, 0.0, 1.0) {
                config.ads.dot.enabled = value != 0.0;
            }
        }
        "A:b" => {
            if check_integer("A:b", value) && check_bounds("A:b", value, 0.0, 1.0) {
                config.ads.hex_color.enabled = value != 0.0;
            }
        }
        "A:z" => {
            if check_integer("A:z", value) && check_bounds("A:z", value, 1.0, 6.0) {
                config.ads.dot.width = value as u8;
            }
        }
        "A:a" => {
            if check_bounds("A:a", value, 0.0, 1.0) {
                config.ads.dot.alpha = value;
            }
        }
        "A:m" => {
            if check_integer("A:m", value) && check_bounds("A:m", value, 0.0, 1.0) {
                config.ads.overwrite_fire_mul = value != 0.0;
            }
        }

        // ads inner
        "A:0b" => {
            if check_integer("A:0b", value) && check_bounds("A:0b", value, 0.0, 1.0) {
                config.ads.inner.enabled = value != 0.0;
            }
        }
        "A:0t" => {
            if check_integer("A:0t", value) && check_bounds("A:0t", value, 0.0, 10.0) {
                config.ads.inner.width = value as u8;
            }
        }
        "A:0l" => {
            if check_integer("A:0l", value) && check_bounds("A:0l", value, 0.0, 20.0) {
                config.ads.inner.length = value as u8;
            }
        }
        "A:0v" => {
            if check_integer("A:0v", value) && check_bounds("A:0v", value, 0.0, 20.0) {
                if let Some(ref mut vert) = config.ads.inner.vertical {
                    vert.length = value as u8;
                }
            }
        }
        "A:0g" => {
            if check_integer("A:0g", value) && check_bounds("A:0g", value, 0.0, 1.0) {
                if let Some(ref mut vert) = config.ads.inner.vertical {
                    vert.enabled = value != 0.0;
                }
            }
        }
        "A:0o" => {
            if check_integer("A:0o", value) && check_bounds("A:0o", value, 0.0, 20.0) {
                config.ads.inner.offset = value as i32;
            }
        }
        "A:0a" => {
            if check_bounds("A:0a", value, 0.0, 1.0) {
                config.ads.inner.alpha = value;
            }
        }
        "A:0m" => {
            if check_integer("A:0m", value) && check_bounds("A:0m", value, 0.0, 1.0) {
                config.ads.inner.move_mul.enabled = value != 0.0;
            }
        }
        "A:0f" => {
            if check_integer("A:0f", value) && check_bounds("A:0f", value, 0.0, 1.0) {
                config.ads.inner.fire_mul.enabled = value != 0.0;
            }
        }
        "A:0s" => {
            if check_bounds("A:0s", value, 0.0, 3.0) {
                config.ads.inner.move_mul.mul = value as u8;
            }
        }
        "A:0e" => {
            if check_bounds("A:0e", value, 0.0, 3.0) {
                config.ads.inner.fire_mul.mul = value as u8;
            }
        }

        // ads outer
        "A:1b" => {
            if check_integer("A:1b", value) && check_bounds("A:1b", value, 0.0, 1.0) {
                config.ads.outer.enabled = value != 0.0;
            }
        }
        "A:1t" => {
            if check_integer("A:1t", value) && check_bounds("A:1t", value, 0.0, 10.0) {
                config.ads.outer.width = value as u8;
            }
        }
        "A:1l" => {
            if check_integer("A:1l", value) && check_bounds("A:1l", value, 0.0, 10.0) {
                config.ads.outer.length = value as u8;
            }
        }
        "A:1v" => {
            if check_integer("A:1v", value) && check_bounds("A:1v", value, 0.0, 20.0) {
                if let Some(ref mut vert) = config.ads.outer.vertical {
                    vert.length = value as u8;
                }
            }
        }
        "A:1g" => {
            if check_integer("A:1g", value) && check_bounds("A:1g", value, 0.0, 1.0) {
                if let Some(ref mut vert) = config.ads.outer.vertical {
                    vert.enabled = value != 0.0;
                }
            }
        }
        "A:1o" => {
            if check_integer("A:1o", value) && check_bounds("A:1o", value, 0.0, 40.0) {
                config.ads.outer.offset = value as i32;
            }
        }
        "A:1a" => {
            if check_bounds("A:1a", value, 0.0, 1.0) {
                config.ads.outer.alpha = value;
            }
        }
        "A:1m" => {
            if check_integer("A:1m", value) && check_bounds("A:1m", value, 0.0, 1.0) {
                config.ads.outer.move_mul.enabled = value != 0.0;
            }
        }
        "A:1f" => {
            if check_integer("A:1f", value) && check_bounds("A:1f", value, 0.0, 1.0) {
                config.ads.outer.fire_mul.enabled = value != 0.0;
            }
        }
        "A:1s" => {
            if check_bounds("A:1s", value, 0.0, 3.0) {
                config.ads.outer.move_mul.mul = value as u8;
            }
        }
        "A:1e" => {
            if check_bounds("A:1e", value, 0.0, 3.0) {
                config.ads.outer.fire_mul.mul = value as u8;
            }
        }

        // sniper
        "S:b" => {
            if check_integer("S:b", value) && check_bounds("S:b", value, 0.0, 1.0) {
                config.sniper.hex_color.enabled = value != 0.0;
            }
        }
        "S:c" => {
            if check_integer("S:c", value) && check_bounds("S:c", value, 0.0, 8.0) {
                config.sniper.color = value as u8;
            }
        }
        "S:t" => {
            if check_integer("S:t", value) && check_bounds("S:t", value, 0.0, 4294967295.0) {
                config.sniper.hex_color.value = format!("{:08X}", value as u32);
            }
        }
        "S:d" => {
            if check_integer("S:d", value) && check_bounds("S:d", value, 0.0, 1.0) {
                config.sniper.dot.enabled = value != 0.0;
            }
        }
        "S:s" => {
            if check_bounds("S:s", value, 0.0, 4.0) {
                config.sniper.dot.width = value as u8;
            }
        }
        "S:o" => {
            if check_bounds("S:o", value, 0.0, 1.0) {
                config.sniper.dot.alpha = value;
            }
        }

        // anything not recognized
        _ => eprintln!("Ignoring unmapped key: {}", full_key),
    }
}

pub fn convert_code_to_object(code: &str) -> CrosshairConfig {
    let mut config = default_crosshair_config();
    let tokens: Vec<&str> = code.split(';').collect();

    if tokens.len() <= 1 {
        return config;
    }

    let groups = ["P", "A", "S"];
    let mut current_group = "0";
    let mut i = 1;
    while i < tokens.len() {
        let token = tokens[i];
        if groups.contains(&token) {
            current_group = token;
            i += 1;
            continue;
        }
        if i + 1 >= tokens.len() {
            break;
        }
        let key = token;
        let value_str = tokens[i + 1];

        // Try parse hex if length=8 and all hex, else parse float
        let value_opt = if value_str.len() == 8 && value_str.chars().all(|c| c.is_ascii_hexdigit())
        {
            u32::from_str_radix(value_str, 16).ok().map(|v| v as f32)
        } else {
            f32::from_str(value_str).ok()
        };

        if let Some(value) = value_opt {
            update_config_from_code(&mut config, current_group, key, value);
        } else {
            eprintln!("Invalid value for key {}: {}", key, value_str);
        }
        i += 2;
    }
    config
}

// ─── Tiny-Skia Rendering ──────────────────────────────────────────────

/// We replicate your color palette from JS.
const COLOR_ARRAY: [&str; 8] = [
    "#ffffff", // 0
    "#00ff00", // 1
    "#7fff00", // 2
    "#dfff00", // 3
    "#ffff00", // 4
    "#00ffff", // 5
    "#ff00ff", // 6
    "#ff0000", // 7
];

/// Helper: parse "#RRGGBB" to tiny-skia `Color`.
fn parse_hex_color(s: &str) -> Color {
    if s.len() == 7 && s.starts_with('#') {
        let r = u8::from_str_radix(&s[1..3], 16).unwrap_or(255);
        let g = u8::from_str_radix(&s[3..5], 16).unwrap_or(255);
        let b = u8::from_str_radix(&s[5..7], 16).unwrap_or(255);
        Color::from_rgba8(r, g, b, 255)
    } else {
        // fallback white
        Color::from_rgba8(255, 255, 255, 255)
    }
}

/// Returns a Pixmap of size `requested_size` x `requested_size` with the crosshair drawn.
pub fn generate_crosshair_image(config: &CrosshairConfig, requested_size: u32) -> Pixmap {
    // Per JS, clamp size to 4096
    let size = requested_size.min(4096);
    let mut pixmap = Pixmap::new(size, size).unwrap();

    // Fill background if you want (optional).
    // By default, it’s transparent.  If you want black or something:
    // pixmap.fill(tiny_skia::Color::from_rgba8(0,0,0,255));

    // In the JS snippet:
    //   scaleFactor = canvasWidth / 128
    //   translation = -( (scaleFactor) - 1 ) * 64
    // Then: ctx.scale(scaleFactor, scaleFactor)
    //       ctx.translate(translation, translation)

    let scale_factor = size as f32 / 128.0;
    let half = (size / 2) as f32;
    let translation = -((scale_factor) - 1.0) * 64.0;

    // Build a transform that does S first, then T:
    // In JS, calls are: ctx.scale(...) -> S, then ctx.translate(...) -> T
    // so final coords are S * point + T. That means
    // we want the matrix M = T * S if we read transforms in left->right.
    // But tiny-skia’s `post_translate` multiplies from the right, so we do:
    //   Transform::from_scale(scale_factor, scale_factor)
    //       .post_translate(translation, translation)
    // to replicate the exact same final coordinate transform as JS.
    let transform = Transform::from_scale(scale_factor, scale_factor)
        .post_translate(half - (64.0 * scale_factor), half - (64.0 * scale_factor));

    // Decide if we’re using the primary or ads config
    let chosen = if config.general.ads_use_primary {
        &config.primary
    } else {
        &config.ads
    };

    // Determine the fill color from chosen.color
    let fill_color = if chosen.color == 8 {
        // custom => "#" + hexColor.value[..6]
        let hex = format!("#{}", &chosen.hex_color.value[..6]);
        parse_hex_color(&hex)
    } else {
        // from array
        if (chosen.color as usize) < COLOR_ARRAY.len() {
            parse_hex_color(COLOR_ARRAY[chosen.color as usize])
        } else {
            Color::from_rgba8(255, 255, 255, 255)
        }
    };

    // We'll follow your JS drawing order: inner -> dot -> outer
    // The “center” in JS is 64 for a 128x128 canvas
    let center = 64.0;

    // Outline info
    let outline_enabled = chosen.outlines.enabled;
    let outline_width = chosen.outlines.width as f32;
    let outline_alpha = chosen.outlines.alpha;

    // 1) draw inner
    if chosen.inner.enabled {
        draw_crosshair_segment(
            &mut pixmap,
            &chosen.inner,
            config.general.hide_on_fire,
            chosen.overwrite_fire_mul,
            fill_color,
            outline_enabled,
            outline_width,
            outline_alpha,
            center,
            transform,
        );
    }

    // 2) draw dot
    if chosen.dot.enabled {
        let dot_width = chosen.dot.width as f32;
        let alpha = chosen.dot.alpha;
        // in JS:
        //   let l = center - Math.ceil(dot_width / 2)
        let x = center - (dot_width / 2.0).ceil();
        let y = center - (dot_width / 2.0).ceil();

        draw_rect(
            &mut pixmap,
            x,
            y,
            dot_width,
            dot_width,
            color_with_alpha(fill_color, alpha),
            outline_enabled,
            outline_width,
            outline_alpha,
            transform,
        );
    }

    // 3) draw outer
    if chosen.outer.enabled {
        draw_crosshair_segment(
            &mut pixmap,
            &chosen.outer,
            config.general.hide_on_fire,
            chosen.overwrite_fire_mul,
            fill_color,
            outline_enabled,
            outline_width,
            outline_alpha,
            center,
            transform,
        );
    }

    pixmap
}

/// A small helper to draw the “inner” or “outer” segments (the arms),
/// replicating the logic from your JS snippet.
fn draw_crosshair_segment(
    pixmap: &mut Pixmap,
    seg: &Segment,
    hide_on_fire: bool,
    overwrite_fire_mul: bool,
    base_color: Color,
    outline_enabled: bool,
    outline_width: f32,
    outline_alpha: f32,
    center: f32,
    transform: Transform,
) {
    let width = seg.width as f32;
    let mut length = seg.length as f32;
    let alpha = seg.alpha;
    let mut offset = seg.offset as f32;

    // If segment.fire_mul.enabled && !overwrite_fire_mul => offset += 4
    if seg.fire_mul.enabled && !overwrite_fire_mul {
        offset += 4.0;
    }

    // The JS code’s “m = width % 2”
    let m = (width as i32) % 2;

    // Right segment
    //   x = center + offset
    //   y = floor(center - width/2)
    let right_x = center + offset;
    let right_y = (center - width / 2.0).floor();

    draw_rect(
        pixmap,
        right_x,
        right_y,
        length,
        width,
        color_with_alpha(base_color, alpha),
        outline_enabled,
        outline_width,
        outline_alpha,
        transform,
    );

    // Left segment
    //   x = center - offset - length - m
    //   y = floor(center - width/2)
    let left_x = center - offset - length - m as f32;
    let left_y = (center - width / 2.0).floor();

    draw_rect(
        pixmap,
        left_x,
        left_y,
        length,
        width,
        color_with_alpha(base_color, alpha),
        outline_enabled,
        outline_width,
        outline_alpha,
        transform,
    );

    // Check vertical
    if let Some(ref v) = seg.vertical {
        if v.enabled {
            length = v.length as f32;
        }
    }

    // Bottom
    //   x = floor(center - width/2)
    //   y = center + offset
    let bottom_x = (center - width / 2.0).floor();
    let bottom_y = center + offset;

    draw_rect(
        pixmap,
        bottom_x,
        bottom_y,
        width,
        length,
        color_with_alpha(base_color, alpha),
        outline_enabled,
        outline_width,
        outline_alpha,
        transform,
    );

    // Top is skipped if hide_on_fire is true
    if !hide_on_fire {
        let top_x = (center - width / 2.0).floor();
        let top_y = center - offset - length - m as f32;

        draw_rect(
            pixmap,
            top_x,
            top_y,
            width,
            length,
            color_with_alpha(base_color, alpha),
            outline_enabled,
            outline_width,
            outline_alpha,
            transform,
        );
    }
}

fn color_with_alpha(color: Color, alpha_mult: f32) -> Color {
    let (r, g, b, a) = (color.red(), color.green(), color.blue(), color.alpha()); // (f32, f32, f32, f32)
    let new_a = a * alpha_mult;
    // from_rgba() returns Option<Color>, so we handle an error with unwrap_or fallback
    Color::from_rgba(r, g, b, new_a).unwrap_or(color)
}
/// Draws a rectangle at (x,y) with w,h using tiny-skia.
/// Applies your scale/translate transform to replicate the JS code's `ctx.scale(...)`, `ctx.translate(...)`.
fn draw_rect(
    pixmap: &mut Pixmap,
    x: f32,
    y: f32,
    w: f32,
    h: f32,
    fill_color: Color,
    outline_enabled: bool,
    outline_width: f32,
    outline_alpha: f32,
    transform: Transform,
) {
    // 1) Fill the original rect
    let mut pb = PathBuilder::new();
    pb.move_to(x, y);
    pb.line_to(x + w, y);
    pb.line_to(x + w, y + h);
    pb.line_to(x, y + h);
    pb.close();
    if let Some(path) = pb.finish() {
        // fill
        let mut fill_paint = Paint::default();
        fill_paint.shader = Shader::SolidColor(fill_color);
        pixmap.fill_path(&path, &fill_paint, FillRule::Winding, transform, None);

        // 2) If outlines, build an “expanded” path
        if outline_enabled && outline_width > 0.0 {
            let expand_xy = 0.5 * outline_width;
            let expand_wh = outline_width;

            let mut outline_pb = PathBuilder::new();
            outline_pb.move_to(x - expand_xy, y - expand_xy);
            outline_pb.line_to(x - expand_xy + w + expand_wh, y - expand_xy);
            outline_pb.line_to(x - expand_xy + w + expand_wh, y - expand_xy + h + expand_wh);
            outline_pb.line_to(x - expand_xy, y - expand_xy + h + expand_wh);
            outline_pb.close();
            if let Some(outline_path) = outline_pb.finish() {
                let mut stroke_paint = Paint::default();
                stroke_paint.shader = Shader::SolidColor(
                    Color::from_rgba(0.0, 0.0, 0.0, outline_alpha).unwrap_or(Color::BLACK),
                );
                let mut stroke = Stroke::default();
                stroke.width = outline_width;
                // stroke.line_join = LineJoin::Miter; // if you want exact matching
                pixmap.stroke_path(&outline_path, &stroke_paint, &stroke, transform, None);
            }
        }
    }
}

// Web Request handler (code -> object -> image)
pub async fn handle_request(
    path: web::Path<CrosshairPath>,
    query: web::Query<CrosshairQuery>,
) -> HttpResponse {
    let code = path.code.clone();
    // Pass size, set default to 256 but max to 4096
    let size = query.size.unwrap_or(256).min(4096);
    let config = convert_code_to_object(&code);
    let pixmap = generate_crosshair_image(&config, size);
    let image_data = pixmap.encode_png().unwrap();

    HttpResponse::Ok()
        .content_type("image/png")
        .body(image_data)
}
