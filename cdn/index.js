import {fastify as f} from 'fastify';
import {existsSync, readdirSync, readFileSync, renameSync, writeFileSync} from 'fs';
import {createClient} from 'redis';
import Jimp from 'jimp';
import replaceColor from 'replace-multiple-color';
import nodemailer from 'nodemailer';

const basedata = JSON.parse(readFileSync('../basedata.json'));
const redis = createClient({url: basedata.redis});
await redis.connect().catch(e => console.error(e));
redis.on('error', err => console.log('Redis Client Error', err));
redis.on('ready', () => console.log('Redis Ready'));
redis.on('connect', () => console.log('Redis Ready'));
const fastify = f({
    logger: {
        level: 'error',
    },
    bodyLimit: 25000000,
});
const errors = {
    codes: {
        4: 'Invalid Hex Code',
    },
    own: {
        404: 'Route Not found',
        500: 'Internal Server Error',
        503: 'Service Unavailable',
        429: 'Rate Limited',
        1015: 'Rate Limited',
        403: 'Forbidden',
        401: 'Unauthorized',
        400: 'Bad Request',
        410: 'Gone',
        409: 'Conflict',
        408: 'Timeout',
        501: 'API Version not implemented',
    },
};

fastify.register(import('@fastify/multipart'), {});
fastify.register(import('@fastify/cors'));
fastify.setNotFoundHandler(async (req, res) => {
    return errorhandler({status: 404, res, errors: [{instance: 'own'}]});
});
fastify.setErrorHandler(async (error, req, res) => {
    console.log(error);
    errorhandler({status: 500, res, errors: [{instance: 'own', details: error.toString()}]});
});

const errorhandlerbuilder = ({status, instance, details = null, message, global = false, code = 0} = {}) => {
    if (status === 429) {
        return {
            message: message ?? errors[instance][status],
            code,
            global,
        };
    }
    return {
        message: message ?? code != 0 ? errors.codes[code] : errors[instance][status],
        code,
        details: typeof details == 'object' || Array.isArray(details) ? JSON.stringify(details) : details,
    };
};
const errorhandler = ({res, status, errors} = {}) => {
    return res.code(status).send({status, errors: errors.map(i => errorhandlerbuilder({status, ...i}))});
};
const fix = async () => {
    const targetColor = {r: 0, g: 0, b: 0, a: 255}; // Color you want to replace
    const replaceColor = {r: 0, g: 0, b: 0, a: 0}; // Color you want to replace with
    const colorDistance = (c1, c2) => Math.sqrt(Math.pow(c1.r - c2.r, 2) + Math.pow(c1.g - c2.g, 2) + Math.pow(c1.b - c2.b, 2) + Math.pow(c1.a - c2.a, 2)); // Distance between two colors
    const threshold = 32; // Replace colors under this threshold. The smaller the number, the more specific it is.
    await Jimp.read(`G:/FModel/Output/Textures/Test/Sniper_Gold_Featured_T.png`).then(image => {
        image.scan(0, 0, image.bitmap.width, image.bitmap.height, (x, y, idx) => {
            const thisColor = {
                r: image.bitmap.data[idx + 0],
                g: image.bitmap.data[idx + 1],
                b: image.bitmap.data[idx + 2],
                a: image.bitmap.data[idx + 3],
            };
            if (colorDistance(targetColor, thisColor) <= threshold) {
                image.bitmap.data[idx + 0] = replaceColor.r;
                image.bitmap.data[idx + 1] = replaceColor.g;
                image.bitmap.data[idx + 2] = replaceColor.b;
                image.bitmap.data[idx + 3] = replaceColor.a;
            }
        });
        image.write(`G:/FModel/Output/Textures/Test/Sniper_Gold_Featured_T.png`);
    });
};
const hexToRgb = (hexCode, array = false) => {
    hexCode = hexCode.replace('#', '');
    const r = parseInt(hexCode.substring(0, 2), 16);
    const g = parseInt(hexCode.substring(2, 4), 16);
    const b = parseInt(hexCode.substring(4, 6), 16);
    const a = 255;

    if (array) return [r, g, b, a];
    return {r, g, b, a};
};
const rgbToHex = (r, g, b) => {
    const hexR = r.toString(16).padStart(2, '0');
    const hexG = g.toString(16).padStart(2, '0');
    const hexB = b.toString(16).padStart(2, '0');
    return `#${hexR}${hexG}${hexB}`;
};

const darkenHexColor = hexCode => {
    const {r, g, b} = hexToRgb(hexCode);

    const darkenedR = Math.round(r * 0.75);
    const darkenedG = Math.round(g * 0.75);
    const darkenedB = Math.round(b * 0.75);

    const darkenedHex = rgbToHex(darkenedR, darkenedG, darkenedB);
    return darkenedHex;
};

const convertToUUID = str => {
    return str.replace(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/, '$1-$2-$3-$4-$5');
};

const patch_premier_team_icons = () => {
    const jsons = readdirSync('./files/valorant/premier/team-icons/json').filter(i => i.includes('DataAsset'));
    for (let json of jsons) {
        const split_path = json.split('_');
        const data_asset = JSON.parse(readFileSync(`./files/valorant/premier/team-icons/json/${json}`));
        const ui_asset_path = data_asset.find(i => i.Properties).Properties.UIData.AssetPathName.split('/');
        const ui_asset = JSON.parse(readFileSync(`./files/valorant/premier/team-icons/json/${ui_asset_path[ui_asset_path.length - 1].split('.')[0]}.json`));
        const asset_uuid = data_asset.find(i => i.Properties).Properties.Uuid;
        const file_name = ui_asset
            .find(i => i.Properties)
            .Properties.DisplayIcon.ObjectName.replaceAll('Texture2D', '')
            .replaceAll("'", '')
        if (!existsSync(`./files/valorant/premier/team-icons/images/${file_name}.png`)) continue;
        renameSync(
            `./files/valorant/premier/team-icons/images/${file_name}.png`,
            `./files/valorant/premier/team-icons/images/${convertToUUID(asset_uuid.replaceAll('-', '')).toLowerCase()}.png`
        );
    }
    console.log('[CDN] Premier Team Icons Patch Done');
};

const patch_premier_conferences = () => {
    const jsons = readdirSync('./files/valorant/premier/conferences/json').filter(i => i.includes('DataAsset'));
    for (let json of jsons) {
        const split_path = json.split('_');
        const data_asset = JSON.parse(readFileSync(`./files/valorant/premier/conferences/json/${json}`));

        const asset = data_asset.find(i => i.Properties).Properties;
        const file_name = asset.Icon.ObjectName.split("'")[1].replaceAll("'", '');
        console.log(asset.Icon);
        console.log(!existsSync(`./files/valorant/premier/conferences/images/${file_name}.png`));
        console.log(`./files/valorant/premier/conferences/images/${file_name}.png`);

        if (!existsSync(`./files/valorant/premier/conferences/images/${file_name}.png`)) continue;
        renameSync(
            `./files/valorant/premier/conferences/images/${file_name}.png`,
            `./files/valorant/premier/conferences/images/${convertToUUID(asset.Uuid.replaceAll('-', '')).toLowerCase()}.png`
        );
    }
    console.log('[CDN] Premier Conferences Patch Done');
};

const areColorsWithinPercentRange = (color1, color2, percentage = 0.15) => {
    const rgb1 = hexToRgb(color1);
    const rgb2 = hexToRgb(color2);

    const maxDifference = percentage * 255;

    const diffR = Math.abs(rgb1.r - rgb2.r);
    const diffG = Math.abs(rgb1.g - rgb2.g);
    const diffB = Math.abs(rgb1.b - rgb2.b);

    return diffR <= maxDifference && diffG <= maxDifference && diffB <= maxDifference;
};

const repaintImage = async (image, colors) => {
    let edited_image = await Jimp.read(image);
    edited_image.scan(0, 0, edited_image.bitmap.width, edited_image.bitmap.height, (x, y, idx) => {
        colors.forEach(color => {
            const targetColor = color.targetColor;
            let replaceColor = color.replaceColor;
            const currentColor = rgbToHex(edited_image.bitmap.data[idx], edited_image.bitmap.data[idx + 1], edited_image.bitmap.data[idx + 2]);

            if (areColorsWithinPercentRange(currentColor, targetColor)) {
                replaceColor = hexToRgb(replaceColor, true);
                edited_image.bitmap.data[idx] = replaceColor[0];
                edited_image.bitmap.data[idx + 1] = replaceColor[1];
                edited_image.bitmap.data[idx + 2] = replaceColor[2];
                if (replaceColor[3] !== null) edited_image.bitmap.data[idx + 3] = replaceColor[3];
            }
        });
    });
    return edited_image.getBufferAsync('image/png');
};

fastify.get('/valorant/v1/premier/team-icon/:id', async (req, res) => {
    if (!existsSync(`./files/valorant/premier/team-icons/images/${req.params.id}.png`))
        return errorhandler({
            res,
            status: 404,
            errors: [{instance: 'own'}],
        });
    const colors = [];
    const hex_regex = /^([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    console.log(req.query);
    let redis_string = `cdn;valorant;premier;team-icon;`;
    if (req.query.primary && hex_regex.test(req.query.primary))
        colors.push(
            {target: '#cb0000', replace: darkenHexColor(req.query.primary)},
            {target: '#ff0000', replace: `#${req.query.primary}`},
            {
                target: '#80090d',
                replace: `#${req.query.primary}`,
            },
            {target: '#ff3e08', replace: `#${req.query.primary}`}
        );
    redis_string += `${req.query.primary};`;
    if (req.query.secondary && hex_regex.test(req.query.secondary)) {
        colors.push(
            {target: '#00ce00', replace: darkenHexColor(req.query.secondary)},
            {target: '#00ff00', replace: `#${req.query.secondary}`},
            {target: '#00c01e', replace: `#${req.query.secondary}`},
            {target: '#008408', replace: `#${req.query.secondary}`},
            {target: '#005500', replace: `#${req.query.secondary}`}
        );
        redis_string += `${req.query.secondary};`;
    }
    if (req.query.tertiary && hex_regex.test(req.query.tertiary)) {
        colors.push(
            {target: '#0000ff', replace: `#${req.query.tertiary}`},
            {target: '#000997', replace: `#${req.query.tertiary}`},
            {target: '#00004c', replace: `#${req.query.tertiary}`},
            {target: '#0000d6', replace: `#${req.query.tertiary}`}
        );
        redis_string += `${req.query.tertiary}`;
    }
    const redis_fetch = null; /*await redis.get(redis_string)*/
    const image = redis_fetch
        ? Buffer.from(redis_fetch, 'base64')
        : await repaintImage(
            `./files/valorant/premier/team-icons/images/${req.params.id}.png`,
            colors.map(i => {
                return {
                    targetColor: i.target,
                    replaceColor: i.replace,
                };
            })
        );
    console.log(image);
    //if (!redis_fetch) redis.set(redis_string, image.toString('base64'), {EX: 2628000});
    res.code(200).type('image/png').send(image);
});

fastify.get('/valorant/v1/premier/conference/:id', async (req, res) => {
    if (!existsSync(`./files/valorant/premier/conferences/images/${req.params.id}.png`))
        return errorhandler({
            res,
            status: 404,
            errors: [{instance: 'own'}],
        });
    res.code(200)
        .type('image/png')
        .send(readFileSync(`./files/valorant/premier/conferences/images/${req.params.id}.png`));
});

fastify.post('/happy_dogs/v1/mitglied', async (req, res) => {
    const parts = req.files();
    const attachments = [];
    for await (const part of parts) {
        attachments.push({
            filename: part.filename,
            content: await part.toBuffer(),
        });
    }
    const connection = encodeURI('smtp://<EMAIL>:<EMAIL>');
    const transporter = nodemailer.createTransport(connection);
    const mailOptions = {
        from: '"Mitglieds Antrag Server" <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Neuer Mitgliedsantrag',
        text: 'Im Anhang findest du den Mitgliederantrag',
        attachments,
    };

    transporter.sendMail(mailOptions, function (err, info) {
        if (err) res.code(500).send({error: true});
        console.log('Message sent: ' + info.response);
        res.code(200).send({error: false});
    });
});

fastify.listen({port: 3602}, (err, address) => {
    if (err) throw err;
    patch_premier_team_icons();
    patch_premier_conferences();
    console.log('[CDN] Online');
});
