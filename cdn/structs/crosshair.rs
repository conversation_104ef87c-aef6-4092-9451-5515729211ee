use serde::{Deserialize, Serialize};

// <PERSON>hair config structure
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct CrosshairConfig {
    pub general: General,
    pub primary: CrosshairSettings,
    pub ads: CrosshairSettings,
    pub sniper: SniperSettings,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct General {
    pub advanced_options: bool,
    pub ads_use_primary: bool,
    pub overwrite_all_primary: bool,
    pub hide_on_fire: i32,
    pub follow_spectating: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CrosshairSettings {
    pub color: i32,
    pub use_custom_color: bool,
    pub hex_color: HexColor,
    pub outlines: Outlines,
    pub dot: Dot,
    pub overwrite_fire_mul: bool,
    pub inner: LineSettings,
    pub outer: LineSettings,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HexColor {
    pub enabled: bool,
    pub value: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Outlines {
    pub enabled: bool,
    pub width: i32,
    pub alpha: f32,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Dot {
    pub enabled: bool,
    pub width: i32,
    pub alpha: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LineSettings {
    pub enabled: bool,
    pub width: i32,
    pub length: i32,
    pub vertical: VerticalLine,
    pub offset: i32,
    pub alpha: f32,
    pub move_mul: Multiplier,
    pub fire_mul: Multiplier,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerticalLine {
    pub enabled: bool,
    pub length: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Multiplier {
    pub enabled: bool,
    pub mul: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SniperSettings {
    pub color: i32,
    pub use_custom_color: bool,
    pub hex_color: HexColor,
    pub dot: Dot,
}
