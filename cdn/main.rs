mod structs;
mod utils;

use crate::utils::crosshair::{ convert_code_to_object, generate_crosshair_image, handle_request };
use actix_multipart::Multipart;
use actix_web::{ http, middleware, web, App, HttpResponse, HttpServer };
use env_logger;
use futures::StreamExt;
use image::{ codecs::png::PngEncoder, ExtendedColorType, GenericImage, GenericImageView, ImageEncoder, Rgba };
use log::info;
use mail_send::{ mail_builder::MessageBuilder, SmtpClientBuilder };
use serde::{ Deserialize, Serialize };
use std::fs;

//Web API
#[derive(Deserialize)]
struct TeamIconPath {
	id: String,
}

#[derive(Deserialize)]
struct TeamIconQuery {
	primary: Option<String>,
	secondary: Option<String>,
	tertiary: Option<String>,
}

#[derive(Deserialize)]
struct CrosshairPath {
	code: String,
}

#[derive(Deserialize)]
struct CrosshairQuery {
	size: Option<u32>,
}

#[derive(Deserialize)]
struct ValLocresPath {
	locale: String,
}

//Error Handling
#[derive(Serialize)]
pub struct APIError {
	code: u16,
	message: String,
	pub status: u16,
	details: Option<serde_json::Value>,
}

#[derive(Serialize)]
pub struct SendError {
	pub errors: Vec<APIError>,
}

pub enum ErrorCodes {
	Base404,
	FileNotFound,
	InvalidFile,
	InternalError,
	InvalidLocaleContent,
}

pub fn get_error(error: &ErrorCodes) -> APIError {
	match error {
		ErrorCodes::Base404 =>
			APIError {
				status: 404,
				code: 0,
				message: String::from("Route not found"),
				details: None,
			},
		ErrorCodes::FileNotFound =>
			APIError {
				status: 404,
				code: 1,
				message: String::from("File not found"),
				details: None,
			},
		ErrorCodes::InvalidFile =>
			APIError {
				code: 2,
				message: String::from("Invalid File, make sure your File is not corrupted"),
				status: 400,
				details: None,
			},
		ErrorCodes::InvalidLocaleContent =>
			APIError {
				code: 3,
				message: String::from("Invalid locale"),
				status: 400,
				details: Some(serde_json::to_value(VALORANT_CONTENT_LOCALS).unwrap()),
			},
		ErrorCodes::InternalError =>
			APIError {
				code: 0,
				message: String::from("Internal Error"),
				status: 500,
				details: None,
			},
		_ =>
			APIError {
				code: 0,
				message: String::from("Internal Error"),
				status: 500,
				details: None,
			},
	}
}

fn error_handler(errors: Vec<ErrorCodes>) -> HttpResponse {
	let mut obj = SendError { errors: vec![] };
	for x in errors.iter() {
		obj.errors.push(get_error(x));
	}
	let status_code: http::StatusCode = http::StatusCode
		::from_u16(match obj.errors.first() {
			None => 500,
			Some(v) => v.status,
		})
		.unwrap();
	HttpResponse::build(status_code).json(obj)
}

//Functionality
#[derive(Debug)]
struct ColorMaps {
	target: Rgba<u8>,
	replacement: Rgba<u8>,
}

fn is_color_within_tolerance(a: Rgba<u8>, b: Rgba<u8>) -> bool {
	let tolerance = (255.0 * 15.0) / 100.0;
	((a[0] as i32) - (b[0] as i32)).abs() <= (tolerance as i32) &&
		((a[1] as i32) - (b[1] as i32)).abs() <= (tolerance as i32) &&
		((a[2] as i32) - (b[2] as i32)).abs() <= (tolerance as i32)
}

fn darken_color(color: Rgba<u8>) -> Rgba<u8> {
	let new_r = ((color[0] as f32) * 0.8) as u8;
	let new_g = ((color[1] as f32) * 0.8) as u8;
	let new_b = ((color[2] as f32) * 0.8) as u8;
	Rgba([new_r, new_g, new_b, 255])
}

fn hex_to_rgba(hex: &str) -> Rgba<u8> {
	let r = u8::from_str_radix(&hex[0..2], 16).unwrap();
	let g = u8::from_str_radix(&hex[2..4], 16).unwrap();
	let b = u8::from_str_radix(&hex[4..6], 16).unwrap();
	Rgba([r, g, b, 255])
}

const VALORANT_CONTENT_LOCALS: [&str; 1] = ["en-US"];

//Routes
#[derive(Serialize)]
pub struct ResponseHappyDogs {
	pub success: bool,
}

async fn get_team_icon(path: web::Path<TeamIconPath>, query: web::Query<TeamIconQuery>) -> HttpResponse {
	let image = match image::open(format!("./files/valorant/premier/team-icons/images/{}.png", path.id)) {
		Ok(v) => v,
		Err(_v) => {
			return error_handler(vec![ErrorCodes::FileNotFound]);
		}
	};
	let mut modified_image = image::DynamicImage::new_rgba8(image.width(), image.height());
	let mut target_colors: Vec<ColorMaps> = vec![];
	if query.primary.is_some() {
		target_colors.push(ColorMaps {
			target: Rgba([170, 0, 0, 255]),
			replacement: darken_color(hex_to_rgba(query.primary.as_deref().unwrap())),
		});
		target_colors.push(ColorMaps {
			target: Rgba([90, 0, 5, 255]),
			replacement: hex_to_rgba(query.primary.as_deref().unwrap()),
		});
		target_colors.push(ColorMaps {
			target: Rgba([255, 0, 0, 255]),
			replacement: hex_to_rgba(query.primary.as_deref().unwrap()),
		});
	}
	if query.secondary.is_some() {
		target_colors.push(ColorMaps {
			target: Rgba([0, 159, 0, 255]),
			replacement: darken_color(hex_to_rgba(query.secondary.as_deref().unwrap())),
		});
		target_colors.push(ColorMaps {
			target: Rgba([0, 231, 0, 255]),
			replacement: darken_color(hex_to_rgba(query.secondary.as_deref().unwrap())),
		});
		target_colors.push(ColorMaps {
			target: Rgba([0, 110, 16, 255]),
			replacement: darken_color(hex_to_rgba(query.secondary.as_deref().unwrap())),
		});
		target_colors.push(ColorMaps {
			target: Rgba([0, 85, 5, 255]),
			replacement: darken_color(hex_to_rgba(query.secondary.as_deref().unwrap())),
		});
		target_colors.push(ColorMaps {
			target: Rgba([0, 255, 0, 255]),
			replacement: hex_to_rgba(query.secondary.as_deref().unwrap()),
		});
	}
	if query.tertiary.is_some() {
		target_colors.push(ColorMaps {
			target: Rgba([0, 0, 255, 255]),
			replacement: hex_to_rgba(query.tertiary.as_deref().unwrap()),
		});
		target_colors.push(ColorMaps {
			target: Rgba([0, 0, 200, 255]),
			replacement: hex_to_rgba(query.tertiary.as_deref().unwrap()),
		});
		target_colors.push(ColorMaps {
			target: Rgba([0, 0, 150, 255]),
			replacement: hex_to_rgba(query.tertiary.as_deref().unwrap()),
		});
	}
	for (x, y, pixel) in image.pixels() {
		let change_colors: Vec<&ColorMaps> = target_colors
			.iter()
			.filter(|color| is_color_within_tolerance(pixel, color.target))
			.collect();
		if !change_colors.is_empty() {
			for color in change_colors {
				modified_image.put_pixel(x, y, color.replacement);
			}
		} else {
			modified_image.put_pixel(x, y, pixel);
		}
	}
	let mut buffer: Vec<u8> = Vec::new();
	PngEncoder::new(&mut buffer).write_image(&modified_image.as_bytes(), modified_image.width(), modified_image.height(), ExtendedColorType::Rgba8).unwrap();
	HttpResponse::Ok().content_type("image/png").body(buffer)
}

async fn get_conference(path: web::Path<TeamIconPath>) -> HttpResponse {
	let file_content = fs::read(format!("./files/valorant/premier/conferences/images/{}.png", path.id));
	match file_content {
		Ok(v) => HttpResponse::Ok().content_type("image/png").body(v),
		Err(_e) => error_handler(vec![ErrorCodes::FileNotFound]),
	}
}

async fn happy_dogs(mut payload: Multipart) -> HttpResponse {
	while let Some(item) = payload.next().await {
		let mut bytes: Vec<u8> = Vec::new();
		if item.is_err() {
			return error_handler(vec![ErrorCodes::InvalidFile]);
		}
		let mut field = item.unwrap();

		while let Some(chunk) = field.next().await {
			let data = chunk.unwrap();
			bytes.extend_from_slice(data.as_ref());
		}

		let content_type = field.content_type().unwrap();

		let message = MessageBuilder::new()
			.from(("Mitglieds Antrag Server", "<EMAIL>"))
			.to("<EMAIL>")
			.subject("Neuer Mitgliedsantrag!")
			.text_body("Im Anhang findest du den Mitgliederantrag.")
			.attachment(content_type.to_string(), field.name(), bytes);

		let send = SmtpClientBuilder::new("mail.your-server.de", 465)
			.credentials(("<EMAIL>", "4pI76FG4mS630VxU"))
			.connect().await
			.unwrap()
			.send(message).await;

		return match send {
			Ok(_) => HttpResponse::Ok().json(ResponseHappyDogs { success: true }),
			Err(_) => error_handler(vec![ErrorCodes::InternalError]),
		};
	}
	error_handler(vec![ErrorCodes::InternalError])
}

async fn get_val_locres(path: web::Path<ValLocresPath>) -> HttpResponse {
	if !VALORANT_CONTENT_LOCALS.iter().any(|x| x.to_lowercase() == path.locale.to_lowercase()) {
		return error_handler(vec![ErrorCodes::InvalidLocaleContent]);
	}
	let file_content = fs::read(format!("./files/valorant/locres/{}.json", path.locale.to_lowercase()));
	match file_content {
		Ok(v) => HttpResponse::Ok().content_type("application/json").body(v),
		Err(_e) => error_handler(vec![ErrorCodes::FileNotFound]),
	}
}

async fn default() -> HttpResponse {
	error_handler(vec![ErrorCodes::Base404])
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
	env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));
	info!("CDN Online");

	HttpServer::new(|| {
		App::new()
			.wrap(middleware::DefaultHeaders::new().add(("X-Version", "v3.1")).add(("Access-Control-Allow-Origin", "*")))
			.default_service(web::to(default))
			.route("/valorant/v1/premier/team-icon/{id}", web::get().to(get_team_icon))
			.route("/valorant/v1/premier/conference/{id}", web::get().to(get_conference))
			.route("/valorant/v1/crosshair/{code}", web::get().to(handle_request))
			.route("/valorant/v1/locres/{locale}", web::get().to(get_val_locres))
			.route("/happy_dogs/v1/mitglied", web::post().to(happy_dogs))
	})
		.bind(("127.0.0.1", 60002))?
		.run().await
}
