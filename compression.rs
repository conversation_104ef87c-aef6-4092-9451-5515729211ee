use brotli::CompressorWriter;
use std::fs;
use std::io::{Read, Write};
use std::path::Path;
use zstd::stream::encode_all;

fn main() -> std::io::Result<()> {
    let path = Path::new("62225ab2-16f6-472c-83aa-d7a5e24f7b8a.json");

    // Load JSON file from disk
    let input = fs::read_to_string(&path)?;
    let input_bytes = input.as_bytes();

    // Brotli compression
    // Time tracking for Brotli compression
    let start_time = std::time::Instant::now();
    let mut brotli_buffer = Vec::new();
    {
        let mut compressor = CompressorWriter::new(&mut brotli_buffer, 4096, 11, 22);
        compressor.write_all(input_bytes)?;
    }
    let elapsed_time = start_time.elapsed();
    println!("Brotli compression took: {:?}", elapsed_time);

    // Zstd compression
    // Time tracking for Zstd compression
    let start_time = std::time::Instant::now();
    let zstd_buffer = encode_all(input_bytes, 15).unwrap();
    let elapsed_time = start_time.elapsed();
    println!("Zstd compression took: {:?}", elapsed_time);

    println!("Original size: {} bytes", input_bytes.len());
    println!("Brotli compressed: {} bytes", brotli_buffer.len());
    println!("Zstd compressed: {} bytes", zstd_buffer.len());

    // Save compressed files to disk
    fs::write("compressed.brotli", &brotli_buffer)?;
    fs::write("compressed.zstd", &zstd_buffer)?;

    // Read compressed files
    read_compressed_files()?;
    Ok(())
}

fn read_compressed_files() -> std::io::Result<()> {
    // Read the compressed files from disk
    let brotli_compressed = fs::read("compressed.brotli")?;
    let zstd_compressed = fs::read("compressed.zstd")?;

    // Decompress the files
    // Time tracking for Brotli decompression
    let start_time = std::time::Instant::now();
    let mut brotli_decompressed = Vec::new();
    {
        let mut decompressor = brotli::Decompressor::new(&brotli_compressed[..], 4096);
        decompressor.read_to_end(&mut brotli_decompressed)?;
    }
    let elapsed_time = start_time.elapsed();
    println!("Brotli decompression took: {:?}", elapsed_time);

    // Time tracking for Zstd decompression
    let start_time = std::time::Instant::now();
    let zstd_decompressed = zstd::stream::decode_all(&zstd_compressed[..])?;
    let elapsed_time = start_time.elapsed();
    println!("Zstd decompression took: {:?}", elapsed_time);

    // Save decompressed files to disk
    fs::write("decompressed.brotli.json", &brotli_decompressed)?;
    fs::write("decompressed.zstd.json", &zstd_decompressed)?;

    Ok(())
}
