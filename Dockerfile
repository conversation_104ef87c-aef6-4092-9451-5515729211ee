# syntax=docker/dockerfile:1

FROM rust:latest as builder

WORKDIR /app

RUN apt-get update && apt-get install -y pkg-config git libssl-dev && rm -rf /var/lib/apt/lists/*
RUN git config --global credential.helper store \
 && mkdir -p ~/.cargo \
 && echo '[net]\ngit-fetch-with-cli = true' >> ~/.cargo/config.toml

ARG GIT_CREDENTIALS
RUN git config --global url."https://henrik-3:${GIT_CREDENTIALS}@gitlab.com/".insteadOf "https://gitlab.com/"


COPY Cargo.toml Cargo.lock ./
RUN cargo fetch

COPY . .
RUN cargo clean
RUN cargo build --release
RUN ls -lh /app/target/release

FROM ubuntu:24.04

RUN useradd -m appuser
RUN apt-get update && apt-get install -y libssl3 ca-certificates && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/release/api_server /usr/local/bin/api_server

RUN chmod +x /usr/local/bin/api_server
USER appuser

# Entrypoint uses environment variables like RIOT_CLIENT_SECRET at runtime
CMD ["api_server"]