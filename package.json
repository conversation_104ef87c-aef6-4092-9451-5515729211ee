{"name": "henrikdev-api", "version": "2.2.0-beta.2", "description": "henrikdev backend API", "type": "module", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@liamcottle/valorant.js": "^1.4.0", "axios": "^0.24.0", "axios-retry": "^3.2.5", "cheerio": "^1.0.0-rc.11", "discord.js": "^13.8.0", "fast-xml-parser": "^3.21.1", "fastify": "^4.3.0", "moment": "^2.29.3", "momentjs": "^2.0.0", "mongodb": "^4.7.0", "node-cron": "^3.0.1", "pm2": "^5.2.0", "promise-socket": "^7.0.0", "randomatic": "^3.1.1", "webuntis": "^1.20.1"}}