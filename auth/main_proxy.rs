use arl::RateLimiter;
use cacache::Integrity;
use headers::Authorization;
use hyper_proxy::{Intercept, Proxy};
use lazy_static::lazy_static;
use log::{debug, error, info, warn};
use once_cell::sync::Lazy;
use std::fmt::Display;
use std::str::FromStr;
use mongodb::bson::doc;
use mongodb::Client;
use mongodb::options::ClientOptions;
use time::{Duration, OffsetDateTime};
use tokio::time::sleep;
use uuid::Uuid;
use valorant_api::enums::region::Region;
use valorant_api::errors::response_error::RequestError;
use valorant_api::utils::credentials_manager::CredentialsManager;
use valorant_api::utils::network::http_client::{ProxyHttpClient, SimpleHttpClient};
use dotenv::dotenv;


lazy_static! {
    static ref USERNAME: String = std::env::var("RIOT_USERNAME").ok().unwrap();
    static ref PASSWORD: String = std::env::var("RIOT_PASSWORD").ok().unwrap();
    static ref AUTH_DIR: String = std::env::var("AUTH_DIR").unwrap_or("./auth".to_string());
    static ref AUTH_KEY: String = std::env::var("AUTH_KEY").unwrap_or("auth".to_string());
    static ref REAUTH_INTERVAL: Duration = Duration::minutes(45);
    static ref VERIFY_INTERVAL: Duration = Duration::minutes(5);
    static ref SLEEP_BETWEEN_REQUESTS: std::time::Duration = std::time::Duration::from_secs(3);
}

static CLIENT: Lazy<ProxyHttpClient> = Lazy::new(|| {
    let proxy_uri = std::env::var("PROXY_URL").unwrap().parse().unwrap();
    /*let username = std::env::var("PROXY_USERNAME").unwrap();
    let password = std::env::var("PROXY_PASSWORD").unwrap();
    let mut proxy = reqwest::Proxy::https(proxy_uri).unwrap().basic_auth(&*USERNAME, &*PASSWORD);*/
    let mut proxy = Proxy::new(Intercept::All, proxy_uri);
    let username = std::env::var("PROXY_USERNAME").unwrap();
    let password = std::env::var("PROXY_PASSWORD").unwrap();
    info!("Proxy username: {}", username);
    info!("Proxy password: {}", password);
    let auth = Authorization::basic(&username, &password);
    proxy.set_authorization(auth);
    proxy.force_connect();

    ProxyHttpClient::new(proxy).unwrap()
});

pub struct DbAuthVal {
    pub access: String,
    pub entitlement: String,
    pub source: String,
}

static SIMPLE_CLIENT: Lazy<SimpleHttpClient> = Lazy::new(|| SimpleHttpClient::new().unwrap());

#[tokio::main]
async fn main() {
    dotenv().ok();
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("debug"));
    println!("[MONGO_DB] Init MongoDB");
    let mut client_options = ClientOptions::parse(std::env::var("MONGOURI").unwrap().as_str())
        .await
        .expect("failed to parse options");
    let client = Client::with_options(client_options)
        .expect("failed to connect");
    println!("[MONGO_DB] Connected");
    sleep(*SLEEP_BETWEEN_REQUESTS).await;

    let mut credentials_manager = match read_auth().await {
        Ok(mut c) => {
            info!("Successfully read auth from cache");
            if let Some(expires_at) = c.exires_at {
                if OffsetDateTime::now_utc() < expires_at {
                    info!("Auth expires at: {}", expires_at);
                    refresh_auth(&mut c, &client).await
                } else {
                    warn!("Auth expired");
                    do_initial_auth(&client).await
                }
            } else {
                do_initial_auth(&client).await
            }
        }
        Err(e) => {
            info!("No auth found in cache: {}", e);
            do_initial_auth(&client).await
        }
    };

    let mut next_refresh = match credentials_manager.exires_at {
        Some(expires_at) => expires_at - Duration::minutes(15),
        None => OffsetDateTime::now_utc() + *REAUTH_INTERVAL,
    };
    let mut last_verify = std::time::Instant::now();
    let mut changed = true;

    loop {
        if OffsetDateTime::now_utc() > next_refresh {
            credentials_manager = refresh_auth(&mut credentials_manager, &client).await;
            next_refresh = OffsetDateTime::now_utc() + *REAUTH_INTERVAL;
            changed = true;
        }
        if last_verify.elapsed() > *VERIFY_INTERVAL {
            match verify_auth(&credentials_manager).await {
                Ok(_) => debug!("Successfully verified"),
                Err(e) => {
                    error!("Failed to verify: {}", e);
                    warn!("Trying to refresh auth");
                    credentials_manager = refresh_auth(&mut credentials_manager, &client).await;
                    changed = true;
                }
            };
            last_verify = std::time::Instant::now();
        }
        if changed {
            match write_auth(&credentials_manager).await {
                Ok(_) => {
                    debug!("Successfully wrote to cache");
                    changed = false;
                }
                Err(e) => error!("Failed to write to cache: {}", e),
            }
        }
        sleep(std::time::Duration::from_secs(60)).await;
    }
}

async fn do_initial_auth(client: &Client) -> CredentialsManager {
    let limiter = RateLimiter::new(1, std::time::Duration::from_secs(1));
    let limiter2 = RateLimiter::new(15, std::time::Duration::from_secs(60));
    loop {
        limiter.wait().await;
        limiter2.wait().await;
        sleep(*SLEEP_BETWEEN_REQUESTS).await;
        match do_initial_auth_once().await {
            Ok(c) => {
                info!("Successfully authenticated");
                let access = c.access_token.clone().unwrap();
                let entitlement = c.entitlements_token.clone().unwrap();
                client.database("API").collection::<DbAuthVal>("auth").update_one(doc! {"source": "val"}, doc! {"$set": doc! {"access": access, "entitlement": entitlement}}, None).await.unwrap();


                match verify_auth(&c).await {
                    Ok(_) => {
                        info!("Successfully verified");
                        return c;
                    }
                    Err(e) => {
                        error!("Failed to verify: {}", e);
                        continue;
                    }
                }
            }
            Err(e) => {
                error!("Failed to authenticate: {}", e);
            }
        }
    }
}

async fn do_initial_auth_once() -> Result<CredentialsManager, RequestError> {
    info!("Try to do initial auth for {}", *USERNAME);
    let mut credentials_manager = CredentialsManager::new();
    credentials_manager
        .authenticate(&*CLIENT, &USERNAME, &PASSWORD)
        .await?;
    Ok(credentials_manager)
}

async fn refresh_auth(credentials_manager: &mut CredentialsManager, client: &Client) -> CredentialsManager {
    match refresh_auth_once(credentials_manager).await {
        Ok(_) => {
            info!("Successfully refreshed auth");
            match verify_auth(credentials_manager).await {
                Ok(_) => {
                    info!("Successfully verified refreshed auth");
                    let access = credentials_manager.access_token.clone().unwrap();
                    let entitlement = credentials_manager.entitlements_token.clone().unwrap();
                    client.database("API").collection::<DbAuthVal>("auth").update_one(doc! {"source": "val"}, doc! {"$set": doc! {"access": access, "entitlement": entitlement}}, None).await.unwrap();
                    credentials_manager.clone()
                }
                Err(e) => {
                    error!("Failed to verify: {}", e);
                    do_initial_auth(&client).await
                }
            }
        }
        Err(e) => {
            error!("Failed to refresh auth: {}", e);
            do_initial_auth(&client).await
        }
    }
}

async fn refresh_auth_once(
    credentials_manager: &mut CredentialsManager,
) -> Result<(), RequestError> {
    credentials_manager
        .refresh_access_token(&*CLIENT, true)
        .await
}

async fn verify_auth(credentials_manager: &CredentialsManager) -> Result<(), RequestError> {
    info!("Try to verify auth");
    valorant_api::get_mmr_details_v1(
        credentials_manager,
        &*SIMPLE_CLIENT,
        Region::EU,
        &Uuid::from_str("ee89b4d9-13d0-5832-8dd7-eb5d8806d918").unwrap(),
    )
        .await
        .map(|_| ())
}

#[derive(Debug)]
pub(crate) enum CacheError {
    Read(cacache::Error),
    Write(cacache::Error),
    Parse(serde_json::Error),
}

impl Display for CacheError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CacheError::Write(e) => write!(f, "Failed to write to cache: {}", e),
            CacheError::Parse(e) => write!(f, "Failed to parse cache: {}", e),
            CacheError::Read(e) => write!(f, "Failed to read from cache: {}", e),
        }
    }
}

async fn write_auth(credentials_manager: &CredentialsManager) -> Result<Integrity, CacheError> {
    debug!("Writing credentials manager to cache");
    cacache::write(
        AUTH_DIR.as_str(),
        AUTH_KEY.as_str(),
        serde_json::to_string(&credentials_manager).map_err(CacheError::Parse)?,
    )
        .await
        .map_err(CacheError::Write)
}

async fn read_auth() -> Result<CredentialsManager, CacheError> {
    debug!("Reading auth from cache");
    let auth = cacache::read(AUTH_DIR.as_str(), AUTH_KEY.as_str())
        .await
        .map_err(CacheError::Read)?;
    serde_json::from_slice(&auth).map_err(CacheError::Parse)
}
