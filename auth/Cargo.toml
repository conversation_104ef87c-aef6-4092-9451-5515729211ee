[package]
name = "auth"
version = "0.2.0"
edition = "2021"

[dependencies]
tokio = { version = "1.37.0", features = ["macros", "rt-multi-thread", "process"] }
mongodb = "2"
serde_json = "1.0.117"
serde = { version = "1.0.201", features = ["serde_derive"] }
valorant-assets-api = "0.1"
dotenv = "0.15.0"
valorant_api = { version = "0.2" }
# valorant_api = { git = "https://valorant_api:<EMAIL>/valorant-api/rust-valorant-api.git" }
# valorant_api = { path = "valorant_api", features = ["proxy"] }
hyper-proxy = "0.9.1"
headers = "0.3.9"
arl = "0.2.0"
lazy_static = "1.4.0"
log = "0.4.21"
once_cell = "1.19.0"
time = "0.3.36"
uuid = { version = "1.8.0", features = ["serde"] }
env_logger = { version = "0.11.3", features = [] }
cacache = "13.0.0"
reqwest = { version = "0.12.4", features = ["json"] }
chrono = "0.4.38"

[[bin]]
name = "auth"
path = "main.rs"