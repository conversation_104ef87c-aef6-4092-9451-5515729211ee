mod main;

use std::collections::HashMap;
use log::{error, info};
use mongodb::options::ClientOptions;
use mongodb::Client;
use rand::Rng;
use reqwest::header::{HeaderMap, HeaderValue};
use serde::de::DeserializeOwned;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use tokio::time::Instant;
use valorant_assets_api::models::version::Version;
use valorant_assets_api::version::get_version;
use dotenv::dotenv;


#[derive(Debug, Clone)]
pub struct FetchResponse<T> {
    pub status: u16,
    pub headers: HeaderMap,
    pub url: String,
    pub data: T,
}

#[derive(Debug, Clone)]
pub struct FetchError {
    pub status: u16,
    pub headers: HeaderMap,
    pub url: String,
    pub error: String,
    pub error_type: String,
    pub data: Option<String>,
}

#[derive(Debug, <PERSON>lone)]
pub struct FetchOptions {
    pub url: String,
    pub method: String,
    pub headers: Option<HeaderMap>,
    pub user_agent: String,
    pub data: Value,
    pub is_text: bool,
}

impl Default for FetchOptions {
    fn default() -> Self {
        FetchOptions {
            url: String::from(""),
            method: String::from("GET"),
            headers: Some(HeaderMap::new()),
            user_agent: String::from("RiotClient/4.0.0"),
            data: Value::Null,
            is_text: false,
        }
    }
}

#[tokio::main]
async fn main() {
    dotenv().ok();
    info!("[MONGO_DB] Init MongoDB");
    let mut client_options = ClientOptions::parse(std::env::var("MONGOURI").unwrap().as_str())
        .await
        .expect("failed to parse options");
    client_options.max_pool_size = Some(100);
    let client = Client::with_options(client_options)
        .expect("failed to connect");
    info!("[MONGO_DB] Connected");
    auth().await;
}

pub async fn fetch<T: DeserializeOwned + std::fmt::Debug + Clone>(
    options: FetchOptions
) -> Result<FetchResponse<T>, FetchError> {
    if cfg!(debug_assertions) {
        info!("[DEBUG] Fetching: {}", options.url);
    }
    let mut http_client = reqwest::Client::builder();
    http_client = http_client.user_agent(options.user_agent);
    let g_headers = if options.headers.is_some() {
        let headers = options.headers.unwrap();
        Some(headers)
    } else { Some(HeaderMap::new()) };
    http_client = http_client.default_headers(g_headers.unwrap());
    let http_client_build = match http_client.build() {
        Ok(v) => v,
        Err(e) => {
            error!("[FETCH] Error: {:?}", e);
            return Err(FetchError {
                status: 500,
                headers: HeaderMap::new(),
                url: options.url,
                error: String::from("Error while building HTTP Client"),
                error_type: String::from("HTTP_CLIENT"),
                data: None,
            });
        }
    };
    let body = match options.method.as_str() {
        "GET" => http_client_build.get(options.url.clone()).send().await,
        "POST" => http_client_build.post(options.url.clone()).json(&options.data).send().await,
        "PUT" => http_client_build.put(options.url.clone()).json(&options.data).send().await,
        "DELETE" => http_client_build.delete(options.url.clone()).send().await,
        _ => http_client_build.get(options.url.clone()).send().await,
    };
    return match body {
        Ok(v) => {
            let status = v.status().as_u16();
            let headers = v.headers().clone();
            let url = v.url().to_string();
            let text = v.text().await.unwrap();
            let decode = serde_json::from_str::<T>(&text);
            if decode.is_err() {
                let deserializer = &mut serde_json::Deserializer::from_str(&text);
                let error = serde_path_to_error::deserialize::<_, T>(deserializer).err();
                let path = error.as_ref().unwrap().path().to_string();
                if cfg!(debug_assertions) {
                    error!("[DEBUG] Error Decoding: {:?}", &url);
                    error!("[DEBUG] Error Decoding: {:?}", &path);
                };
                return Err(FetchError {
                    status,
                    headers: HeaderMap::new(),
                    url: url.clone(),
                    error: String::from("Error while parsing JSON"),
                    error_type: String::from("PARSE_JSON"),
                    data: Some(path),
                });
            }
            let response = FetchResponse {
                status,
                headers,
                url,
                data: decode.unwrap(),
            };
            Ok(response)
        }
        Err(e) => {
            error!("[FETCH] Error: {:?}", e);
            Err(FetchError {
                status: 500,
                headers: HeaderMap::new(),
                url: options.url.clone(),
                error: e.to_string(),
                error_type: String::from("HTTP_CLIENT"),
                data: None,
            })
        }
    };
}

async fn auth() {
    info!("[AUTH] Auth service started");
    let timeout = std::time::Duration::from_secs(60 * 60);
    let reqwest_client = reqwest::Client::new();
    let version = get_version(&reqwest_client).await;
    if version.is_err() {
        info!("[AUTH] Failed to get version");
        tokio::time::sleep(tokio::time::Duration::from_secs(60)).await;
    }
    let version = version.unwrap();
    info!("[AUTH] Version: {:?}", version);

    info!("[AUTH] Create session");
    let session = create_session(None, version.clone()).await;
    if session.is_err() {
        info!("[AUTH] Failed to create session");
        error!("{:?}", session.clone().err().unwrap());
        tokio::time::sleep(tokio::time::Duration::from_secs(60)).await;
        return;
    }
    let session = session.unwrap();
    info!("[AUTH] Session created: {:?}", session.data);

    let asid_cookie = find_asid_cookie(&session.headers);
    info!("[AUTH] Login");
    let login = login(asid_cookie, std::env::var("USERNAME").unwrap(), std::env::var("PASSWORD").unwrap(), version.clone()).await;
}

fn find_asid_cookie(headers: &HeaderMap) -> Option<String> {
    headers.get("set-cookie").and_then(|value| {
        value.to_str().ok()?.split(',').find_map(|cookie| {
            if cookie.trim_start().starts_with("asid") {
                Some(cookie.to_string())
            } else {
                None
            }
        })
    })
}

async fn login(cookie: Option<String>, username: String, pw: String, version: Version) -> Result<FetchResponse<Value>, FetchError> {
    info!("[AUTH] Logging in");
    let mut headers = reqwest::header::HeaderMap::new();
    if let Some(cookie) = cookie {
        headers.insert(
            reqwest::header::COOKIE,
            reqwest::header::HeaderValue::from_str(cookie.as_str()).unwrap(),
        );
    }
    headers.insert(
        reqwest::header::USER_AGENT,
        reqwest::header::HeaderValue::from_static("RiotClient/4.0.0"),
    );
    let mut rng = rand::thread_rng();
    let n1: u8 = rng.gen();
    fetch::<Value>(
        FetchOptions {
            url: "https://auth.riotgames.com/api/v1/authorization".to_string(),
            method: "PUT".to_string(),
            headers: Some(headers),
            user_agent: format!("RiotClient/{} rso-authenticator (Windows;10;;Professional, x64)", version.build_version),
            data: serde_json::json!({
                "language": "en_US",
                "password": pw,
                "region": null,
                "remember": false,
                "type": "auth",
                "username": username,
            }),
            is_text: false,
            ..Default::default()
        }
    ).await
}

async fn create_session(cookie: Option<String>, version: Version) -> Result<FetchResponse<Value>, FetchError> {
    info!("[AUTH] Creating session");

    let mut headers = reqwest::header::HeaderMap::new();
    if let Some(cookie) = cookie {
        headers.insert(
            reqwest::header::COOKIE,
            reqwest::header::HeaderValue::from_str(cookie.as_str()).unwrap(),
        );
    }
    let riot_client = format!("RiotClient/{} rso-authenticator (Windows;10;;Professional, x64)", version.build_version.clone());
    let mut rng = rand::thread_rng();
    let n1: u8 = rng.gen();
    fetch::<Value>(
        FetchOptions {
            url: "https://auth.riotgames.com/api/v1/authorization".to_string(),
            method: "POST".to_string(),
            headers: Some(headers),
            user_agent: riot_client,
            data: serde_json::json!({
                "acr_values": "",
                "claims": "",
                "client_id": "riot-client",
                "code_challenge": "",
                "code_challenge_method": "",
                "nonce": n1,
                "redirect_uri": "http://localhost/redirect",
                "response_type": "token id_token",
                "scope": "account openid offline_access link id offline",
            }),
            is_text: false,
            ..Default::default()
        }
    ).await
}