mod main;

use arl::RateLimiter;
use headers::Authorization;
use dotenv::dotenv;
use mongodb::Client;
use futures::{TryFutureExt, TryStreamExt};
use mongodb::bson::doc;
use mongodb::options::ClientOptions;
use tokio::time::sleep;
use valorant_api::errors::response_error::RequestError;
use valorant_api::utils::credentials_manager::CredentialsManager;
use valorant_api::utils::network::http_client::{HttpClient, HttpClientError, SimpleHttpClient, ProxyHttpClient};

pub struct DbAuthVal {
    pub access: String,
    pub entitlement: String,
    pub source: String,
}

#[tokio::main]
async fn main() {
    dotenv().ok();
    println!("[MONGO_DB] Init MongoDB");
    let mut client_options = ClientOptions::parse(std::env::var("MONGOURI").unwrap().as_str())
        .await
        .expect("failed to parse options");
    let client = Client::with_options(client_options)
        .expect("failed to connect");
    println!("[MONGO_DB] Connected");
    let mut manager = do_initial_auth(&client).await;
    loop {
        sleep(std::time::Duration::from_secs(60 * 50)).await;
        manager = refresh_auth(manager.clone(), &client).await;
    }
}

async fn refresh_auth(mut credentials_manager: CredentialsManager, client: &Client) -> CredentialsManager {
    /* let mut proxy = hyper_proxy::Proxy::new(hyper_proxy::Intercept::All, "http://pr.oxylabs.io:7777".parse().unwrap());
     proxy.set_authorization(Authorization::basic(&*proxy_username.clone(), &*proxy_pw.clone()));
     let http_client = ProxyHttpClient::new(proxy).unwrap();*/
    let http_client = SimpleHttpClient::new().unwrap();
    match credentials_manager.refresh_access_token(&http_client, true).await {
        Ok(_) => {
            println!("[REFRESH] Authenticated");
            println!("{:?}", credentials_manager);
            let access = credentials_manager.access_token.clone();
            let entitlement = credentials_manager.entitlements_token.clone();
            client.database("API").collection::<DbAuthVal>("auth").update_one(doc! {"source": "val"}, doc! {"$set": doc! {"access": access.unwrap(), "entitlement": entitlement.unwrap()}}, None).await.unwrap();
            return credentials_manager;
        }
        Err(e) => {
            println!("[REFRESH] Error: {:?}", e);
            do_initial_auth(client).await
        }
    }
}

async fn do_initial_auth_once() -> Result<CredentialsManager, RequestError> {
    let proxy_username = std::env::var("PROXY_USERNAME").unwrap();
    let proxy_pw = std::env::var("PROXY_PASSWORD").unwrap();
    let riot_username = std::env::var("RIOT_USERNAME").unwrap();
    let riot_pw = std::env::var("RIOT_PASSWORD").unwrap();

    /* let mut proxy = hyper_proxy::Proxy::new(hyper_proxy::Intercept::All, "http://pr.oxylabs.io:7777".parse().unwrap());
     proxy.set_authorization(Authorization::basic(&*proxy_username.clone(), &*proxy_pw.clone()));
     let http_client = ProxyHttpClient::new(proxy).unwrap();*/
    let http_client = SimpleHttpClient::new().unwrap();
    let mut credentials_manager = valorant_api::utils::credentials_manager::CredentialsManager::new();
    credentials_manager.authenticate(&http_client, &*riot_username.clone(), &*riot_pw.clone()).await?;
    Ok(credentials_manager)
}

async fn do_initial_auth(client: &Client) -> CredentialsManager {
    let limiter = RateLimiter::new(1, std::time::Duration::from_secs(1));
    let limiter2 = RateLimiter::new(15, std::time::Duration::from_secs(60));
    let mut counter = 0;
    loop {
        limiter.wait().await;
        limiter2.wait().await;
        match do_initial_auth_once().await {
            Ok(credentials_manager) => {
                println!("[AUTH] Authenticated");
                println!("{:?}", credentials_manager);
                let access = credentials_manager.access_token.clone();
                let entitlement = credentials_manager.entitlements_token.clone();
                client.database("API").collection::<DbAuthVal>("auth").update_one(doc! {"source": "val"}, doc! {"$set": doc! {"access": access.unwrap(), "entitlement": entitlement.unwrap()}}, None).await.unwrap();
                return credentials_manager;
            }
            Err(e) => {
                println!("[AUTH] Error: {:?}", e);
            }
        }
        println!("Attempt: {}", counter);
        counter += 1;
    }
}