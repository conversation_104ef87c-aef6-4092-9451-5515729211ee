import axios from 'axios';
import {Agent} from 'https';
import {randomUUID} from 'crypto';

let client_version;
let client_build;
let client_sdk;
let user_agent;
const USERNAME = 'HenrikAPI3';
const PASSWORD = 'Duisburg02@';
const CAPSOLVER_API_KEY = 'CAP-0D93BAC7AFD962B108E6CC6A496D22A3';

const ciphers = ['TLS_CHACHA20_POLY1305_SHA256', 'TLS_AES_128_CCM_SHA256', 'TLS_AES_256_GCM_SHA384', 'TLS_EDCHE_ECDSA_CHACHA20_POLY1305_SHA256'];
// agent with custom cipher suites
const agent = new Agent({
    ciphers: ciphers,
    honorCipherOrder: true,
    minVersion: 'TLSv1.2',
});

const main = async () => {
    await fetch_valorant_version();
    let first = await create_auth_first().catch(e => e);
    console.log('[AUTH FIRST]', first.data ?? first.response);
    let cookies = first.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ');
    console.log('[COOKIES]', cookies);
    let session = await create_session().catch(e => e);
    console.log('[SESSION]', session.data ?? session.response);
    console.log('[SESSION CAPTCHA]', session.data.captcha.hcaptcha);
    //Get cookies
    cookies += `; ${session.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ')}`;
    let captcha = await create_hcaptcha_task(session.data.captcha.hcaptcha.key, session.data.captcha.hcaptcha.data).catch(e => e);
    console.log('[CAPTCHA SET]', captcha.data ?? captcha.response);
    console.log('[CAPTCHA SLEEP]...');
    await sleep(10000);
    let captcha_response = await get_hcaptcha_response(captcha.data.taskId).catch(e => e);
    while (captcha_response.error) {
        captcha = await create_hcaptcha_task(session.data.captcha.hcaptcha.key, session.data.captcha.hcaptcha.data).catch(e => e);
        console.log('[CAPTCHA SET]', captcha);
        console.log('[CAPTCHA SLEEP]...');
        await sleep(10000);
        captcha_response = await get_hcaptcha_response(captcha.data.taskId).catch(e => e);
    }
    console.log('[CAPTCHA RESPONSE]', captcha_response.data ?? captcha_response);
    console.log(cookies);
    let put_captcha = await put_hcaptcha_to_riot(captcha_response.data.solution.gRecaptchaResponse, cookies, captcha_response.data.solution.userAgent).catch(e => e);
    cookies += `; ${put_captcha.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ')}`;
    console.log('[PUT CAPTCHA]', put_captcha.data ?? put_captcha.response);
    let login_cookies = await get_login_cookies(put_captcha.data.success.login_token, cookies).catch(e => e);
    console.log('[LOGIN COOKIES]', login_cookies.data ?? login_cookies.response);
    cookies += `; ${login_cookies.headers['set-cookie'].map(cookie => cookie.split(';')[0]).join('; ')}`;
    console.log('[NEW COOKIES]', cookies);
    let auth_data = await get_auth_data(cookies).catch(e => e);
    console.log('[AUTH DATA]', auth_data.data ?? auth_data.response);
};

const fetch_valorant_version = async () => {
    let version = await axios.get('https://valorant-api.com/v1/version').catch(e => e);
    if (!version.data) console.error('[ERROR] Failed to fetch Valorant version');
    client_version = version.data.data.riotClientVersion;
    client_build = version.data.data.riotClientBuild;
    client_sdk = version.data.data.riotClientBuild.split('.')[1];
    user_agent = `RiotClient/${client_build} rso-authenticator (Windows;10;;Professional, x64)`;
};

const create_hcaptcha_task = async (key, rqdata) =>
    axios({
        method: 'POST',
        url: 'https://api.capsolver.com/createTask',
        data: {
            clientKey: CAPSOLVER_API_KEY,
            task: {
                type: 'HCaptchaTaskProxyLess',
                websiteKey: key,
                websiteURL: 'https://authenticate.riotgames.com/api/v1/login',
                enterprisePayload: {
                    rqdata: rqdata,
                },
                userAgent: user_agent,
            },
        },
        headers: {
            'User-Agent': user_agent,
        },
    });

const get_hcaptcha_response = async taskId => {
    let get = await axios({
        method: 'POST',
        url: 'https://api.capsolver.com/getTaskResult',
        data: {
            clientKey: CAPSOLVER_API_KEY,
            taskId: taskId,
        },
        headers: {
            'User-Agent': user_agent,
        },
    });
    while (get.data.status !== 'ready') {
        if (get.data.status === 'failed') {
            console.error('[CAPTCHA FAILED]', get.data);
            return {error: true, message: 'CAPTCHA Failed'};
        }
        await sleep(5000);
        get = await axios({
            method: 'POST',
            url: 'https://api.capsolver.com/getTaskResult',
            data: {
                clientKey: CAPSOLVER_API_KEY,
                taskId: taskId,
            },
            headers: {
                'User-Agent': user_agent,
            },
        });
    }
    return get;
};

const create_session = async () =>
    axios({
        method: 'POST',
        url: 'https://authenticate.riotgames.com/api/v1/login',
        data: {
            clientId: 'riot-client',
            language: '',
            platform: 'windows',
            remember: false,
            riot_identity: {
                language: 'en_GB',
                state: 'auth',
            },
            sdkVersion: client_sdk,
            type: 'auth',
        },
        headers: {
            'User-Agent': user_agent,
        },
    });

const create_auth_first = async () =>
    axios({
        method: 'POST',
        url: 'https://auth.riotgames.com/api/v1/authorization',
        data: {
            acr_values: '',
            claims: '',
            client_id: 'riot-client',
            code_challenge: '',
            code_challenge_method: '',
            nonce: randomUUID(), // random string
            redirect_uri: 'http://localhost/redirect',
            response_type: 'token id_token',
            scope: 'openid link account lol summoner offline_access',
        },
        headers: {
            'User-Agent': user_agent,
        },
        proxy: {
            host: 'p.webshare.io',
            port: 80,
            auth: {
                username: 'dmczebuk-FI-DE-GB-SE-AT-rotate',
                password: '1budhi56ns0m',
            },
            protocol: 'http',
        },
    });

const put_hcaptcha_to_riot = async (captcha, cookies, ua) =>
    axios({
        method: 'PUT',
        url: 'https://authenticate.riotgames.com/api/v1/login',
        data: {
            riot_identity: {
                campain: null,
                captcha: `hcaptcha ${captcha}`,
                language: 'de_DE',
                state: 'auth',
                remember: false,
                password: PASSWORD,
                username: USERNAME,
            },
            type: 'auth',
        },
        headers: {
            Cookie: cookies,
            'User-Agent': ua,
        },
    });

const get_login_cookies = async (token, cookies) =>
    axios({
        method: 'POST',
        url: 'https://auth.riotgames.com/api/v1/login-token',
        data: {
            authentication_type: 'RiotAuth',
            code_verifier: '',
            login_token: token,
            persist_login: false,
        },
        headers: {
            Cookie: cookies,
            'User-Agent': user_agent,
        },
    });

const get_auth_data = async cookies =>
    axios({
        method: 'GET',
        url: 'https://auth.riotgames.com/api/v1/authorization',
        headers: {
            Cookie: cookies,
            'User-Agent': user_agent,
            Origin: 'riot',
            'X-NewRelic-ID': 'VQcPUF5ACQIHVVlVBgc=',
        },
        data: {
            client_id: 'riot-client',
            nonce: randomUUID(), // random string
            redirect_uri: 'http://localhost/redirect',
            response_type: 'token id_token',
            scope: 'openid link account lol summoner offline_access',
        },
    });

const sleep = ms => new Promise(resolve => setTimeout(resolve, ms));
main();
