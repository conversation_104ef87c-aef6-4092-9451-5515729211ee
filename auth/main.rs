use arl::RateLimiter;
use cacache::Integrity;
use headers::Authorization;
use hyper_proxy::{Intercept, Proxy};
use lazy_static::lazy_static;
use log::{debug, error, info, warn};
use once_cell::sync::Lazy;
use std::fmt::Display;
use std::str::FromStr;
use chrono::DateTime;
use time::{Duration, OffsetDateTime};
use tokio::time::sleep;
use uuid::Uuid;
use dotenv::dotenv;
use mongodb::bson::{datetime, doc};
use mongodb::Client;
use mongodb::options::ClientOptions;
use valorant_api::enums::region::Region;
use valorant_api::errors::response_error::RequestError;
use valorant_api::utils::credentials_manager::CredentialsManager;
use valorant_api::utils::network::http_client::{ProxyHttpClient, SimpleHttpClient};

pub struct DbAuthVal {
    pub access: String,
    pub entitlement: String,
    pub source: String,
}

lazy_static! {
    static ref USERNAME: String = std::env::var("RIOT_USERNAME").ok().unwrap();
    static ref PASSWORD: String = std::env::var("RIOT_PASSWORD").ok().unwrap();
    static ref AUTH_DIR: String = std::env::var("AUTH_DIR").unwrap_or("./auth".to_string());
    static ref AUTH_KEY: String = std::env::var("AUTH_KEY").unwrap_or("auth".to_string());
    static ref REAUTH_INTERVAL: Duration = Duration::minutes(50);
    static ref VERIFY_INTERVAL: Duration = Duration::minutes(5);
    static ref SLEEP_BETWEEN_REQUESTS: std::time::Duration = std::time::Duration::from_secs(3);
}

static SIMPLE_CLIENT: Lazy<SimpleHttpClient> = Lazy::new(|| SimpleHttpClient::new().unwrap());

#[tokio::main]
async fn main() {
    dotenv().ok();
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("debug"));
    sleep(*SLEEP_BETWEEN_REQUESTS).await;

    let mut client_options = ClientOptions::parse(std::env::var("MONGOURI").unwrap().as_str())
        .await
        .expect("failed to parse options");
    let client = Client::with_options(client_options)
        .expect("failed to connect");
    println!("[MONGO_DB] Connected");

    let mut manager = do_initial_auth(&client).await;
    loop {
        sleep(std::time::Duration::from_secs(60 * 50)).await;
        manager = refresh_auth(manager.clone(), &client).await;
    }
}

async fn do_initial_auth(client: &Client) -> CredentialsManager {
    let limiter = RateLimiter::new(1, std::time::Duration::from_secs(1));
    let limiter2 = RateLimiter::new(15, std::time::Duration::from_secs(60));
    loop {
        limiter.wait().await;
        limiter2.wait().await;
        match do_initial_auth_once().await {
            Ok(c) => {
                info!("Successfully authenticated");
                let access = c.access_token.clone();
                let entitlement = c.entitlements_token.clone();
                client.database("API").collection::<DbAuthVal>("auth").update_one(doc! {"source": "val"}, doc! {"$set": doc! {"access": access.unwrap(), "entitlement": entitlement.unwrap()}}, None).await.unwrap();
                match verify_auth(&c).await {
                    Ok(_) => {
                        info!("Successfully verified");
                        return c;
                    }
                    Err(e) => {
                        error!("Failed to verify: {}", e);
                        continue;
                    }
                }
            }
            Err(e) => {
                error!("Failed to authenticate: {}", e);
            }
        }
    }
}

async fn do_initial_auth_once() -> Result<CredentialsManager, RequestError> {
    info!("Try to do initial auth for {}", *USERNAME);
    let mut credentials_manager = CredentialsManager::new();
    credentials_manager
        .authenticate(&*SIMPLE_CLIENT, &USERNAME, &PASSWORD)
        .await?;
    Ok(credentials_manager)
}

async fn refresh_auth(mut credentials_manager: CredentialsManager, client: &Client) -> CredentialsManager {
    match refresh_auth_once(&mut credentials_manager).await {
        Ok(_) => {
            info!("Successfully refreshed auth");
            match verify_auth(&credentials_manager).await {
                Ok(_) => {
                    info!("Successfully verified refreshed auth");
                    let access = credentials_manager.access_token.clone();
                    let entitlement = credentials_manager.entitlements_token.clone();
                    client.database("API").collection::<DbAuthVal>("auth").update_one(doc! {"source": "val"}, doc! {"$set": doc! {"access": access.unwrap(), "entitlement": entitlement.unwrap()}}, None).await.unwrap();
                    credentials_manager.clone()
                }
                Err(e) => {
                    error!("Failed to verify: {}", e);
                    do_initial_auth(&client).await
                }
            }
        }
        Err(e) => {
            error!("Failed to refresh auth: {}", e);
            do_initial_auth(client).await
        }
    }
}

async fn refresh_auth_once(
    credentials_manager: &mut CredentialsManager,
) -> Result<(), RequestError> {
    credentials_manager
        .refresh_access_token(&*SIMPLE_CLIENT, true)
        .await
}

async fn verify_auth(credentials_manager: &CredentialsManager) -> Result<(), RequestError> {
    info!("Try to verify auth");
    valorant_api::get_mmr_details_v1(
        credentials_manager,
        &*SIMPLE_CLIENT,
        Region::EU,
        &Uuid::from_str("ee89b4d9-13d0-5832-8dd7-eb5d8806d918").unwrap(),
    )
        .await
        .map(|_| ())
}

#[derive(Debug)]
pub(crate) enum CacheError {
    Read(cacache::Error),
    Write(cacache::Error),
    Parse(serde_json::Error),
}

impl Display for CacheError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CacheError::Write(e) => write!(f, "Failed to write to cache: {}", e),
            CacheError::Parse(e) => write!(f, "Failed to parse cache: {}", e),
            CacheError::Read(e) => write!(f, "Failed to read from cache: {}", e),
        }
    }
}