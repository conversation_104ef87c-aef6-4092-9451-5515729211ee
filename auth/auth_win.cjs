const Valorant = require('@liamcottle/valorant.js');
require('dotenv').config();
const localRiotClientApi = Valorant.LocalRiotClientAPI.initFromLockFile();
const mongo = require('mongodb').MongoClient;
const axios = require('axios');
const mongodb = new mongo(process.env.MONGOURI);

(async function () {
    await mongodb.connect();
    const db = mongodb.db('API');
    const collection = db.collection('auth');
    await localRiotClientApi.axios.post('/rso-auth/v1/authorization/refresh');
    let tokens = await localRiotClientApi.axios.get('/entitlements/v1/token');
    console.log('[AUTH DATA]', tokens.data);
    await collection.updateOne(
        {source: 'val'},
        {
            $set: {
                access: tokens.data.accessToken,
                entitlement: tokens.data.token,
                timestamp: new Date(),
            },
        }
    );

    // Temp for manu with redis
    const post = await axios
        .post('https://commands.manuel-hexe.de/tokens', {
            account: 'henrik',
            accessToken: tokens.data.accessToken,
            entitlementsToken: tokens.data.token,
        })
        .catch(e => e);
    console.log('[MANU POST]', post.data ?? post.response);

    process.exit(0);
})();
