import axios from 'axios';
import {MongoClient} from 'mongodb';
import cheerio from 'cheerio';
import webuntis from 'webuntis';
import fs from 'fs';
import {fastify as f} from 'fastify';
import moment from 'moment';
import https from 'https';
import zlib from 'zlib';
import {WebhookClient} from 'discord.js';
import axiosRetry from 'axios-retry';
import {Agent} from 'https';
import nodecron from 'node-cron';
import SteamUser from 'steam-user';
import {auth} from './external/auth.js';
import {createClient} from 'redis';
import pm2 from 'pm2';
import {HttpsProxyAgent} from 'https-proxy-agent';

let pm2_instance = {};

const basedata = JSON.parse(fs.readFileSync('./basedata.json'));
const mongodb = new MongoClient(basedata.mongodb);
await mongodb.connect();
const webhookClient = new WebhookClient({
    url: 'https://discord.com/api/webhooks/925787166070681610/qu8tG8Le0E_FwEluRgSHAzHPmRwj4L6M-1nAEN2vkA-e02zQbhUXHILdUzryK5iocysp',
});
const fastify = f({
    logger: {
        level: 'error',
    },
});
const agent = new Agent({
    ciphers: ['TLS_CHACHA20_POLY1305_SHA256', 'TLS_AES_128_GCM_SHA256', 'TLS_AES_256_GCM_SHA384', 'TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256'].join(':'),
    honorCipherOrder: true,
    minVersion: 'TLSv1.2',
});
const canvas = basedata.production ? await import('canvas') : null;
const base_url = basedata.production ? 'https://api.henrikdev.xyz' : basedata.production == false ? 'https://staging-api.henrikdev.xyz' : 'http://127.0.0.1:3600';
const redis = createClient({url: basedata.redis});
await redis.connect().catch(e => console.error(e));
redis.on('error', err => console.log('Redis Client Error', err));
redis.on('ready', () => console.log('Redis Ready'));
redis.on('connect', () => console.log('Redis Ready'));

axiosRetry(axios, {
    retries: 2,
    shouldResetTimeout: true,
    retryCondition: error => {
        return error.code === 'ECONNABORTED';
    },
});

fastify.register(import('@fastify/cors'));
/*fastify.addHook('onSend', (req, res, payload, done) => {
    const uuid = uuidv4();
    res.uuid = uuid;
    if (!res.sent) res.header('X-Request-ID', uuid);
    done();
});*/

fastify.addHook('onResponse', (req, res) => {
    /*const logging = {
        id: res.uuid,
        fastify_id: req.id,
        url: req.url,
        method: req.method,
        http: req.raw.httpVersion,
        params: Object.entries(req.params),
        queries: Object.entries(req.query),
        body: req.body,
        headers: Object.entries(req.headers),
        route: req.context.config.url,
        date: new Date(),
        status: res.statusCode,
        proxy: res.proxy,
        latency: res.getResponseTime(),
        payload: null,
    };
    getDB('logs', 'API').insertOne(logging);*/
    //console.error(req.headers)
});

fastify.addHook('onRequest', async (req, res) => {
    res.type('application/json; charset=UTF-8');
    res.header('X-Handled-Server', `Server ${pm2_instance?.cluster_id + 1 ?? 1}`);
    if (maintenance) return errorhandler({status: 503, res, errors: [{instance: 'own'}]});
    if (req.headers['user-agent'] == 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36')
        return errorhandler({
            status: 401,
            res,
            errors: [{instance: 'own', code: 1}],
        });
    //if (req.headers["user-agent"]?.includes("Mozilla/5.0")) return errorhandler({status: 403, res, errors: [{instance: 'own', code: 1}]});
    if (
        req.headers['cf-connecting-ip'] == '***************' ||
        req.headers['cf-connecting-ip'] == '**************' ||
        proxies.some(i => i.ip == req.headers['cf-connecting-ip']) ||
        req.headers['cf-connecting-ip'] == '2a03:4000:5c:b5c:a847:7bff:fe9a:16f6' ||
        req.headers['cf-connecting-ip'] == '2a01:4f9:3a:2625::2' ||
        req.headers['cf-connecting-ip'] == '2a01:4f8:2200:218b::2'
    ) {
        res.admin = true;
        return;
    }
    //if (!req.headers['authorization']) return errorhandler({status: 403, res, errors: [{instance: 'own', code: 1}]});
    //console.log(req.headers["cf-connecting-ip"])
    const instance = req.url.split('/')[1];
    let api_key = req.headers['authorization'] ?? req.query.api_key;
    if (api_key) {
        const verify = await getDB('tokens', 'API').findOne({token: api_key});
        if (!verify) return errorhandler({status: 403, res, errors: [{instance: 'own', code: 1}]});
        if (verify.admin) res.admin = true;
        if (verify.type != instance) return errorhandler({status: 403, res, errors: [{instance: 'own', code: 2}]});
        let currentrate = await getDB('ratelimit', 'API').findOne({token: api_key});
        return await checkRateLimit({c_rate: currentrate, auth: api_key, key: verify, res});
    }
    if (['multiversus'].some(i => instance == i))
        return errorhandler({
            status: 403,
            res,
            errors: [{instance: 'own', code: 3, details: "You can't use that endpoint without a valid API Key"}],
        });
    let currentrate = await getDB('ratelimit', 'API').findOne({token: req.headers['cf-connecting-ip']});
    return await checkRateLimit({c_rate: currentrate, auth: req.headers['cf-connecting-ip'], key: null, res});
});
/*fastify.addHook('onResponse', async (req, res) => {
    const responsetime = res.getResponseTime();
    if (responsetime > 15000)
        webhookClient
            .send({
                content: `High Latency: ${responsetime} | Proxy: ${res.proxy}`,
                username: 'ValAPI-Error',
            })
            .then()
            .catch(error => console.log(error));
});
fastify.addHook('onError', async (req, res, error) => {
    webhookClient
        .send({
            content: `Error:\n\`\`\`${error.toString()}\`\`\`Proxy: ${res.proxy} URL: ${req.url}`,
            username: 'ValAPI',
        })
        .then()
        .catch(error => console.log(error));
});*/
fastify.setNotFoundHandler(async (req, res) => {
    return errorhandler({status: 404, res, errors: [{instance: 'own'}]});
});
fastify.setErrorHandler(async (error, req, res) => {
    console.log(error);
    errorhandler({status: 500, res, errors: [{instance: 'own', details: error.toString()}]});
});

const errors = {
    codes: {
        1: 'Invalid API Key',
        2: 'Forbidden endpoint',
        3: 'Restricted endpoint',
        101: 'No region found for this Player',
        102: "No matches found, can't get puuid",
        103: "Possible name change detected, can't get puuid. Please play one match, wait 1-2 minutes and try it again",
        104: 'Invalid region',
        105: 'Invalid filter',
        106: 'Invalid gamemode',
        107: 'Invalid map',
        108: 'Invalid locale',
        109: 'Missing name',
        110: 'Missing tag',
        111: 'Player not found in leaderboard',
        112: 'Invalid raw type',
        113: 'Invalid match or player id',
        114: 'Invalid country code',
        115: 'Invalid season',
        116: 'Multiple queries detected. Make sure to only request one query for this query type.',
        117: 'Missing query',
        118: "Query 'page' and 'size' must be a valid number",
        119: "Query 'page' must be greater then 0",
        120: "Query 'size' must be greater then 0",
        121: "Query 'division' must be a valid number",
        122: "Query 'division' must be between 1 and 20",
        123: 'Invalid premier conference',
        124: 'Leaderboard affinity does not match conference affinity',
        201: 'Invalid gamemode',
    },
    riot: {
        501: 'API Version not implemented',
        500: 'Internal Server Error',
        404: 'Not found',
        503: 'Riot Origin Server is not reachable',
        429: 'Riot Origin Server Rate Limit, try again later',
        1015: 'Riot Origin Server Rate Limit, try again later',
        403: 'Riot Origin Server down for maintenance',
        400: 'Not able to connect to API',
        410: 'Deprecated',
        409: 'The User has to many incoming Friend Invites, can not get puuid',
        408: 'A needed fetch request timed out three times, request will be terminated, please try again',
    },
    multiverse: {
        501: 'API Version not implemented',
        500: 'Internal Server Error',
        404: 'Not found',
        503: 'WB Games Origin Server is not reachable',
        429: 'WB Games Server Rate Limit, try again later',
        1015: 'WB Games Server Rate Limit, try again later',
        403: 'WB Games Server forbidden',
        400: 'WB Games bad request',
        410: 'Deprecated',
        409: 'WB Games Confict',
        408: 'A needed fetch request timed out three times, request will be terminated, please try again',
    },
    r6: {
        404: 'Not found',
        500: 'Internal Server Error',
        503: 'Ubisoft Origin Server is not reachable',
        429: 'Ubisoft Origin Server Rate Limit, try again later',
        1015: 'Ubisoft Origin Server Rate Limit, try again later',
        403: 'Ubisoft Origin Server down for maintenance',
        400: 'Not able to connect to API',
        410: 'Deprecated',
        408: 'Ubisoft Origin Server Timeout',
        409: 'Conflict issue',
    },
    util: {
        404: 'Not found',
        500: 'Internal Server Error',
        503: 'Dependency Unavailable',
        429: 'Dependency rate limit',
        1015: 'Dependency rate limit',
        403: 'Dependency access forbidden',
        400: 'Dependency bad request',
        408: 'Dependency Timeout',
        410: 'Deprecated',
        409: 'Dependency Conflict',
    },
    own: {
        404: 'Route not found',
        500: 'Internal Server Error',
        503: 'Service Unavailable',
        429: 'Rate Limited',
        1015: 'Rate Limited',
        403: 'Forbidden',
        401: 'Unauthorized',
        400: 'Bad Request',
        410: 'Gone',
        409: 'Conflict',
        408: 'Timeout',
        501: 'API Version not implemented',
    },
};
let proxies = [];
const regions = ['eu', 'ap', 'na', 'kr'];
const affinities = [...regions, 'latam', 'br'];
const premier_urls = {
    eu: 'https://euc1-red.pp.sgp.pvp.net',
    na: 'https://usw2-red.pp.sgp.pvp.net',
    ap: 'https://apse1-red.pp.sgp.pvp.net',
    kr: 'https://apne1-red.pp.sgp.pvp.net',
};
const glz_urls = {
    eu: 'https://glz-eu-1.eu.a.pvp.net',
    na: 'https://glz-na-1.na.a.pvp.net',
    br: 'https://glz-br-1.na.a.pvp.net',
    latam: 'https://glz-latam-1.na.a.pvp.net',
    kr: 'https://glz-kr-1.kr.a.pvp.net',
    ap: 'https://glz-ap-1.ap.a.pvp.net',
};
let queues = [];
let gamemodes = [
    {
        id: 'spikerush',
        name: 'Spike Rush',
        api: 'spikerush',
        asset: 'GameModes/QuickBomb',
    },
    {
        name: 'Standard',
        asset: 'GameModes/Bomb',
    },
];
const tiers = {
    0: 'Unrated',
    1: 'Unknown 1',
    2: 'Unknown 2',
    3: 'Iron 1',
    4: 'Iron 2',
    5: 'Iron 3',
    6: 'Bronze 1',
    7: 'Bronze 2',
    8: 'Bronze 3',
    9: 'Silver 1',
    10: 'Silver 2',
    11: 'Silver 3',
    12: 'Gold 1',
    13: 'Gold 2',
    14: 'Gold 3',
    15: 'Platinum 1',
    16: 'Platinum 2',
    17: 'Platinum 3',
    18: 'Diamond 1',
    19: 'Diamond 2',
    20: 'Diamond 3',
    21: 'Ascendant 1',
    22: 'Ascendant 2',
    23: 'Ascendant 3',
    24: 'Immortal 1',
    25: 'Immortal 2',
    26: 'Immortal 3',
    27: 'Radiant',
};
const old_tiers = {
    0: 'Unrated',
    1: 'Unknown 1',
    2: 'Unknown 2',
    3: 'Iron 1',
    4: 'Iron 2',
    5: 'Iron 3',
    6: 'Bronze 1',
    7: 'Bronze 2',
    8: 'Bronze 3',
    9: 'Silver 1',
    10: 'Silver 2',
    11: 'Silver 3',
    12: 'Gold 1',
    13: 'Gold 2',
    14: 'Gold 3',
    15: 'Platinum 1',
    16: 'Platinum 2',
    17: 'Platinum 3',
    18: 'Diamond 1',
    19: 'Diamond 2',
    20: 'Diamond 3',
    21: 'Immortal 1',
    22: 'Immortal 2',
    23: 'Immortal 3',
    24: 'Radiant',
};
let category = ['e6a3', 'e6a2', 'e6a1', 'e5a3', 'e5a2', 'e5a1', 'e4a3', 'e4a2', 'e4a1', 'e3a3', 'e3a2', 'e3a1', 'e2a3', 'e2a2', 'e2a1', 'e1a3', 'e1a2', 'e1a1'];
let actids = {
    e1a1: '3f61c772-4560-cd3f-5d3f-a7ab5abda6b3',
    e1a2: '0530b9c4-4980-f2ee-df5d-09864cd00542',
    e1a3: '46ea6166-4573-1128-9cea-60a15640059b',
    e2a1: '97b6e739-44cc-ffa7-49ad-398ba502ceb0',
    e2a2: 'ab57ef51-4e59-da91-cc8d-51a5a2b9b8ff',
    e2a3: '52e9749a-429b-7060-99fe-4595426a0cf7',
    e3a1: '2a27e5d2-4d30-c9e2-b15a-93b8909a442c',
    e3a2: '4cb622e1-4244-6da3-7276-8daaf1c01be2',
    e3a3: 'a16955a5-4ad0-f761-5e9e-389df1c892fb',
    e4a1: '573f53ac-41a5-3a7d-d9ce-d6a6298e5704',
    e4a2: 'd929bc38-4ab6-7da4-94f0-ee84f8ac141e',
    e4a3: '3e47230a-463c-a301-eb7d-67bb60357d4f',
    e5a1: '67e373c7-48f7-b422-641b-079ace30b427',
    e5a2: '7a85de9a-4032-61a9-61d8-f4aa2b4a84b6',
    e5a3: 'aca29595-40e4-01f5-3f35-b1b3d304c96e',
    e6a1: '9c91a445-4f78-1baa-a3ea-8f8aadf4914d',
    e6a2: '34093c29-4306-43de-452f-3f944bde22be',
    e6a3: '2de5423b-4aad-02ad-8d9b-c0a931958861',
};
const locals = [
    'ar-AE',
    'de-DE',
    'en-GB',
    'en-US',
    'es-ES',
    'es-MX',
    'fr-FR',
    'id-ID',
    'it-IT',
    'ja-JP',
    'ko-KR',
    'pl-PL',
    'pt-BR',
    'ru-RU',
    'th-TH',
    'tr-TR',
    'vi-VN',
    'zh-CN',
    'zh-TW',
];
const mv_maps = {
    '/Game/Panda_Main/Maps/Map_Batcave/Map_Batcave.Map_Batcave': 'Batcave',
    '/Game/Panda_Main/Maps/Map_Classic3Platform/Map_Classic3Platform_Small.Map_Classic3Platform_Small': 'Classic 3 Platform (1 vs 1)',
    '/Game/Panda_Main/Maps/Map_Classic3Platform/Map_Classic3Platform_Large.Map_Classic3Platform_Large': 'Classic 3 Platform',
    '/Game/Panda_Main/Maps/M011/Maps/LargeNoPlat/Map_M011_LargeNoPlat.Map_M011_LargeNoPlat': 'Sky Arena',
    '/Game/Panda_Main/Maps/M011/Maps/Small/Map_M011_Small.Map_M011_Small': 'Sky Arena Platforms (1 vs 1)',
    '/Game/Panda_Main/Maps/M011/Maps/SmallNoPlat/Map_M011_SmallNoPlat.Map_M011_SmallNoPlat': 'Sky Arena (1 vs 1)',
    '/Game/Panda_Main/Maps/M011/Map_M011.Map_M011': 'Sky Arena Platforms',
    '/Game/Panda_Main/Maps/ScoobyMansion/Map_ScoobyDoo.Map_ScoobyDoo': "Scooby's Haunted Mansion",
    '/Game/Panda_Main/Maps/ContainerMaps/TrainingContainer.TrainingContainer': 'Training Room',
    '/Game/Panda_Main/Maps/TrainingRoomLarge/Map_TrainingRoomLarge.Map_TrainingRoomLarge': 'Training Room',
    '/Game/Panda_Main/Maps/TrainingRoomSmall/Map_TrainingRoomSmall.Map_TrainingRoomSmall': 'Training Room (1 vs 1)',
    '/Game/Panda_Main/Maps/Map_TreeHouseSmall/Map_TreeHouseSmall.Map_TreeHouseSmall': 'Tree Fort (1 vs 1)',
    '/Game/Panda_Main/Maps/Map_TreeHouse/Map_TreeHouse.Map_TreeHouse': 'Tree Fort',
    '/Game/Panda_Main/Maps/TrophyRoom/Map_TrophyRoom_2platforms.Map_TrophyRoom_2platforms': "Trophy's E.D.G.E. 2",
    '/Game/Panda_Main/Maps/TrophyRoom/Map_TrophyRoom_LargePlatform.Map_TrophyRoom_LargePlatform': "Trophy's E.D.G.E.",
};
const mv_agents = {
    character_arya: 'Arya Stark',
    character_batman: 'Batman',
    character_bugs_bunny: 'Bugs Bunny',
    character_creature: 'Reindog',
    character_taz: 'Taz',
    character_c16: 'Lebron James',
    character_c017: 'The Iron Giant',
    character_c019: 'Morty',
    character_c020: 'Rick Sanchez',
    character_finn: 'Finn the Human',
    character_garnet: 'Garnet',
    character_harleyquinn: 'Harley Quinn',
    character_jake: 'Jake the Dog',
    character_shaggy: 'Shaggy',
    character_steven: 'Steven Universe',
    character_superman: 'Superman',
    character_tom_and_jerry: 'Tom and Jerry',
    character_velma: 'Velma',
    character_wonder_woman: 'Wonder Woman',
};
let agents = [];
let maps = [];
let weapons = [];
let gear = [];
let skins = [];
let sprays = [];
let buddies = [];
let player_cards = [];
let player_titles = [];
let content_tiers = [];
let esports_leagues = {};
let esports_scheduels = {};
let esports_vods = {};
let item_types_val = {
    'e7c63390-eda7-46e0-bb7a-a6abdacd2433': 'skin_level',
    '3ad1b2b2-acdb-4524-852f-954a76ddae0a': 'skin_chroma',
    '01bb38e1-da47-4e6a-9b3d-945fe4655707': 'agent',
    'f85cb6f7-33e5-4dc8-b609-ec7212301948': 'contract_definition',
    'dd3bf334-87f3-40bd-b043-682a57a8dc3a': 'buddy',
    'd5f120f8-ff8c-4aac-92ea-f2b5acbe9475': 'spray',
    '3f296c07-64c3-494c-923b-fe692a4fa1bd': 'player_card',
    'de7caa6b-adf7-4588-bbd1-143831e786c6': 'player_title',
};
let gamepods = {};
let riot_header = {};
let mv_headers = {};
let client_versions = {};
let cseason;
let maintenance = true;
let val_leaderboard = [];
let val_premier_conferences = [];
let val_premier_current_season = null;
const httpsAgent = new HttpsProxyAgent('*************************************************');

const checkRateLimit = async ({c_rate, auth, key, res} = {}) => {
    if (!c_rate) {
        res.headers({'x-ratelimit-limit': 1, 'x-ratelimit-remaining': key?.limit ?? 29, 'x-ratelimit-reset': 60});
        return getDB('ratelimit', 'API').insertOne({
            token: auth,
            used: 2,
            limit: key?.limit ?? 30,
            createdAt: new Date(),
            unix: moment().unix(),
        });
    }
    if (c_rate.unix + 60 - moment().unix() < 0)
        c_rate = await getDB('ratelimit', 'API').findOneAndUpdate(
            {token: auth},
            {$set: {used: 2, limit: key?.limit ?? 30, createdAt: new Date(), unix: moment().unix()}},
            {returnDocument: 'after', upsert: true, includeResultMetadata: false}
        );
    if (c_rate.used > c_rate.limit) {
        res.headers({
            'x-ratelimit-limit': c_rate.used,
            'x-ratelimit-remaining': 0,
            'retry-after': c_rate.unix + 60 - moment().unix(),
        });
        return errorhandler({
            status: 429,
            res,
            errors: [{instance: 'own', details: 'You reached your Rate Limit, please try again later', global: true}],
        });
    }
    res.headers({
        'x-ratelimit-limit': c_rate.used,
        'x-ratelimit-remaining': c_rate.limit - c_rate.used,
        'x-ratelimit-reset': 60 - (moment().unix() - c_rate.unix),
    });
    return getDB('ratelimit', 'API').updateOne({token: auth}, {$inc: {used: 1}});
};
const endpointspecific = async ({token, instance, endpoint, limit, res} = {}) => {
    const acclimit = await getDB('specificratelimit', 'API').findOne({token, instance, endpoint});
    if (acclimit && acclimit.used > limit)
        return errorhandler({
            res,
            status: 429,
            errors: [
                {
                    instance: 'own',
                    message: 'You reached your account endpoint rate Limit, please try again later',
                    global: false,
                },
            ],
        });
    if (acclimit && acclimit.used <= limit)
        await getDB('specificratelimit', 'API').updateOne(
            {
                token,
                instance,
                endpoint,
            },
            {$inc: {used: 1}}
        );
    if (!acclimit)
        await getDB('specificratelimit', 'API').insertOne({
            token,
            instance,
            endpoint,
            createdAt: new Date(),
            used: 2,
        });
    return;
};
const makeRequest = async ({
    url,
    method = 'GET',
    instance = 'riot',
    timeout,
    return_error = false,
    res,
    overwrite_headers,
    proxy = true,
    httpAgent = false,
    body,
    region,
} = {}) => {
    const req_proxy = proxies[generateNumber()];
    const request = await axios({
        method,
        url,
        data: body ?? null,
        timeout: timeout ?? 10000,
        headers: overwrite_headers ?? (region ? getRiotHeaders(region) : null),
        proxy: proxy
            ? {
                  host: req_proxy.ip,
                  port: req_proxy.port,
                  auth: {username: req_proxy.username, password: req_proxy.pw},
                  protocol: 'http',
              }
            : null,
        httpsAgent: httpAgent ? agent : null,
        responseType: 'json',
    }).catch(e => {
        return e;
    });
    if (request.data && region) request.data.region = region;
    if (res && proxy) res.proxy = req_proxy.ip;
    if (request.code == 'ECONNABORTED' && !return_error) {
        errorhandler({
            res,
            status: 408,
            errors: [{instance}],
        });
        return request;
    }
    if (return_error && request.response) return request;
    if (!return_error && request.response) {
        errorhandler({
            res,
            status: request.response.status,
            errors: [
                {
                    instance,
                    details: request.response.data,
                },
            ],
        });
        return request;
    }
    return request;
};
const errorhandlerbuilder = ({status, instance, details = null, message, global = false, code = 0} = {}) => {
    if (status === 429) {
        return {
            message: message ?? errors[instance][status],
            code,
            global,
        };
    }
    return {
        message: message ?? code != 0 ? errors.codes[code] : errors[instance][status],
        code,
        details: typeof details == 'object' || Array.isArray(details) ? JSON.stringify(details) : details,
    };
};
const proxy_408 = [];
const errorhandler = ({res, status, errors} = {}) => {
    /*if (status == 408) {
        if (!proxy_408.includes(res.proxy) && res.proxy) proxy_408.push(res.proxy);
        console.error('[408 ERROR]', res.proxy ?? 'Unknown Proxy', proxy_408.length);
    }*/
    return res.code(status).send({status, errors: errors.map(i => errorhandlerbuilder({status, ...i}))});
};
const generateNumber = length => {
    return Math.floor(Math.random() * (length ?? proxies.length));
};
const getDB = (col, db = 'INGAME-API') => {
    return mongodb.db(db).collection(col);
};

const getMajorValoData = async () => {
    const valdata = (
        await Promise.allSettled([
            makeRequest({url: 'https://valorant-api.com/v1/agents', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/maps', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/weapons', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/internal/locres/en-US', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/gear', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/seasons', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/weapons/skins', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/sprays', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/buddies', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/playercards', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/contenttiers', return_error: true, proxy: false}),
            makeRequest({
                url: 'https://esports-api.service.valorantesports.com/persisted/val/getLeagues?hl=en-US&sport=val',
                overwrite_headers: {
                    'x-api-key': '0TvQnueqKa5mxJntVWt0w4LpLfEkrV1Ta8rQBb9Z',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.60',
                },
                return_error: true,
                proxy: false,
            }),
            makeRequest({url: 'https://valorant-api.com/v1/gamemodes/queues', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/gamemodes', return_error: true, proxy: false}),
            makeRequest({url: 'https://valorant-api.com/v1/playertitles', return_error: true, proxy: false}),
        ])
    ).map(i => i.value);
    if (valdata[0].data) agents = valdata[0].data.data;
    if (valdata[1].data) maps = valdata[1].data.data;
    if (valdata[2].data) weapons = valdata[2].data.data;
    if (valdata[3].data) gamepods = valdata[3].data.data.UI_GamePodStrings;
    if (valdata[4].data) gear = valdata[4].data.data;
    if (valdata[5].data) {
        cseason = valdata[5].data.data.find(i => moment().unix() > moment(i.startTime).unix() && moment().unix() < moment(i.endTime).unix() && i.type).uuid;
        valdata[5].data.data.shift();
        const category_cache = [];
        for (const episode of valdata[5].data.data.filter(i => !i.parentUuid)) {
            const acts = valdata[5].data.data.filter(i => i.parentUuid == episode.uuid);
            for (const act of acts) {
                const name = `e${episode.displayName.split(' ')[1]}a${act.displayName.split(' ')[1]}`;
                actids[name] = act.uuid;
                category_cache.push(name);
            }
        }
        category = category_cache;
    }
    if (valdata[6].data) {
        let cache = [];
        valdata[6].data.data.forEach(
            i =>
                (cache = cache.concat(
                    i.levels.map(k => {
                        return {...k, parent: i.uuid};
                    })
                ))
        );
        skins = {levels: cache, base: valdata[6].data.data};
    }
    if (valdata[7].data) {
        sprays = valdata[7].data.data;
    }
    if (valdata[8].data) {
        let cache = [];
        valdata[8].data.data.forEach(
            i =>
                (cache = cache.concat(
                    i.levels.map(k => {
                        return {...k, parent: i.uuid};
                    })
                ))
        );
        buddies = {levels: cache, base: valdata[8].data.data};
    }
    if (valdata[9].data) player_cards = valdata[9].data.data;
    if (valdata[10].data) content_tiers = valdata[10].data.data;
    let schedule;
    let vods;
    if (valdata[11].data) {
        esports_leagues = valdata[11].data.data.leagues;
        schedule = await makeRequest({
            url: `https://esports-api.service.valorantesports.com/persisted/val/getSchedule?hl=en-US&sport=val&leagueId=${esports_leagues.map(i => i.id).join(',')}`,
            overwrite_headers: {
                'x-api-key': '0TvQnueqKa5mxJntVWt0w4LpLfEkrV1Ta8rQBb9Z',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.60',
            },
            return_error: true,
            proxy: false,
        });
        vods = await makeRequest({
            url: `https://esports-api.service.valorantesports.com/persisted/val/getVods?hl=en-US&sport=val&leagueId=${esports_leagues.map(i => i.id).join(',')}`,
            overwrite_headers: {
                'x-api-key': '0TvQnueqKa5mxJntVWt0w4LpLfEkrV1Ta8rQBb9Z',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.60',
            },
            return_error: true,
            proxy: false,
        });
    }
    if (schedule?.data) esports_scheduels = schedule.data.data.schedule.events;
    if (vods?.data) esports_vods = vods.data.data.schedule.events;
    if (valdata[12].data) {
        queues = valdata[12].data.data.map(i => {
            if (i.queueId == 'newmap')
                return {
                    id: i.queueId,
                    name: 'New Map',
                    api: 'newmap',
                };
            return {
                id: i.queueId,
                name: i.dropdownText,
                api: i.selectedText.toLowerCase().replaceAll(' ', ''),
            };
        });
        queues.push({id: 'premier', name: 'Premier', api: 'premier'});
        queues.push({id: '', name: 'Custom Game', api: 'custom'});
    }
    if (valdata[13].data)
        gamemodes = valdata[13].data.data.map(i => {
            const asset_split = i.assetPath.split('/');
            return {
                name: i.displayName,
                asset: `${asset_split[2]}/${asset_split[3]}`,
            };
        });
    if (valdata[14].data) player_titles = valdata[14].data.data;
    return;
};
const getMinorValoDataMain = async () => {
    const valdata = (
        await Promise.allSettled([
            makeRequest({
                url: 'https://pd.eu.a.pvp.net/premier/v1/affinities/eu/conferences',
                return_error: true,
                proxy: true,
                region: 'eu',
            }),
            makeRequest({
                url: 'https://pd.na.a.pvp.net/premier/v1/affinities/na/conferences',
                return_error: true,
                proxy: true,
                region: 'na',
            }),
            makeRequest({
                url: 'https://pd.na.a.pvp.net/premier/v1/affinities/latam/conferences',
                return_error: true,
                proxy: true,
                region: 'na',
            }),
            makeRequest({
                url: 'https://pd.na.a.pvp.net/premier/v1/affinities/br/conferences',
                return_error: true,
                proxy: true,
                region: 'na',
            }),
            makeRequest({
                url: 'https://pd.kr.a.pvp.net/premier/v1/affinities/kr/conferences',
                return_error: true,
                proxy: true,
                region: 'kr',
            }),
            makeRequest({
                url: 'https://pd.ap.a.pvp.net/premier/v1/affinities/ap/conferences',
                return_error: true,
                proxy: true,
                region: 'ap',
            }),
            makeRequest({
                url: 'https://pd.eu.a.pvp.net/premier/v1/affinities/eu/premier-seasons',
                return_error: true,
                proxy: true,
                region: 'eu',
            }),
            makeRequest({
                url: 'https://pd.na.a.pvp.net/premier/v1/affinities/na/premier-seasons',
                return_error: true,
                proxy: true,
                region: 'na',
            }),
            makeRequest({
                url: 'https://pd.na.a.pvp.net/premier/v1/affinities/latam/premier-seasons',
                return_error: true,
                proxy: true,
                region: 'na',
            }),
            makeRequest({
                url: 'https://pd.na.a.pvp.net/premier/v1/affinities/br/premier-seasons',
                return_error: true,
                proxy: true,
                region: 'na',
            }),
            makeRequest({
                url: 'https://pd.kr.a.pvp.net/premier/v1/affinities/kr/premier-seasons',
                return_error: true,
                proxy: true,
                region: 'kr',
            }),
            makeRequest({
                url: 'https://pd.ap.a.pvp.net/premier/v1/affinities/ap/premier-seasons',
                return_error: true,
                proxy: true,
                region: 'ap',
            }),
        ])
    ).map(i => i.value);
    const premier_conferences_fetch = valdata.slice(0, 6);
    val_premier_conferences = [];
    const premier_seasons_fetch = valdata.slice(6, 12);
    for (let i = 0; premier_seasons_fetch.length > i; i++) {
        if (premier_seasons_fetch[i].response || premier_seasons_fetch[i].code) continue;
        const affinity = premier_seasons_fetch[i].config.url.split('/')[6];
        const region = premier_seasons_fetch[i].config.url.split('/')[2].split('.')[1];
        for (let k = 0; premier_seasons_fetch[i].data.PremierSeasons.length > k; k++) {
            await getDB('premier_metadata').updateOne(
                {id: premier_seasons_fetch[i].data.PremierSeasons[k].ID, affinity, type: 'season'},
                {$set: {...premier_seasons_fetch[i].data.PremierSeasons[k], region}},
                {upsert: true}
            );
        }
        if (affinity == 'eu' && premier_seasons_fetch[i].data.PremierSeasons.length)
            val_premier_current_season = premier_seasons_fetch[i].data.PremierSeasons.sort((a, b) => new Date(b.EndTime) - new Date(a.EndTime))[0].ID;
    }
    for (let i = 0; premier_conferences_fetch.length > i; i++) {
        if (premier_conferences_fetch[i].response || premier_conferences_fetch[i].code) continue;
        const affinity = premier_conferences_fetch[i].config.url.split('/')[6];
        const region = premier_conferences_fetch[i].config.url.split('/')[2].split('.')[1];
        const conferences = premier_conferences_fetch[i].data.PremierConferences.map(i => {
            return {
                ...i,
                affinity,
                region,
                type: 'conference',
            };
        });
        for (const conference of conferences)
            await getDB('premier_metadata').updateOne(
                {
                    id: conference.id,
                    season: val_premier_current_season,
                },
                {$set: conference},
                {upsert: true}
            );
        val_premier_conferences.push(...conferences);
    }
    /*if (!valdata[6].response && !valdata[6].code) {
        await getDB('premier_metadata').updateOne({id: valdata[6].data.ID}, {$set: {...valdata[6].data, type: 'season'}}, {upsert: true});
        val_premier_current_season = valdata[6].data.PremierSeasons.sort((a, b) => b.EndTime - a.EndTime)[0].ID;
    }*/
    return;
};
const getMinorValoDataSide = async () => {
    const valdata = (
        await Promise.allSettled([
            getDB('premier_metadata').find({type: 'conference', season: val_premier_current_season}).toArray(),
            getDB('premier_metadata').findOne({
                type: 'season',
                StartTime: {$lt: new Date().toISOString()},
                EndTime: {$gt: new Date().toISOString()},
            }),
        ])
    ).map(i => i.value);
    val_premier_conferences = valdata[0];
    val_premier_current_season = valdata[1]?.ID ?? null;
    return;
};
const getMaps = () => {
    return maps;
};
const getAgents = () => {
    return agents;
};
const getWeapons = () => {
    return weapons;
};
const getGamepods = () => {
    return gamepods;
};
const getGear = () => {
    return gear;
};
const getSkins = () => {
    return skins;
};
const getSprays = () => {
    return sprays;
};
const getBuddies = () => {
    return buddies;
};
const getPlayerCards = () => {
    return player_cards;
};
const getPlayerTitles = () => {
    return player_titles;
};
const getCurrentSeason = () => {
    return cseason;
};
const getCurrentSeasonShort = () => {
    return Object.entries(actids).find(i => i[1] == cseason)[0];
};
const getActIDs = () => {
    return actids;
};
const getCategories = () => {
    return category;
};
const getContentTiers = () => {
    return content_tiers;
};
const getQueues = () => {
    return queues;
};
const getGamemodes = () => {
    return gamemodes;
};
const getEsportLeagues = () => esports_leagues;
const getEsportSchedules = () => esports_scheduels;
const getEsportVods = () => esports_vods;

const getClientVersion = async () => {
    client_versions['eu'] = (await getDB('versions').findOne({region: 'eu'})).version_for_api;
    client_versions['na'] = (await getDB('versions').findOne({region: 'na'})).version_for_api;
    client_versions['kr'] = (await getDB('versions').findOne({region: 'kr'})).version_for_api;
    client_versions['ap'] = (await getDB('versions').findOne({region: 'ap'})).version_for_api;
    return null;
};
const getRiotHeaders = region => {
    return {
        Authorization: riot_header['Authorization'],
        'X-Riot-Entitlements-JWT': riot_header['X-Riot-Entitlements-JWT'],
        'X-Riot-ClientPlatform': riot_header['X-Riot-ClientPlatform'],
        'X-Riot-ClientVersion': client_versions[['br', 'latam'].some(i => i == region) ? 'na' : region],
        'user-agent': 'ShooterGame/13 Windows/10.0.19043.1.256.64bit',
    };
};
const getMultiVersusHeaders = () => {
    return mv_headers;
};
const getCurrentLeaderboard = region => {
    return val_leaderboard[region];
};
const getPremierConferences = () => val_premier_conferences;
const getPremierSeason = () => val_premier_current_season;

const updateContent = async () => {
    const axiosf = [];
    for (let i = 0; locals.length > i; i++) {
        axiosf.push(
            axios.get(`https://ap.api.riotgames.com/val/content/v1/contents?locale=${locals[i]}`, {headers: {'X-Riot-Token': basedata.riottoken}}).catch(error => {
                return error;
            })
        );
    }
    const req = await axios.all(axiosf).catch(error => {
        return error;
    });
    for (let i = 0; req.length > i; i++) {
        if (!req[i].response) fs.writeFileSync(`./files/valorant/content/content-${locals[i].toLowerCase()}.json`, JSON.stringify(req[i].data));
    }
};
const updateLeaderboard = async () => {
    for (let i = 0; affinities.length > i; i++) {
        const req_proxy = proxies[generateNumber()];
        const firstreq = await axios
            .get(
                `https://pd.${regions.some(k => k == affinities[i]) ? affinities[i] : 'na'}.a.pvp.net/mmr/v1/leaderboards/affinity/${
                    affinities[i]
                }/queue/competitive/season/${cseason}?startIndex=0&size=1000`,
                {
                    headers: getRiotHeaders(regions.some(k => k == regions[i]) ? regions[i] : 'na'),
                    timeout: 3000,
                    proxy: {
                        host: req_proxy.ip,
                        port: req_proxy.port,
                        auth: {username: req_proxy.username, password: req_proxy.pw},
                        protocol: 'http',
                    },
                }
            )
            .catch(error => {
                return error;
            });
        if (!firstreq.response && !firstreq.code) {
            if (!firstreq.data) console.error(firstreq);
            let clusterdata = [...firstreq.data.Players];
            let axiosall = [];
            for (let j = 0; Math.floor(firstreq.data.totalPlayers / 1000) > j; j++) {
                if (firstreq.data.totalPlayers > Number(`${j + 1}000`))
                    axiosall.push(
                        axios
                            .get(
                                `https://pd.${regions.some(k => k == affinities[i]) ? affinities[i] : 'na'}.a.pvp.net/mmr/v1/leaderboards/affinity/${
                                    affinities[i]
                                }/queue/competitive/season/${cseason}?startIndex=${j + 1}000&size=1000`,
                                {
                                    headers: getRiotHeaders(affinities[i]),
                                    timeout: 3000,
                                    proxy: {
                                        host: req_proxy.ip,
                                        port: req_proxy.port,
                                        auth: {username: req_proxy.username, password: req_proxy.pw},
                                        protocol: 'http',
                                    },
                                }
                            )
                            .catch(e => e)
                    );
            }
            const fetchLeaderboard = async () => {
                let leaderboardfetch = await Promise.allSettled(axiosall);
                leaderboardfetch = leaderboardfetch.map(item => {
                    return item.value;
                });
                axiosall = [];
                for (let j = 0; leaderboardfetch.length > j; j++) {
                    if (leaderboardfetch[j]?.data == undefined && (leaderboardfetch[j].response?.status == 403 || leaderboardfetch[j].code)) {
                        axiosall.push(
                            axios
                                .get(leaderboardfetch[j].config.url, {
                                    headers: getRiotHeaders(affinities[i]),
                                    timeout: 3000,
                                    proxy: {
                                        host: req_proxy.ip,
                                        port: req_proxy.port,
                                        auth: {username: req_proxy.username, password: req_proxy.pw},
                                        protocol: 'http',
                                    },
                                })
                                .catch(e => e)
                        );
                        console.log(leaderboardfetch[j].config.url);
                    }
                    if (leaderboardfetch[j]?.data != undefined) clusterdata.push(...leaderboardfetch[j].data.Players);
                }
                if (axiosall.length) return fetchLeaderboard();
                return;
            };
            await fetchLeaderboard();
            clusterdata = clusterdata.sort((a, b) => a.leaderboardRank - b.leaderboardRank);
            val_leaderboard[affinities[i]] = {
                last_update: moment().unix(),
                next_update: moment().unix() + 3060,
                total_players: firstreq.data.totalPlayers,
                radiant_threshold: firstreq.data.tierDetails['27'].rankedRatingThreshold,
                immortal_3_threshold: firstreq.data.tierDetails['26'].rankedRatingThreshold,
                immortal_2_threshold: firstreq.data.tierDetails['25'].rankedRatingThreshold,
                immortal_1_threshold: firstreq.data.tierDetails['24'].rankedRatingThreshold,
                players: clusterdata,
            };
            console.log('success');
            fs.writeFileSync(
                `./files/valorant/leaderboard/${getCurrentSeasonShort()}-leaderboard-${affinities[i]}.json`,
                JSON.stringify({
                    last_update: moment().unix(),
                    next_update: moment().unix() + 3060,
                    total_players: firstreq.data.totalPlayers,
                    radiant_threshold: firstreq.data.tierDetails['27'].rankedRatingThreshold,
                    immortal_3_threshold: firstreq.data.tierDetails['26'].rankedRatingThreshold,
                    immortal_2_threshold: firstreq.data.tierDetails['25'].rankedRatingThreshold,
                    immortal_1_threshold: firstreq.data.tierDetails['24'].rankedRatingThreshold,
                    players: clusterdata,
                })
            );
        }
    }
};
const setLeaderboard = async () => {
    for (let i = 0; affinities.length > i; i++) {
        if (fs.existsSync(`./files/valorant/leaderboard/${getCurrentSeasonShort()}-leaderboard-${affinities[i]}.json`))
            val_leaderboard[affinities[i]] = JSON.parse(
                fs.readFileSync(`./files/valorant/leaderboard/${getCurrentSeasonShort()}-leaderboard-${affinities[i]}.json`, {encoding: 'utf8'})
            );
    }
    return;
};
const extractHexColorCode = str => {
    const rgbaValues = str.match(/R=(.*?),G=(.*?),B=(.*?),A=(.*?)\)/);
    const r = Math.round(parseFloat(rgbaValues[1]) * 255)
        .toString(16)
        .padStart(2, '0');
    const g = Math.round(parseFloat(rgbaValues[2]) * 255)
        .toString(16)
        .padStart(2, '0');
    const b = Math.round(parseFloat(rgbaValues[3]) * 255)
        .toString(16)
        .padStart(2, '0');
    return `#${r}${g}${b}`;
};
const updatePremierLeaderboard = async () => {
    for (let i = 0; val_premier_conferences.length > i; i++) {
        for (let k = 1; k < 22; k++) {
            let req_proxy = proxies[generateNumber()];
            let firstreq = await axios
                .get(
                    `${premier_urls[val_premier_conferences[i].region]}/leaderboard/v1/name/val-premier/region/${
                        val_premier_conferences[i].affinity
                    }/season/${val_premier_current_season}/grouping/${val_premier_conferences[i].key}-${k}?startRank=0&endRank=19`,
                    {
                        headers: getRiotHeaders(val_premier_conferences[i].region),
                        httpsAgent,
                        timeout: 3000,
                    }
                )
                .catch(e => e);
            let error_count = 0;
            while (firstreq.response) {
                if (error_count > 10) return;
                if (firstreq.response?.status == 401) await updateauth({val_force: false});
                if (firstreq.response?.status == 404) {
                    const metadata = {
                        conference: val_premier_conferences[i].key,
                        region: val_premier_conferences[i].region,
                        affinity: val_premier_conferences[i].affinity,
                        div: k,
                    };
                    await getDB('premier_metadata').updateOne(
                        {
                            grouping: `${metadata.conference.toLowerCase()}:${metadata.div}`,
                            season: val_premier_current_season,
                        },
                        {
                            $set: {
                                updated_at: new Date().toISOString(),
                                ...metadata,
                                teams: [],
                                type: 'leaderboard',
                            },
                        },
                        {upsert: true}
                    );
                    break;
                }
                console.error('[ERROR] Premier Leaderboard Fetch |', firstreq.config.url, req_proxy.ip, firstreq.response?.status);
                firstreq = await axios
                    .get(
                        `${premier_urls[val_premier_conferences[i].region]}/leaderboard/v1/name/val-premier/region/${
                            val_premier_conferences[i].affinity
                        }/season/${val_premier_current_season}/grouping/${val_premier_conferences[i].key}-${k}?startRank=0&endRank=19`,
                        {
                            headers: getRiotHeaders(val_premier_conferences[i].region),
                            httpsAgent,
                            timeout: 3000,
                        }
                    )
                    .catch(e => e);
                error_count++;
            }
            if (!firstreq.response && !firstreq.code) {
                if (!firstreq.data) console.error(firstreq);
                let clusterdata = [...firstreq.data.rankings];
                let axiosall = [];
                let size = firstreq.data.size;
                let lower_limit = 20;
                let higher_limit = 39;
                while (lower_limit <= size) {
                    axiosall.push(
                        axios
                            .get(
                                `${premier_urls[val_premier_conferences[i].region]}/leaderboard/v1/name/val-premier/region/${
                                    val_premier_conferences[i].affinity
                                }/season/${val_premier_current_season}/grouping/${val_premier_conferences[i].key}-${k}?startRank=${lower_limit}&endRank=${higher_limit}`,
                                {
                                    headers: getRiotHeaders(val_premier_conferences[i].region),
                                    httpsAgent,
                                    timeout: 3000,
                                }
                            )
                            .catch(e => e)
                    );
                    lower_limit += 20;
                    higher_limit += 20;
                }
                const fetchLeaderboard = async () => {
                    let leaderboardfetch = await Promise.allSettled(axiosall);
                    leaderboardfetch = leaderboardfetch.map(i => i.value);
                    axiosall = [];
                    for (let j = 0; leaderboardfetch.length > j; j++) {
                        if ((leaderboardfetch[j]?.data == undefined && leaderboardfetch[j].response?.status) || leaderboardfetch[j].code) {
                            console.log('[ERROR] Premier Leaderboard Fetch |', leaderboardfetch[j].response?.status);
                            const region = leaderboardfetch[j].config.url.split('/')[2].split('.')[1];
                            axiosall.push(
                                axios
                                    .get(leaderboardfetch[j].config.url, {
                                        headers: getRiotHeaders(region),
                                        timeout: 3000,
                                        httpsAgent,
                                    })
                                    .catch(e => e)
                            );
                            console.log(leaderboardfetch[j].config.url);
                        }
                        if (leaderboardfetch[j]?.data != undefined) clusterdata.push(...leaderboardfetch[j].data.rankings);
                    }
                    if (axiosall.length) return fetchLeaderboard();
                    return;
                };
                await fetchLeaderboard();
                clusterdata = clusterdata.sort((a, b) => a.ranking - b.ranking);
                const metadata = {
                    conference: val_premier_conferences[i].key,
                    region: val_premier_conferences[i].region,
                    affinity: val_premier_conferences[i].affinity,
                    div: k,
                };
                await getDB('premier_metadata').updateOne(
                    {grouping: firstreq.data.grouping, season: firstreq.data.season},
                    {
                        $set: {
                            updated_at: new Date().toISOString(),
                            ...metadata,
                            teams: clusterdata.map(i => i.entityId),
                            type: 'leaderboard',
                        },
                    },
                    {upsert: true}
                );
                for (const team of clusterdata)
                    await getDB('premier_teams').updateOne(
                        {id: team.entityId},
                        {
                            $set: {
                                ...metadata,
                                updated_at: new Date().toISOString(),
                                ranking: team.ranking,
                                score: team.score,
                                anonymous: team.anonymous,
                                name: team.additionalInfo.rosterName.trim(),
                                wins: team.additionalInfo.wins,
                                losses: team.additionalInfo.losses,
                                tag: team.additionalInfo.tag.trim(),
                                customization: {
                                    icon: team.additionalInfo.customization.icon,
                                    primary_color: extractHexColorCode(team.additionalInfo.customization.primaryColor),
                                    secondary_color: extractHexColorCode(team.additionalInfo.customization.secondaryColor),
                                    tertiary_color: extractHexColorCode(team.additionalInfo.customization.tertiaryColor),
                                },
                                season: getPremierSeason(),
                            },
                        },
                        {upsert: true}
                    );
                console.log('[PREMIER LEADERBOARD METADATA] |', metadata.conference, metadata.div);
            }
        }
    }
    console.log('[PREMIER LEADERBOARD METADATA] | Done');
};

const updateStore = async () => {
    const offers = await makeRequest({
        url: 'https://pd.eu.a.pvp.net/store/v1/offers/',
        return_error: true,
        region: 'eu',
    });
    const storefront = await makeRequest({
        url: 'https://pd.eu.a.pvp.net/store/v2/storefront/c9739865-7efd-52de-a164-37c9cf7a7341',
        return_error: true,
        region: 'eu',
    });
    if (!offers.response && !offers.code) fs.writeFileSync(`./files/valorant/store/store_offers.json`, JSON.stringify(offers.data));
    if (!storefront.response && !storefront.code) {
        for (let i = 0; storefront.data.FeaturedBundle.Bundles.length > i; i++) {
            storefront.data.FeaturedBundle.Bundles[i].timestamp = new Date(
                Date.now() + storefront.data.FeaturedBundle.Bundles[i].DurationRemainingInSeconds * 1000
            ).toISOString();
        }
        fs.writeFileSync(`./files/valorant/store/store_featured.json`, JSON.stringify(storefront.data.FeaturedBundle));
    }
};
const getPuuid = async ({name, tag} = {}) => {
    return await getDB('puuids').findOne({name, tag}, {collation: {locale: 'en', strength: 2, alternate: 'shifted'}});
};
const check404 = async ({name, tag} = {}) => {
    const res = await getDB('notfound').findOne(
        {name, tag},
        {
            collation: {
                locale: 'en',
                strength: 2,
                alternate: 'shifted',
            },
        }
    );
    return res ? true : false;
};
const calculateElo = ({tier, progress} = {}) => {
    if (tier >= 24) return 2100 + progress;
    return tier * 100 - 300 + progress;
};
const patchData = data => {
    const selected_rank_act = [];
    if (
        data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID == null ||
        data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id] == null ||
        data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].WinsByTier == null
    )
        return {error: 'No data Available'};
    const keys =
        data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].WinsByTier != null
            ? Object.keys(data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].WinsByTier)
            : 0;
    const value =
        data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].WinsByTier != null
            ? Object.values(data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].WinsByTier)
            : 0;
    for (let i = 0; keys.length > i; i++) {
        for (let j = 0; value[i] > j; j++) {
            var patched_tier = data.old ? old_tiers[Number(keys[i])] : tiers[Number(keys[i])];
            selected_rank_act.push({patched_tier: patched_tier, tier: Number(keys[i])});
        }
    }
    return {
        wins: data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].NumberOfWins,
        number_of_games: data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].NumberOfGames,
        final_rank: data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].CompetitiveTier,
        final_rank_patched: data.old
            ? old_tiers[data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].CompetitiveTier]
            : tiers[data.raw.QueueSkills.competitive.SeasonalInfoBySeasonID[data.act_id].CompetitiveTier],
        act_rank_wins: selected_rank_act.reverse(),
        old: data.old,
    };
};
const isOld = ({id, season, request} = {}) => {
    if (season) {
        const act = Object.entries(actids).find(i => i[1] == season)[0];
        if (!act) return false;
        return !['e5', 'e6', 'e7', 'e8', 'e9', 'e10'].some(i => act.startsWith(i));
    }
    if (!id) console.log(request[0].data.Matches);
    return !['e5', 'e6', 'e7', 'e8', 'e9', 'e10'].some(i => id.startsWith(i));
};
const convert_old_rank = ({tier, old} = {}) => {
    if (old && tier >= 21) tier += 3;
    return {tier: tier ?? 0, old: old ?? true};
};
const updateRiotHeader = data => {
    riot_header = {
        Authorization: `Bearer ${data.access_token}`,
        'X-Riot-Entitlements-JWT': data.entitlements_token,
        'X-Riot-ClientPlatform':
            'ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9',
    };
};
const updateauth = async ({val_force = false, mv_force = false, val_retries = 0, mv_retries = 0} = {}) => {
    if (val_force && basedata.production) {
        const riotauth = await auth('HenrikAPI2', 'Duisburg02@');
        if (riotauth.error) {
            console.error(riotauth.error);
            if (val_retries <= 3) return updateauth({val_force: true, val_retries: val_retries + 1});
            if (val_retries > 3) return;
        }
        riot_header = {
            Authorization: `Bearer ${riotauth.access_token}`,
            'X-Riot-Entitlements-JWT': riotauth.entitlements_token,
            'X-Riot-ClientPlatform':
                'ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9',
        };
        await getDB('auth', 'API').updateOne(
            {source: 'val'},
            {
                $set: {
                    access: riotauth.access_token,
                    entitlement: riotauth.entitlements_token,
                },
            },
            {upsert: true}
        );
        return;
    }
    /*if (mv_force && basedata.production) {
        const steamuser = new SteamUser();
        try {
            steamuser.logOn({accountName: 'gforgnetwork', password: 'Y1U1mcGkxJ1d9l%Y2zW!RZ%kz*c^AwJN'});
        } catch (err) {
            throw new Error('Invalid Steam username or password provided!');
        }
        steamuser.on('loggedOn', async details => {
            const ticket = await steamuser.getEncryptedAppTicket(1818750, null);
            const auth_data = await makeRequest({
                url: 'https://dokken-api.wbagora.com/access',
                method: 'POST',
                return_error: true,
                proxy: false,
                overwrite_headers: {
                    'x-hydra-api-key': '51586fdcbd214feb84b0e475b130fce0',
                    'x-hydra-client-id': '47201f31-a35f-498a-ae5b-e9915ecb411e',
                    'x-hydra-user-agent': 'Hydra-Cpp/1.132.0',
                    'Content-Type': 'application/json',
                },
                body: {
                    auth: {
                        fail_on_missing: false,
                        steam: ticket.encryptedAppTicket.toString('hex'),
                    },
                    options: ['wb_network'],
                },
            });
            if (auth_data.response && mv_retries <= 3) return updateauth({mv_force: true, mv_retries: mv_retries + 1});
            if (auth_data.response && mv_retries > 3) return;
            mv_headers = {
                'x-hydra-access-token': auth_data.data.token,
                'x-hydra-api-key': '51586fdcbd214feb84b0e475b130fce0',
                'x-hydra-client-id': '47201f31-a35f-498a-ae5b-e9915ecb411e',
                'x-hydra-user-agent': 'Hydra-Cpp/1.132.0',
                'User-Agent': 'MultiVersus/++UE4+Release-4.26-CL-0 Windows/10.0.19042.1.768.64bit',
            };
            await getDB('auth', 'API').updateOne({source: 'mv'}, {$set: {access: auth_data.data.token}}, {upsert: true});
            return;
        });
    }*/
    if (!val_force) {
        const val_db = await getDB('auth', 'API').findOne({source: 'val'});
        riot_header = {
            Authorization: `Bearer ${val_db.access}`,
            'X-Riot-Entitlements-JWT': val_db.entitlement,
            'X-Riot-ClientPlatform':
                'ew0KCSJwbGF0Zm9ybVR5cGUiOiAiUEMiLA0KCSJwbGF0Zm9ybU9TIjogIldpbmRvd3MiLA0KCSJwbGF0Zm9ybU9TVmVyc2lvbiI6ICIxMC4wLjE5MDQyLjEuMjU2LjY0Yml0IiwNCgkicGxhdGZvcm1DaGlwc2V0IjogIlVua25vd24iDQp9',
        };
    }
    /*if (!mv_force) {
        const mv_db = await getDB('auth', 'API').findOne({source: 'mv'});
        mv_headers = {
            'x-hydra-access-token': mv_db.access,
            'x-hydra-api-key': '51586fdcbd214feb84b0e475b130fce0',
            'x-hydra-client-id': '47201f31-a35f-498a-ae5b-e9915ecb411e',
            'x-hydra-user-agent': 'Hydra-Cpp/1.132.0',
            'User-Agent': 'MultiVersus/++UE4+Release-4.26-CL-0 Windows/10.0.19042.1.768.64bit',
        };
    }*/
    await getClientVersion();
    return;
};
const sixhourcycle = async () => {
    await Promise.all([updatePremierLeaderboard()]).catch(e => e);
};
const hourcycle = async () => {
    await Promise.all([updateContent(), updateLeaderboard(), updateStore(), getMinorValoDataMain()]).catch(e => e);
};
const quarterhourcycle = async () => {
    await Promise.all([getClientVersion(), getMajorValoData(), setLeaderboard(), getMinorValoDataSide()]).catch(e => e);
};
const checkdowntimeMainThread = async () => {
    console.log('downtime main thread');
    const valo = await makeRequest({
        url: 'https://pd.eu.a.pvp.net/mmr/v1/players/54942ced-1967-5f66-8a16-1e0dae875641',
        return_error: true,
        region: 'eu',
    });
    /*const multiversus = await makeRequest({
        url: `https://dokken-api.wbagora.com/profiles/search_queries/get-by-username/run?limit=10&username=HenrikZ3&account_fields=identity&account_fields=presence&account_fields=server_data&account_fields=data&partial_response=1`,
        overwrite_headers: getMultiVersusHeaders(),
        instance: 'multiverse',
        return_error: true,
    });
    if (multiversus.response && multiversus.response.status == 401) updateauth({mv_force: true});*/
    if (valo.response && valo.response.status == 400 && valo.response.data.errorCode == 'BAD_CLAIMS') updateauth({val_force: true});
    const proxy = await makeRequest({
        url: 'https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page=1&page_size=100',
        overwrite_headers: {
            Authorization: `Token i4o4tt6ncipwuyr1cb2zfzsxihp3s8a6irmtvop2`,
        },
        proxy: false,
        return_error: true,
    });
    if (!proxy.response) {
        proxies = proxy.data.results.map(i => {
            return {
                ip: i.proxy_address,
                port: i.port,
                username: i.username,
                pw: i.password,
                country: i.country_code,
                city: i.city_name,
            };
        });
    }
};
const checkdowntime = async () => {
    console.log('downtime');
    updateauth({val_force: false});
    const proxy = await makeRequest({
        url: 'https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page=1&page_size=100',
        overwrite_headers: {
            Authorization: `Token i4o4tt6ncipwuyr1cb2zfzsxihp3s8a6irmtvop2`,
        },
        proxy: false,
        return_error: true,
    });
    if (!proxy.response) {
        proxies = proxy.data.results.map(i => {
            return {
                ip: i.proxy_address,
                port: i.port,
                username: i.username,
                pw: i.password,
                country: i.country_code,
                city: i.city_name,
            };
        });
    }
};
const uuidv4 = () => {
    let dt = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = (dt + Math.random() * 16) % 16 | 0;
        dt = Math.floor(dt / 16);
        return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
    return uuid;
};
const existsMatchFile = id => {
    const split = [id.split('')[0], id.split('')[1]];
    return fs.existsSync(`./matches/${split[0]}/${split[1]}/${id}.json`);
};
const getMatchFile = id => {
    const split = [id.split('')[0], id.split('')[1]];
    if (fs.existsSync(`./matches/${split[0]}/${split[1]}/${id}.json`))
        return JSON.parse(zlib.brotliDecompressSync(fs.readFileSync(`./matches/${split[0]}/${split[1]}/${id}.json`)));
    return null;
};
const saveMatchFile = async (data, id) => {
    const split = [id.split('')[0], id.split('')[1]];
    if (!fs.existsSync(`./matches/${split[0]}/${split[1]}`)) fs.mkdirSync(`./matches/${split[0]}/${split[1]}`, {recursive: true});
    fs.writeFileSync(`./matches/${split[0]}/${split[1]}/${id}.json`, zlib.brotliCompressSync(JSON.stringify(data), {params: {[zlib.constants.BROTLI_PARAM_QUALITY]: 6}}));
    return;
};

async function generatePastLeaderboards() {
    const category = ['e8a3'];
    for (let k = 0; category.length > k; k++) {
        for (let i = 0; affinities.length > i; i++) {
            const c2season = actids[category[k]];
            const req_proxy = proxies[generateNumber()];
            const firstreq = await axios
                .get(
                    `https://pd.${regions.some(k => k == affinities[i]) ? affinities[i] : 'na'}.a.pvp.net/mmr/v1/leaderboards/affinity/${
                        affinities[i]
                    }/queue/competitive/season/${c2season}?startIndex=0&size=1000`,
                    {
                        headers: getRiotHeaders(regions.some(item => item == regions[i]) ? regions[i] : 'na'),
                        timeout: 3000,
                        proxy: {
                            host: req_proxy.ip,
                            port: req_proxy.port,
                            auth: {username: req_proxy.username, password: req_proxy.pw},
                            protocol: 'http',
                        },
                    }
                )
                .catch(error => {
                    return error;
                });
            console.log(firstreq);
            if (!firstreq.response && !firstreq.code) {
                if (!firstreq.data) console.error(firstreq);
                let clusterdata = [...firstreq.data.Players];
                let axiosall = [];
                for (let j = 0; Math.floor(firstreq.data.totalPlayers / 1000) > j; j++) {
                    if (firstreq.data.totalPlayers > Number(`${j + 1}000`))
                        axiosall.push(
                            axios
                                .get(
                                    `https://pd.${regions.some(item => item == affinities[i]) ? affinities[i] : 'na'}.a.pvp.net/mmr/v1/leaderboards/affinity/${
                                        affinities[i]
                                    }/queue/competitive/season/${c2season}?startIndex=${j + 1}000&size=1000`,
                                    {
                                        headers: getRiotHeaders(affinities[i]),
                                        timeout: 3000,
                                        proxy: {
                                            host: req_proxy.ip,
                                            port: req_proxy.port,
                                            auth: {username: req_proxy.username, password: req_proxy.pw},
                                            protocol: 'http',
                                        },
                                    }
                                )
                                .catch(e => e)
                        );
                }
                const fetchLeaderboard = async () => {
                    let leaderboardfetch = await Promise.allSettled(axiosall);
                    leaderboardfetch = leaderboardfetch.map(item => {
                        return item.value;
                    });
                    axiosall = [];
                    for (let j = 0; leaderboardfetch.length > j; j++) {
                        if (leaderboardfetch[j]?.data == undefined && (leaderboardfetch[j].response?.status == 403 || leaderboardfetch[j].code)) {
                            axiosall.push(
                                axios
                                    .get(leaderboardfetch[j].config.url, {
                                        headers: getRiotHeaders(affinities[i]),
                                        timeout: 3000,
                                        proxy: {
                                            host: req_proxy.ip,
                                            port: req_proxy.port,
                                            auth: {username: req_proxy.username, password: req_proxy.pw},
                                            protocol: 'http',
                                        },
                                    })
                                    .catch(e => e)
                            );
                            console.log(leaderboardfetch[j].config.url);
                        }
                        if (leaderboardfetch[j]?.data != undefined) clusterdata.push(...leaderboardfetch[j].data.Players);
                    }
                    if (axiosall.length) return fetchLeaderboard();
                    return;
                };
                await fetchLeaderboard();
                clusterdata = clusterdata.sort((a, b) => a.leaderboardRank - b.leaderboardRank);
                val_leaderboard[affinities[i]] = {
                    last_update: moment().unix(),
                    next_update: moment().unix() + 3060,
                    total_players: firstreq.data.totalPlayers,
                    radiant_threshold: firstreq.data.tierDetails['27'].rankedRatingThreshold,
                    immortal_3_threshold: firstreq.data.tierDetails['26'].rankedRatingThreshold,
                    immortal_2_threshold: firstreq.data.tierDetails['25'].rankedRatingThreshold,
                    immortal_1_threshold: firstreq.data.tierDetails['24'].rankedRatingThreshold,
                    players: clusterdata,
                };
                console.log('success');
                fs.writeFileSync(
                    `./files/valorant/leaderboard/${category[k]}-leaderboard-${affinities[i]}.json`,
                    JSON.stringify({
                        last_update: moment().unix(),
                        next_update: moment().unix() + 3060,
                        total_players: firstreq.data.totalPlayers,
                        radiant_threshold: firstreq.data.tierDetails['27'].rankedRatingThreshold,
                        immortal_3_threshold: firstreq.data.tierDetails['26'].rankedRatingThreshold,
                        immortal_2_threshold: firstreq.data.tierDetails['25'].rankedRatingThreshold,
                        immortal_1_threshold: firstreq.data.tierDetails['24'].rankedRatingThreshold,
                        players: clusterdata,
                    })
                );
            }
        }
    }
}

(async () => {
    await updateauth({});
    await mongodb.connect();
    const proxy = await makeRequest({
        url: 'https://proxy.webshare.io/api/v2/proxy/list/?mode=direct&page=1&page_size=100',
        overwrite_headers: {
            Authorization: `Token i4o4tt6ncipwuyr1cb2zfzsxihp3s8a6irmtvop2`,
        },
        proxy: false,
        timeout: 20000,
        return_error: true,
    });
    if (!proxy.response) {
        proxies = proxy.data.results.map(i => {
            return {
                ip: i.proxy_address,
                port: i.port,
                username: i.username,
                pw: i.password,
                country: i.country_code,
                city: i.city_name,
            };
        });
    }
    await getMajorValoData();
    if (pm2_instance?.cluster_id == 0) await getMinorValoDataMain();
    console.log(val_premier_conferences);
    if (pm2_instance?.cluster_id != 0) await getMinorValoDataSide();
    //updateStore();
    return generatePastLeaderboards();
    setLeaderboard();
    maintenance = false;
    //await updatePremierLeaderboard();
    if (basedata.production == true && pm2_instance?.cluster_id == 0) updateStore();
    //if (basedata.production == true && pm2_instance?.cluster_id == 0) await updatePremierLeaderboard();
    console.log('Start');
})();
setInterval(async () => {
    await getDB('rate-limit-key', 'VALORANT-LABS').updateOne({key: 'key'}, {$set: {current: 0}}, {upsert: true});
}, 120000);

const type = 'application/json';
export {
    basedata,
    type,
    mongodb,
    axios,
    errorhandler,
    moment,
    tiers,
    generateNumber,
    proxies,
    regions,
    calculateElo,
    gamemodes,
    getAgents,
    getMaps,
    getWeapons,
    getPuuid,
    https,
    cheerio,
    webuntis,
    getGamepods,
    fs,
    getRiotHeaders,
    getGear,
    zlib,
    check404,
    canvas,
    old_tiers,
    getCurrentSeason,
    makeRequest,
    getDB,
    category,
    actids,
    patchData,
    isOld,
    locals,
    mv_maps,
    getMultiVersusHeaders,
    mv_agents,
    endpointspecific,
    getSkins,
    getSprays,
    getBuddies,
    getPlayerCards,
    item_types_val,
    getCurrentLeaderboard,
    redis,
    getMatchFile,
    existsMatchFile,
    getActIDs,
    getCategories,
    getContentTiers,
    getEsportSchedules,
    getEsportLeagues,
    getEsportVods,
    convert_old_rank,
    saveMatchFile,
    base_url,
    getPremierSeason,
    getPremierConferences,
    extractHexColorCode,
    affinities,
    getQueues,
    getGamemodes,
    glz_urls,
    getPlayerTitles,
    getCurrentSeasonShort,
};

//GUS
fastify.register(import('./routes/gus/login.js'));
fastify.register(import('./routes/gus/mensacheck.js'));
fastify.register(import('./routes/gus/mensafood.js'));
fastify.register(import('./routes/gus/mensalogin.js'));
fastify.register(import('./routes/gus/mensaorder.js'));
fastify.register(import('./routes/gus/website.js'));

//Internal
fastify.register(import('./routes/internal/valorant_raw.js'));

//R6
fastify.register(import('./routes/r6/profile.js'));
fastify.register(import('./routes/r6/section.js'));

//RundUmGK
fastify.register(import('./routes/rundumgk/archiv.js'));
fastify.register(import('./routes/rundumgk/create.js'));
fastify.register(import('./routes/rundumgk/get.js'));
fastify.register(import('./routes/rundumgk/getbyclass.js'));
fastify.register(import('./routes/rundumgk/post.js'));

//Splitgate
fastify.register(import('./routes/splitgate/queue.js'));
fastify.register(import('./routes/splitgate/store.js'));

//VALORANT
fastify.register(import('./routes/valorant/account.js'));
fastify.register(import('./routes/valorant/bypuuid.js'));
fastify.register(import('./routes/valorant/bypuuid_account.js'));
fastify.register(import('./routes/valorant/content.js'));
fastify.register(import('./routes/valorant/crosshair.js'));
fastify.register(import('./routes/valorant/leaderboard.js'));
fastify.register(import('./routes/valorant/matchid.js'));
fastify.register(import('./routes/valorant/matches.js'));
fastify.register(import('./routes/valorant/mmr-history.js'));
fastify.register(import('./routes/valorant/mmr.js'));
fastify.register(import('./routes/valorant/profile.js'));
fastify.register(import('./routes/valorant/raw.js'));
fastify.register(import('./routes/valorant/status.js'));
fastify.register(import('./routes/valorant/store_featured.js'));
fastify.register(import('./routes/valorant/store_offers.js'));
fastify.register(import('./routes/valorant/website.js'));
fastify.register(import('./routes/valorant/version.js'));
fastify.register(import('./routes/valorant/esports.js'));
fastify.register(import('./routes/valorant/lifetime.js'));
fastify.register(import('./routes/valorant/premier.js'));
fastify.register(import('./routes/valorant/queue_status.js'));

//ValorantLabs
fastify.register(import('./routes/valorantlabs/matchid.js'));
fastify.register(import('./routes/valorantlabs/matches.js'));

//MultiVersus
fastify.register(import('./routes/multiversus/account_search.js'));
fastify.register(import('./routes/multiversus/account_details.js'));
fastify.register(import('./routes/multiversus/matches.js'));
fastify.register(import('./routes/multiversus/match.js'));
fastify.register(import('./routes/multiversus/leaderboard.js'));
fastify.register(import('./routes/multiversus/leaderboard_placements.js'));
fastify.register(import('./routes/multiversus/byid.js'));
fastify.register(import('./routes/multiversus/byid_matches.js'));

fastify.listen({port: basedata.production ? 3600 : basedata.production == false ? 3601 : 3600}, (err, address) => {
    if (err) throw err;
    // Server is now listening on ${address}
    pm2.connect(function () {
        console.log('pm2 connected');
        pm2.list((err, list) => {
            pm2_instance = list.find(i => i.pid == process.pid);
            pm2_instance.cluster_id = list.filter(i => i.name == pm2_instance.name).findIndex(i => i.pid == process.pid);
            if (basedata.production == true && pm2_instance.cluster_id == 0) {
                nodecron.schedule('0 * * * *', hourcycle, {timezone: 'Europe/Berlin'});
                nodecron.schedule('0 */6 * * *', sixhourcycle, {timezone: 'Europe/Berlin'});
            }
            //if (basedata.production == false && pm2_instance.cluster_id == 0) nodecron.schedule('0 */6 * * *', sixhourcycle, {timezone: 'Europe/Berlin'});
            nodecron.schedule('*/15 * * * *', quarterhourcycle, {timezone: 'Europe/Berlin'});
            //if (basedata.production && pm2_instance.cluster_id == 0) nodecron.schedule('*/1 * * * *', checkdowntimeMainThread, {timezone: 'Europe/Berlin'});
            nodecron.schedule('*/1 * * * *', checkdowntime, {timezone: 'Europe/Berlin'});
        });
    });
});

process.on('uncaughtException', function (err) {
    console.error(err);
});
