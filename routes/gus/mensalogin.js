import {axios, type} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/gus/v1/mensa/login', async (req, res) => {
        console.log(req.body);
        let bodyFormData = new URLSearchParams();
        bodyFormData.append('idCardNumber', req.body.card);
        bodyFormData.append('pin', req.body.pin);
        bodyFormData.append('action', 'login');
        const request = await axios
            .post('https://gus.sams-on.de/server/userLogin.php', bodyFormData, {headers: {'content-type': 'application/x-www-form-urlencoded'}})
            .catch(error => {
                return error;
            });
        console.log(request);
        if (
            request.data.user.error ==
            'Der eingegebene Pin oder Benutzername war leider falsch. Achtung: Wenn der Pin 3 mal Falsch eingegeben wurde, wird der Account gesperrt.'
        )
            return res.code(500).send({message: 'Falsche Login Daten'});
        if (request.data.user.error == 'Sie haben Ihre Pin zu oft falsch eingeben. Ihr Account wurde für 10 Minuten gesperrt')
            return res.code(500).send({message: 'Account gesperrt'});
        return res.send({
            message: 'Login Daten korrekt',
            data: request.data,
            sessionid: request.headers['set-cookie']
                .find(item => item.includes('PHPSESSID'))
                .split(';')[0]
                .substr(10),
        });
    });
}
