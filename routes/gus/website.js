import {axios, moment, cheerio} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/gus/v1/website', async (req, res) => {
        const request = await axios.get('https://www.gymnasium-unterrieden.de/').catch(error => {
            return error;
        });
        if (request.response) return res.code(500).send(request.response.data);
        const $ = cheerio.load(request.data);
        const all_articles = Array.from(Object.values($('.uk-panel')));
        let beta_articles = [];
        moment.locale('de');
        function createText(text_elements) {
            let text_a = [];
            for (const text of text_elements) {
                text_a.push($(text).text());
            }
            return text_a.join(' ');
        }
        for (const article of all_articles) {
            if (typeof article == 'object' && article.xml == undefined && typeof article.children != 'function') {
                let short_text =
                    article.children.find(item => item.name == 'div' && item.attribs.class == 'uk-margin').children.filter(item => item.name == 'p').length > 1
                        ? createText(article.children.find(item => item.name == 'div' && item.attribs.class == 'uk-margin').children.filter(item => item.name == 'p'))
                        : $(article.children.find(item => item.name == 'div' && item.attribs.class == 'uk-margin').children.filter(item => item.name == 'p')[0]).text();
                console.log(article.children.find(item => item.name == 'p' && item.attribs.class == undefined && item.children[0].name == 'a').children[0].attribs);
                beta_articles.push({
                    title: $(
                        article.children
                            .find(item => item.name == 'h3' && item.attribs.class == 'uk-panel-title')
                            .children.find(item => item.name == 'a' && item.attribs.class == 'uk-link-reset')
                    ).text(),
                    short_text: short_text,
                    time: moment(
                        article.children.find(item => item.name == 'p' && item.attribs.class == 'uk-article-meta').children.find(item => item.name == 'time').attribs
                            .datetime
                    ).format('LLLL'),
                    image:
                        article.children.find(item => item.name == 'div' && item.attribs.class == 'uk-text-center uk-panel-teaser') != undefined
                            ? `https://gymnasium-unterrieden.de${
                                  article.children
                                      .find(item => item.name == 'div' && item.attribs.class == 'uk-text-center uk-panel-teaser')
                                      .children.find(item => item.name == 'div' && item.attribs.class == 'uk-overlay uk-overlay-hover ')
                                      .children.find(item => item.name == 'img').attribs.src
                              }`
                            : 'https://www.gymnasium-unterrieden.de/templates/unterrieden2020/images/designer/fb080f41d857ce5370ae1a0dd0c50801_2017072017.36.12.jpg',
                    link:
                        article.children.find(item => item.name == 'p' && item.attribs.class == undefined && item.children[0].name == 'a').children[0].attribs !=
                        undefined
                            ? `https://gymnasium-unterrieden.de${
                                  article.children.find(item => item.name == 'p' && item.attribs.class == undefined && item.children[0].name == 'a').children[0].attribs
                                      .href
                              }`
                            : 'https://www.gymnasium-unterrieden.de',
                });
            }
        }
        return res.send(beta_articles);
    });
}
