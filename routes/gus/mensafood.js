import {axios, moment} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/gus/v1/mensa/food', async (req, res) => {
        console.log(req.body);
        moment.locale('en');
        let bodyFormData = new URLSearchParams();
        bodyFormData.append('weekNumber', req.body.week != null ? req.body.week : moment().format('WW'));
        bodyFormData.append('weekYear', req.body.year != null ? req.body.year : moment().format('YYYY'));
        const request = await axios
            .post('https://gus.sams-on.de/server/userDayMenuPlan.php', bodyFormData, {
                headers: {cookie: `PHPSESSID=${req.body.id}`, 'content-type': 'application/x-www-form-urlencoded'},
            })
            .catch(error => {
                return error;
            });
        let bodyFormData2 = new URLSearchParams();
        bodyFormData2.append('weekNumber', req.body.nweek != null ? req.body.nweek : request.data.weekNumber + 1);
        bodyFormData2.append(
            'weekYear',
            req.body.nyear != null ? req.body.nyear : request.data.weekNumber == 52 || request.data.weekNumber == 53 ? request.data.weekYear + 1 : request.data.weekYear
        );
        const nrequest = await axios
            .post('https://gus.sams-on.de/server/userDayMenuPlan.php', bodyFormData2, {
                headers: {cookie: `PHPSESSID=${req.body.id}`, 'content-type': 'application/x-www-form-urlencoded'},
            })
            .catch(error => {
                return error;
            });
        return res.send({cdata: request.data, ndata: nrequest.data});
    });
}
