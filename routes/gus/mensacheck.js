import {axios, moment} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/gus/v1/mensa/check', async (req, res) => {
        console.log(req.body);
        if (req.body.id != null) {
            let bodyFormData = new URLSearchParams();
            bodyFormData.append('weekNumber', moment().format('ww'));
            bodyFormData.append('weekYear', moment().format('yyyy'));
            const request = await axios
                .post('https://gus.sams-on.de/server/userDayMenuPlan.php', bodyFormData, {
                    headers: {cookie: `PHPSESSID=${req.body.id}`, 'content-type': 'application/x-www-form-urlencoded'},
                })
                .catch(error => {
                    return error;
                });
            console.log(request.data.use);
            if (request.data.use == true) return res.code(200).send({success: true});
            const fetchlogin = await axios.post('https://api.henrikdev.xyz/gus/v1/mensa/login', {card: req.body.uname, pin: req.body.pw}).catch(error => {
                return error;
            });
            console.log(fetchlogin);
            return res
                .code(200)
                .send({success: false, error: fetchlogin.response ? true : false, data: fetchlogin.response ? fetchlogin.response.data : fetchlogin.data});
        }
    });
}
