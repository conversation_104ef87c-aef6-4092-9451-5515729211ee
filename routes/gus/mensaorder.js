import {axios, type, moment} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/gus/v1/mensa/order', async (req, res) => {
        console.log(req.body);
        let bodyFormData = new URLSearchParams();
        bodyFormData.append('day1Menu', req.body.one);
        bodyFormData.append('day2Menu', req.body.two);
        bodyFormData.append('day3Menu', req.body.three);
        bodyFormData.append('day4Menu', req.body.four);
        bodyFormData.append('day5Menu', req.body.five);
        const request = await axios
            .post(`https://gus.sams-on.de/server/userDayMenuPlan.php?weekNumber=${req.body.week}&weekYear=${req.body.year}&action=order`, bodyFormData, {
                headers: {cookie: `PHPSESSID=${req.body.id}`, 'content-type': 'application/x-www-form-urlencoded'},
            })
            .catch(error => {
                return error;
            });
        if (request.response) return res.code(500).type(type).send(request.reponse.data);
        return res.type(type).send(request.data);
    });
}
