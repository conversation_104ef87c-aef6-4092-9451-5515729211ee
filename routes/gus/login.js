import {webuntis, moment} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/gus/v2/vertretungsplan/login', async (req, res) => {
        const untis = new webuntis('gym unterrieden', req.body.username, req.body.pw, 'herakles.webuntis.com');
        const request = await untis.login().catch(error => {
            return error;
        });
        if (request instanceof Error && request.error.message == 'bad credentials') return res.code(500).send(request);
        //const timetable = await untis.getOwnTimetable()
        const timetable = await untis.getOwnTimetableForRange(
            new Date(moment().startOf('isoWeek').format('YYYY-MM-DDTHH:mm:ss')),
            new Date(moment().endOf('isoWeek').add(14, 'days').format('YYYY-MM-DDTHH:mm:ss'))
        );
        var patched = [];
        var dayId = [];
        var days = [];
        var months = [];
        var years = [];
        moment.locale('en');
        if (typeof timetable == 'object')
            for (element of timetable) {
                if (dayId.find(item => item == element.date) == undefined) {
                    dayId.push(element.date);
                    const filter = timetable.filter(item => item.date == element.date);
                    if (days.find(item => item == moment(webuntis.convertUntisDate(filter[0].date)).format('DD')) == undefined)
                        days.push(moment(webuntis.convertUntisDate(filter[0].date)).format('DD'));
                    if (months.find(item => item == moment(webuntis.convertUntisDate(filter[0].date)).format('MM')) == undefined)
                        months.push(moment(webuntis.convertUntisDate(filter[0].date)).format('MM'));
                    if (years.find(item => item == moment(webuntis.convertUntisDate(filter[0].date)).format('YYYY')) == undefined)
                        years.push(moment(webuntis.convertUntisDate(filter[0].date)).format('YYYY'));
                    var edit = [];
                    for (lesson of filter) {
                        const convert = webuntis.convertUntisDate(lesson.date);
                        console.log(lesson);
                        edit.push({
                            start: lesson.startTime,
                            end: lesson.endTime,
                            date: lesson.date,
                            patcheddate: moment(convert).format('MMM DD, YYYY'),
                            lehrer: {
                                kürzel: lesson.te[0].name,
                                name: lesson.te[0].longname,
                            },
                            fach: {
                                id: lesson.su[0].name,
                                name: lesson.su[0].longname,
                            },
                            raum: {
                                raum: lesson.ro[0]?.name,
                                beschreibung: lesson.ro[0]?.longname,
                            },
                            entfällt: lesson.code != null ? true : false,
                            info: lesson.info != null ? lesson.info : null,
                        });
                    }
                    edit = edit.sort((item1, item2) => item1.start - item2.start);
                    patched.push(edit);
                }
            }
        return res
            .code(200)
            .send({login: request, timetable: patched, days: days.sort((item1, item2) => item1 - item2).join(','), months: months.join(','), years: years.join(',')});
    });
}
