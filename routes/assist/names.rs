use std::sync::Arc;
use axum::body::Body;
use axum::extract::{State};
use axum::{debug_handler, extract::J<PERSON>};
use axum::http::HeaderMap;
use axum::response::Response;
use crate::{AppState, error_handler};
use crate::structs::post_payloads::AssistV1NamesPayload;
use serde_json::json;
use crate::structs::errors::get_base_error;
use crate::structs::http_clients::{redis_fetch, RedisFetchOptions};
use crate::structs::pvp_api::PVPNameServiceV2;
use crate::structs::responses::AssistV1NamesResponse;

#[debug_handler]
pub async fn assist_names(State(app_state): State<Arc<AppState>>, headers: HeaderMap, Json(payload): Json<AssistV1NamesPayload>) -> Response {
    let auth = headers.get("Authorization");
    if auth.is_none() {
        return error_handler(vec![get_base_error(403)]);
    }
    if auth.unwrap() != "HDEV-9f4c30ec-242c-4940-a935-37ed4a697f68" {
        return error_handler(vec![get_base_error(403)]);
    }
    /*let redis = app_state.pool.clone();
    let conn = redis.get().await.expect("Failed to get Redis connection from pool");*/
    let conn = app_state.redis.clone();
    let names = redis_fetch::<Vec<PVPNameServiceV2>>(
        RedisFetchOptions {
            redis_client: Some(conn),
            url: String::from("https://pd.eu.a.pvp.net/name-service/v2/players"),
            store: format!("assist;names;{}", payload.puuids.join(";")),
            data: json!(&payload.puuids),
            method: String::from("PUT"),
            ..RedisFetchOptions::default()
        }
    ).await;
    match names {
        Ok(names) => {
            Response::builder()
                .status(200)
                .header("Content-Type", "application/json")
                .body(Body::from(serde_json::to_string(&payload.puuids.iter().map(|i| {
                    let api_entry = names.data.iter().find(|x| x.Subject == *i);
                    AssistV1NamesResponse {
                        puuid: i.to_string(),
                        name: match api_entry {
                            Some(x) => if x.GameName.len() > 0 { Some(x.GameName.clone()) } else { None },
                            None => None,
                        },
                        tag: match api_entry {
                            Some(x) => if x.TagLine.len() > 0 { Some(x.TagLine.clone()) } else { None },
                            None => None,
                        },
                        exists: api_entry.is_some(),
                    }
                }).collect::<Vec<_>>()).unwrap())).unwrap()
        }
        Err(_) => {
            error_handler(vec![get_base_error(500)])
        }
    }
}