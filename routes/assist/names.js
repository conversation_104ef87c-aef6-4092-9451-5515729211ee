import {errorhandler, makeRequest, redis} from '../../server.js';

export default async function (fastify, opts, done) {
    fastify.post(
        '/assist/v1/names',
        {
            body: {
                schema: {
                    type: 'object',
                    required: ['puuids'],
                    additionalProperties: false,
                    properties: {
                        puuids: {
                            type: 'array',
                            items: 'string',
                        },
                    },
                },
            },
        },
        async (req, res) => {
            if (!req.headers.authorization) return errorhandler({status: 401, res, errors: [{instance: 'own'}]});
            if (req.headers.authorization != 'HDEV-9f4c30ec-242c-4940-a935-37ed4a697f68') return errorhandler({status: 403, res, errors: [{instance: 'own'}]});
            if (!req.body.puuids) return errorhandler({status: 400, res, errors: [{instance: 'own'}]});
            const redis_data = await redis.get(`assist;names;${req.body.puuids.join('|')}`);
            const names =
                JSON.parse(redis_data) ??
                (await makeRequest({
                    url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                    method: 'PUT',
                    body: req.body.puuids,
                    return_error: true,
                    region: 'eu',
                    res,
                }));
            if (!names.data) errorhandler({status: names.response?.status ?? 500, res, errors: [{instance: 'riot'}]});
            if (!redis_data && names.data) redis.set(`assist;names;${req.body.puuids.join('|')}`, JSON.stringify({data: names.data}), {EX: 1500});
            return res.code(200).send(
                req.body.puuids.map(i => {
                    const api_entry = names.data.find(k => k.Subject == i);
                    return {
                        puuid: i,
                        name: api_entry ? (api_entry.GameName != '' ? api_entry.GameName : null) : null,
                        tag: api_entry ? (api_entry.TagLine != '' ? api_entry.TagLine : null) : null,
                        exists: api_entry ? true : false,
                    };
                })
            );
        }
    );
}
