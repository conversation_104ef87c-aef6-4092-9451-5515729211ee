import {errorhandler, existsMatchFile, regions, fs, zlib, axios, getRiotHeaders, generateNumber, getMatchFile} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/internal/v1/valorant-raw', async (req, res) => {
        const rdata = req.body;
        rdata.region = ['br', 'latam'].some(i => i == rdata.region.toLowerCase()) ? 'na' : rdata.region.toLowerCase();
        const proxies = [
            {ip: '************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '***********', country: 'us'},
            {ip: '***************', country: 'br'},
            {ip: '***********', country: 'us'},
            {ip: '**************', country: 'br'},
            {ip: '***************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '*************', country: 'br'},
            {ip: '**************', country: 'es'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '***************', country: 'br'},
            {ip: '***********', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '***************', country: 'br'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '*************', country: 'es'},
            {ip: '**************', country: 'br'},
            {ip: '************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '**************', country: 'br'},
            {ip: '*************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '**************', country: 'br'},
            {ip: '***************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '**************', country: 'br'},
            {ip: '************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '**************', country: 'br'},
            {ip: '***************', country: 'br'},
            {ip: '***************', country: 'us'},
            {ip: '***********', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '***********', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '*************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '***************', country: 'de'},
            {ip: '*************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '**************', country: 'es'},
            {ip: '*************', country: 'us'},
            {ip: '*************9', country: 'es'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
            {ip: '***************', country: 'us'},
            {ip: '*************', country: 'es'},
            {ip: '***************', country: 'us'},
            {ip: '************', country: 'us'},
        ];
        if (!regions.includes(rdata.region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        if (!['matchdetails'].includes(rdata.type.toLowerCase()))
            return errorhandler({res, status: 400, errors: [{instance: 'own', code: 112, details: ['matchdetails']}]});
        if (rdata.type == 'matchdetails') {
            const values = !Array.isArray(rdata.value) ? [rdata.value] : rdata.value;
            const fetch = [];
            const proxy = proxies[generateNumber(proxies.length)];
            for (let i = 0; values.length > i; i++) {
                fetch.push(
                    existsMatchFile(values[i])
                        ? {data: getMatchFile(values[i])}
                        : axios
                              .get(`https://pd.${rdata.region}.a.pvp.net/match-details/v1/matches/${values[i]}`, {
                                  timeout: 8000,
                                  headers: getRiotHeaders(rdata.region),
                                  proxy: {host: proxy.ip, port: 3128, protocol: 'http'},
                              })
                              .catch(e => e)
                );
            }
            const results = [];
            const responses = (await Promise.allSettled(fetch)).map(i => i.value);
            for (let i = 0; responses.length > i; i++) {
                if (responses[i].response || responses[i].code) results.push({error: true, code: responses[i].status, id: responses[i].config.url.split('/')[6]});
                else {
                    if (responses[i].config) {
                        const json = JSON.stringify(responses[i].data);
                        if (!json?.length) console.log(responses[i]);
                        const compressed = zlib.brotliCompressSync(json, {params: {[zlib.constants.BROTLI_PARAM_QUALITY]: 6}});
                        //await fs.promises.writeFile(`./matches/${responses[i].config.url.split('/')[6]}.json`, compressed).catch(e => console.error);
                        fs.writeFileSync(`./matches2/${responses[i].config.url.split('/')[6]}.json`, compressed);
                    }
                    results.push(responses[i].data);
                }
            }
            return res.code(200).send(!Array.isArray(rdata.value) ? results[0] : results);
        }
    });
}
