import {fs, getSkins, getBuddies, getSprays, getPlayerCards, item_types_val, getContentTiers, getPlayerTitles} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/store-offers', async (req, res) => {
        const sdata = JSON.parse(fs.readFileSync('./files/valorant/store/store_offers.json'));
        switch (req.params.version) {
            case 'v1': {
                return res.code(200).send({status: 200, data: sdata});
            }
            case 'v2': {
                const content_tiers = getContentTiers();
                return res.code(200).send({
                    status: 200,
                    data: {
                        offers: sdata.Offers.filter(i => i.Rewards[0].ItemTypeID != 'ea6fcd2e-8373-4137-b1c0-b458947aa86d').map(i => {
                            const type = item_types_val[i.Rewards[0].ItemTypeID];
                            const dataset =
                                type == 'skin_level'
                                    ? getSkins()
                                    : type == 'buddy'
                                    ? getBuddies()
                                    : type == 'spray'
                                    ? {levels: null, base: getSprays()}
                                    : type == 'player_card'
                                    ? {levels: null, base: getPlayerCards()}
                                    : type == 'player_title'
                                    ? {levels: getPlayerTitles(), base: getPlayerTitles()}
                                    : null;
                            const found = (dataset.levels ?? dataset.base).find(o => o.uuid == i.Rewards[0].ItemID);
                            const extended_dataset = found.parent ? dataset.base.find(o => o.uuid == found.parent) : found;
                            const content_tier = extended_dataset ? content_tiers.find(o => o.uuid == extended_dataset.contentTierUuid) : null;
                            return {
                                offer_id: i.OfferID,
                                cost: i.Cost['85ad13f7-3d1b-5128-9eb2-7cd8ee0b5741'],
                                name: found.titleText ?? found.displayName,
                                icon: found.displayIcon ?? null,
                                type,
                                skin_id: found.parent ?? null,
                                content_tier: content_tier
                                    ? {
                                          name: content_tier.displayName,
                                          dev_name: content_tier.devName,
                                          icon: content_tier.displayIcon,
                                      }
                                    : null,
                            };
                        }),
                    },
                });
            }
            default: {
                return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
            }
        }
    });
}
