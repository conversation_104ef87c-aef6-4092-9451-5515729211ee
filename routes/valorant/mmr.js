import {
    errorhandler,
    regions,
    getCategories,
    makeRequest,
    tiers,
    getActIDs,
    calculateElo,
    getPuuid,
    old_tiers,
    patchData,
    isOld,
    getDB,
    redis,
    convert_old_rank,
    base_url,
} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/mmr/:region/:name/:tag', async (req, res) => {
        const name = req.params.name.replaceAll('_', '').replaceAll('+', ' ').trim();
        const tag = req.params.tag.replaceAll('_', '').replaceAll('+', ' ').trim();
        const version = req.params.version;
        const actids = getActIDs();
        const category = getCategories();
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        let db = await getPuuid({name, tag});
        if (!db) {
            const puuid = await makeRequest({
                url: `${base_url}/valorant/v1/account/${encodeURI(name)}/${encodeURI(tag)}`,
                proxy: false,
                res,
                timeout: 7500,
                return_error: true,
                overwrite_headers: req.headers,
            });
            if (puuid.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (puuid.response) return errorhandler({status: puuid.response.status, res, errors: [{instance: 'riot'}]});
            db = puuid.data.data;
        }
        const redis_data_names = await redis.get(`names;${db.puuid}`);
        switch (version) {
            case 'v1': {
                const redis_data = await redis.get(`mmr;v1;${region};${db.puuid}`);
                const request = (
                    await Promise.allSettled([
                        JSON.parse(redis_data) ??
                            makeRequest({
                                url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${db.puuid}/competitiveupdates?queue=competitive`,
                                region,
                                return_error: true,
                                res,
                            }),
                        JSON.parse(redis_data_names) ??
                            makeRequest({
                                method: 'PUT',
                                url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                                res,
                                body: [db.puuid],
                                region: 'eu',
                                return_error: true,
                            }),
                    ])
                ).map(i => i.value);
                if (res.sent) return;
                if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
                if (request[0].response || request[0].code) return errorhandler({status: request[0].response?.status ?? 500, res, errors: [{instance: 'riot'}]});
                if (!redis_data && request[0].data) redis.set(`mmr;v1;${region};${db.puuid}`, JSON.stringify({data: request[0].data}), {EX: 300});
                if (!redis_data_names && request[1].data) redis.set(`names;${db.puuid}`, JSON.stringify({data: request[1].data}), {EX: 300});
                if (!request[0].data.Matches.length)
                    return res.code(200).send({
                        status: 200,
                        data: {
                            currenttier: null,
                            currenttierpatched: null,
                            images: null,
                            ranking_in_tier: null,
                            mmr_change_to_last_game: null,
                            elo: null,
                            name: null,
                            tag: null,
                            old: true,
                        },
                    });
                const comp = request[0].data.Matches.filter(item => item.TierAfterUpdate != 0 && item.SeasonID != '').sort(
                    (item1, item2) => item2.MatchStartTime - item1.MatchStartTime
                );
                if (!comp || !comp.length)
                    return res.code(200).send({
                        status: 200,
                        data: {
                            currenttier: null,
                            currenttierpatched: null,
                            images: null,
                            ranking_in_tier: null,
                            mmr_change_to_last_game: null,
                            elo: null,
                            name: null,
                            tag: null,
                            old: true,
                        },
                    });
                let riotid = {
                    name: request[1].data?.[0]?.GameName ?? null,
                    tag: request[1].data?.[0]?.TagLine ?? null,
                };
                if (riotid.name != null && riotid.tag != null) getDB('puuids').updateOne({puuid: db.puuid}, {$set: {name: riotid.name, tag: riotid.tag}});
                return res.code(200).send({
                    status: 200,
                    data: {
                        currenttier: comp[0].TierAfterUpdate,
                        currenttierpatched: isOld({season: comp[0].SeasonID, request}) ? old_tiers[comp[0].TierAfterUpdate] : tiers[comp[0].TierAfterUpdate],
                        images: {
                            small: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/smallicon.png`,
                            large: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/largeicon.png`,
                            triangle_down: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/ranktriangledownicon.png`,
                            triangle_up: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/ranktriangleupicon.png`,
                        },
                        ranking_in_tier: comp[0].RankedRatingAfterUpdate,
                        mmr_change_to_last_game: comp[0].RankedRatingEarned,
                        elo: calculateElo({tier: comp[0].TierAfterUpdate, progress: comp[0].RankedRatingAfterUpdate}),
                        name: riotid.name,
                        tag: riotid.tag,
                        old: isOld({season: comp[0].SeasonID}),
                    },
                });
            }
            case 'v2': {
                if (req.query.filter && !category.includes(req.query.filter))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 105, details: category}]});
                if (req.query.season && !category.includes(req.query.season))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 115, details: category}]});
                const filter = req.query.filter ?? req.query.season ?? null;
                const redis_data = await redis.get(`mmr;v2;${region};${db.puuid}`);
                const request = (
                    await Promise.allSettled([
                        JSON.parse(redis_data) ??
                            makeRequest({
                                url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${db.puuid}?queue=competitive`,
                                region,
                                return_error: true,
                                res,
                            }),
                        JSON.parse(redis_data_names) ??
                            makeRequest({
                                url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                                method: 'PUT',
                                body: [db.puuid],
                                region: 'eu',
                                return_error: true,
                                res,
                            }),
                        makeRequest({
                            url: `${base_url}/valorant/v1/by-puuid/mmr/${region}/${db.puuid}`,
                            proxy: false,
                            return_error: true,
                        }),
                    ])
                ).map(i => i.value);
                if (res.sent) return;
                if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
                if (request[0].response || request[0].code) return errorhandler({status: request[0].response?.status ?? 500, res, errors: [{instance: 'riot'}]});
                if (!redis_data && request[0].data) redis.set(`mmr;v2;${region};${db.puuid}`, JSON.stringify({data: request[0].data}), {EX: 300});
                if (!redis_data_names && request[1].data) redis.set(`names;${db.puuid}`, JSON.stringify({data: request[1].data}), {EX: 300});
                if (!request[1].data) redis.del(`names;${db.puuid}`);
                let riotid = {
                    name: request[1].data?.[0]?.GameName ?? null,
                    tag: request[1].data?.[0]?.TagLine ?? null,
                };
                if (riotid.name != null && riotid.tag != null) getDB('puuids').updateOne({puuid: db.puuid}, {$set: {name: riotid.name, tag: riotid.tag}});
                if (filter) {
                    const act_id = actids[filter.toLowerCase()];
                    const selected_rank_act = [];
                    const keys =
                        request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]?.WinsByTier != null
                            ? Object.keys(request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].WinsByTier)
                            : 0;
                    const value =
                        request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]?.WinsByTier != null
                            ? Object.values(request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].WinsByTier)
                            : 0;
                    for (let i = 0; keys.length > i; i++) {
                        for (let j = 0; value[i] > j; j++) {
                            var patched_tier = isOld({id: filter.toLowerCase()}) ? old_tiers[Number(keys[i])] : tiers[Number(keys[i])];
                            selected_rank_act.push({patched_tier: patched_tier, tier: Number(keys[i])});
                        }
                    }
                    return res.code(200).send({
                        status: 200,
                        data: {
                            wins: request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].NumberOfWins
                                : null,
                            number_of_games: request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].NumberOfGames
                                : null,
                            final_rank: request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].CompetitiveTier
                                : null,
                            final_rank_patched: isOld({id: filter.toLowerCase()})
                                ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                    ? old_tiers[request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].CompetitiveTier]
                                    : null
                                : request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                ? tiers[request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].CompetitiveTier]
                                : null,
                            act_rank_wins: selected_rank_act,
                            old: isOld({id: filter.toLowerCase()}),
                        },
                    });
                }
                const season_data = [];
                for (let i = 0; category.length > i; i++) {
                    if (!request[0].data) console.error(request, riotid, db);
                    const season_data_single = patchData({act_id: actids[category[i]], raw: request[0].data, old: isOld({id: category[i]})});
                    season_data.push({id: category[i], data: season_data_single});
                }
                if (request[2].response) request[2].error = true;
                let by_season = {};
                let highest_rank = {old: false, tier: 0, patched_tier: 'Unranked', season: null, converted: 0};
                for (const act of category) {
                    let act_data = season_data.find(item => item.id == act).data;
                    by_season[act] = act_data;
                    const converted = convert_old_rank({tier: act_data.act_rank_wins?.[0]?.tier, old: act_data.old});
                    if (converted.tier > highest_rank.converted)
                        highest_rank = {
                            old: act_data.old,
                            tier: act_data.act_rank_wins[0].tier,
                            patched_tier: act_data.act_rank_wins[0].patched_tier,
                            season: act,
                            converted: converted.tier,
                        };
                }
                delete highest_rank.converted;
                return res.code(200).send({
                    status: 200,
                    data: {
                        name: riotid.name,
                        tag: riotid.tag,
                        puuid: db.puuid,
                        current_data: {
                            currenttier: !request[2].error ? request[2].data.data.currenttier : null,
                            currenttierpatched: !request[2].error ? request[2].data.data.currenttierpatched : null,
                            images: !request[2].error
                                ? {
                                      small: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                          request[2].data.data.currenttier ?? 0
                                      }/smallicon.png`,
                                      large: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                          request[2].data.data.currenttier ?? 0
                                      }/largeicon.png`,
                                      triangle_down: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                          request[2].data.data.currenttier ?? 0
                                      }/ranktriangledownicon.png`,
                                      triangle_up: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                          request[2].data.data.currenttier ?? 0
                                      }/ranktriangleupicon.png`,
                                  }
                                : null,
                            ranking_in_tier: !request[2].error ? request[2].data.data.ranking_in_tier : null,
                            mmr_change_to_last_game: !request[2].error ? request[2].data.data.mmr_change_to_last_game : null,
                            elo: !request[2].error ? request[2].data.data.elo : null,
                            games_needed_for_rating: request[0].data.QueueSkills.competitive.CurrentSeasonGamesNeededForRating,
                            old: !request[2].error ? request[2].data.data.old : true,
                        },
                        highest_rank,
                        by_season,
                    },
                });
            }
            default: {
                return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
            }
        }
        return;
    });
}
