use crate::structs::errors::{error_handler, ErrorCodes};
use crate::structs::paths::Crosshair<PERSON>uery;
use axum::body::Body;
use axum::extract::Query;
use axum::response::Response;

pub async fn crosshair(Query(query): Query<CrosshairQuery>) -> Response {
    if query.id.is_none() {
        return error_handler(vec![ErrorCodes::InvalidCrosshairCode]);
    }
    let data = reqwest::get(&format!(
        "http://127.0.0.1:60002/valorant/v1/crosshair/{}",
        query.id.clone().unwrap()
    ))
    .await;
    if data.is_err() {
        return error_handler(vec![ErrorCodes::InvalidCrosshairCode]);
    }
    let body = data.unwrap().bytes().await.unwrap();
    Response::builder()
        .status(200)
        .header("Content-Type", "image/png")
        .body(Body::from(body))
        .unwrap()
}
