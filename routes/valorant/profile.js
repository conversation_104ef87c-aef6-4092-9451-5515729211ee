import {errorhandler, axios, moment, type} from '../../server.js';

export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/profile/:name/:tag', async (req, res) => {
        var name = encodeURI(req.params.name).replace('%0A', '');
        var tag = encodeURI(req.params.tag).replace('%0A', '');
        var version = req.params.version;
        var API_Key = 'HDEV-VAPI-Personal-29b410e5-f326-45f1-b09b-60bbfa45cd5a';
        res.header('X-Handled-Server', 'API-Server 1');
        if (version != 'v2') return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
        if (!req.headers['z-api-token-trn']) return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
        if (req.headers['z-api-token-trn'] != API_Key) return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
        var matchhistory = await axios.get(`https://api.tracker.gg/api/v2/valorant/standard/matches/riot/${name}%23${tag}`).catch(error => {
            return error;
        });
        if (matchhistory.response) {
            var playerlookup = await axios.get(`https://api.tracker.gg/api/v2/valorant/standard/profile/riot/${name}%23${tag}/`).catch(error => {
                return error;
            });
            return errorhandler({status: playerlookup.response.status, res, errors: [{instance: 'own'}]});
        }
        var req_profile_base = axios.get(`https://api.tracker.gg/api/v2/valorant/standard/profile/riot/${name}%23${tag}`);
        var req_agent_comp = axios.get(`https://api.tracker.gg/api/v2/valorant/standard/profile/riot/${name}%23${tag}/segments/agent?playlist=competitive`);
        var req_agent_spike = axios.get(`https://api.tracker.gg/api/v2/valorant/standard/profile/riot/${name}%23${tag}/segments/agent?playlist=spikerush`);
        var req_agent_unrated = axios.get(`https://api.tracker.gg/api/v2/valorant/standard/profile/riot/${name}%23${tag}/segments/agent?playlist=unrated`);
        var raw_base = await axios.all([req_profile_base, req_agent_comp, req_agent_spike, req_agent_unrated]).catch(error => {
            return error;
        });
        if (raw_base.response) return errorhandler({status: raw_base.response.status, res, errors: [{instance: 'own'}]});
        var overviewdata = raw_base[0].data.data.segments.filter(segment => segment.type == 'playlist');
        var rankeddata = overviewdata.filter(item => item.attributes.key == 'competitive');
        var extracteddata = overviewdata.filter(item => item.attributes.key != 'deathmatch');

        var playtime = 0;
        var matches = 0;
        var kills = 0;
        var deaths = 0;
        var assists = 0;
        var kd = 0;
        var headshots = 0;
        var headshotspct = 0;
        var wins = 0;
        var fb = 0;
        var aces = 0;
        var clutches = 0;
        var flawless = 0;
        for (let i = 0; overviewdata.length > i; i++) {
            playtime = playtime + overviewdata[i].stats.timePlayed.value;
            kills = kills + overviewdata[i].stats.kills.value;
            deaths = deaths + overviewdata[i].stats.deaths.value;
            assists = assists + overviewdata[i].stats.assists.value;
            kd = kd + overviewdata[i].stats.kDRatio.value;
            headshots = headshots + overviewdata[i].stats.headshots.value;
            headshotspct = headshotspct + overviewdata[i].stats.headshotsPercentage.value;
            wins = wins + overviewdata[i].stats.matchesWon.value;
            fb = fb + overviewdata[i].stats.firstBloods.value;
            aces = aces + overviewdata[i].stats.aces.value;
            clutches = clutches + overviewdata[i].stats.clutches.value;
            flawless = flawless + overviewdata[i].stats.flawless.value;
        }
        kd = kd / overviewdata.length;
        headshotspct = headshotspct / overviewdata.length;

        var winspct = 0;
        for (let i = 0; extracteddata.length > i; i++) {
            winspct = winspct + extracteddata[i].stats.matchesWinPct.value;
            matches = matches + extracteddata[i].stats.matchesPlayed.value;
        }
        winspct = winspct / extracteddata.length;
        var rankeddata2 = rankeddata.length == 0 ? 'Unranked' : rankeddata[0].stats.rank.metadata.tierName;
        var calculatedagents = [];
        var allagents = [];
        var raw_agent = [];
        for (let i = 0; raw_base[1].data.data.length > i; i++) {
            raw_agent.push(raw_base[1].data.data[i]);
        }
        for (let i = 0; raw_base[2].data.data.length > i; i++) {
            raw_agent.push(raw_base[2].data.data[i]);
        }
        for (let i = 0; raw_base[3].data.data.length > i; i++) {
            raw_agent.push(raw_base[3].data.data[i]);
        }
        for (let i = 0; raw_agent.length > i; i++) {
            allagents.push(raw_agent[i].metadata.name);
        }
        allagents = [...new Set(allagents)];
        var sortedagents = [];
        for (let i = 0; allagents.length > i; i++) {
            sortedagents1 = raw_agent.filter(item => item.metadata.name == allagents[i]);
            sortedagents.push(sortedagents1);
        }
        for (let i = 0; sortedagents.length > i; i++) {
            var agentcache = [];
            for (let j = 0; sortedagents[i].length > j; j++) {
                var playtime_2 = 0;
                playtime_2 = playtime_2 + sortedagents[i][j].stats.timePlayed.value;
                var matches2 = 0;
                matches2 = matches2 + sortedagents[i][j].stats.matchesPlayed.value;
                var kills_2 = 0;
                kills_2 = kills_2 + sortedagents[i][j].stats.kills.value;
                var deaths_2 = 0;
                deaths_2 = deaths_2 + sortedagents[i][j].stats.deaths.value;
                var assists_2 = 0;
                assists_2 = assists_2 + sortedagents[i][j].stats.assists.value;
                var kd_2 = 0;
                kd_2 = kd_2 + sortedagents[i][j].stats.kDRatio.value;
                var headshots_2 = 0;
                headshots_2 = headshots_2 + sortedagents[i][j].stats.headshots.value;
                var headshotspct_2 = 0;
                headshotspct_2 = headshotspct_2 + sortedagents[i][j].stats.headshotsPercentage.value;
                var wins_2 = 0;
                wins_2 = wins_2 + sortedagents[i][j].stats.matchesWon.value;
                var winspct_2 = 0;
                winspct_2 = winspct_2 + sortedagents[i][j].stats.matchesWinPct.value;
                var fb2 = 0;
                fb2 = fb2 + sortedagents[i][j].stats.firstBloods.value;
                var aces2 = 0;
                aces2 = aces2 + sortedagents[i][j].stats.aces.value;
                var clutches2 = 0;
                clutches2 = clutches2 + sortedagents[i][j].stats.clutches.value;
                var flawless2 = 0;
                flawless2 = flawless2 + sortedagents[i][j].stats.flawless.value;

                var json = {
                    agent: sortedagents[i][j].metadata.name,
                    agenturl: sortedagents[i][j].metadata.imageUrl,
                    playtimeraw: playtime_2,
                    matches: matches2,
                    kills: kills_2,
                    deaths: deaths_2,
                    assists: assists_2,
                    kdratio: Number(kd_2.toFixed(2)),
                    headshots: headshots_2,
                    headshotpercentage: Number(headshotspct_2.toFixed(2)),
                    wins: Number(wins_2),
                    winpercentage: Number(winspct_2.toFixed(2)),
                    firstbloods: fb2,
                    aces: aces2,
                    clutches: clutches2,
                    flawless: flawless2,
                };
                agentcache.push(json);
            }
            var playtime_4 = 0;
            var matches4 = 0;
            var kills_4 = 0;
            var deaths_4 = 0;
            var assists_4 = 0;
            var kd_4 = 0;
            var headshots_4 = 0;
            var headshotspct_4 = 0;
            var wins_4 = 0;
            var winspct_4 = 0;
            var fb4 = 0;
            var aces4 = 0;
            var clutches4 = 0;
            var flawless4 = 0;
            for (let i = 0; agentcache.length > i; i++) {
                playtime_4 = playtime_4 + agentcache[i].playtimeraw;
                matches4 = matches4 + agentcache[i].matches;
                kills_4 = kills_4 + agentcache[i].kills;
                deaths_4 = deaths_4 + agentcache[i].deaths;
                assists_4 = assists_4 + agentcache[i].assists;
                kd_4 = kd_4 + agentcache[i].kdratio;
                headshots_4 = headshots_4 + agentcache[i].headshots;
                wins_4 = wins_4 + agentcache[i].wins;
                headshotspct_4 = headshotspct_4 + agentcache[i].headshotpercentage;
                winspct_4 = winspct_4 + agentcache[i].winpercentage;
                fb4 = fb4 + agentcache[i].firstbloods;
                aces4 = aces4 + agentcache[i].aces;
                clutches4 = clutches4 + agentcache[i].clutches;
                flawless4 = flawless4 + agentcache[i].flawless;
            }
            kd_4 = kd_4 / agentcache.length;
            headshotspct_4 = headshotspct_4 / agentcache.length;
            winspct_4 = winspct_4 / agentcache.length;
            var json = {
                agent: agentcache[0].agent,
                agenturl: agentcache[0].agenturl,
                playtime: {
                    playtimepatched:
                        moment.duration(playtime_4).days() +
                        'D ' +
                        moment.duration(playtime_4).hours() +
                        'H ' +
                        moment.duration(playtime_4).minutes() +
                        'M ' +
                        moment.duration(playtime_4).seconds() +
                        'S',
                    playtimedays: moment.duration(playtime_4).days(),
                    playtimehours: moment.duration(playtime_4).hours(),
                    playtimeminutes: moment.duration(playtime_4).minutes(),
                    playtimeseconds: moment.duration(playtime_4).seconds(),
                    playtimeraw: playtime_4,
                },
                matches: matches4,
                kills: kills_4,
                deaths: deaths_4,
                assists: assists_4,
                kdratio: Number(kd_4.toFixed(2)),
                headshots: headshots_4,
                headshotpercentage: Number(headshotspct_4.toFixed(2)),
                wins: Number(wins_4),
                winpercentage: Number(winspct_4.toFixed(2)),
                firstbloods: fb4,
                aces: aces4,
                clutches: clutches4,
                flawless: flawless4,
            };
            calculatedagents.push(json);
        }
        var json = {
            user: raw_base[0].data.data.platformInfo.platformUserIdentifier,
            status: '200',
            stats: {
                rank: rankeddata2,
                playtime: {
                    playtimepatched:
                        moment.duration(playtime).days() +
                        'D ' +
                        moment.duration(playtime).hours() +
                        'H ' +
                        moment.duration(playtime).minutes() +
                        'M ' +
                        moment.duration(playtime).seconds() +
                        'S',
                    playtimedays: moment.duration(playtime).days(),
                    playtimehours: moment.duration(playtime).hours(),
                    playtimeminutes: moment.duration(playtime).minutes(),
                    playtimeseconds: moment.duration(playtime).seconds(),
                    playtime_raw: playtime,
                },
                matches: matches,
                kills: kills,
                deaths: deaths,
                assists: assists,
                kdratio: Number(kd.toFixed(2)),
                headshots: headshots,
                headshotpercentage: Number(headshotspct.toFixed(2)),
                wins: Number(wins),
                winpercentage: Number(winspct.toFixed(2)),
                firstbloods: fb,
                aces: aces,
                clutches: clutches,
                flawless: flawless,
                playercard: raw_base[0].data.data.platformInfo.avatarUrl,
            },
            agents: calculatedagents,
        };
        res.code(200).type(type).send(json);
    });
}
