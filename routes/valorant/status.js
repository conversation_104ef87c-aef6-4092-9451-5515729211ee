import {makeRequest, errorhandler, regions} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/status/:region', async (req, res) => {
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        const status = await makeRequest({url: `https://valorant.secure.dyn.riotcdn.net/channels/public/x/status/${region}.json`, res, proxy: false, instance: 'riot'});
        if (res.sent) return;
        return res.code(200).send({status: 200, region, data: {maintenances: status.data.maintenances, incidents: status.data.incidents}});
    });
}
