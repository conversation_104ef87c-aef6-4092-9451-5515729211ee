import {getPremierConferences, getDB, errorhandler, affinities, getPremierSeason} from '../../server.js';
import {PremierTeam, BasePremierTeam, Conference, PremierSeason} from '../../methods/premier.js';

export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/premier/:name/:tag', async (req, res) => {
        const name = req.params.name.trim();
        const tag = req.params.tag.trim();
        const team = new PremierTeam({name, tag}, res);
        await team._patch();
        await team.respond();
    });
    fastify.get('/valorant/v1/premier/:name/:tag/history', async (req, res) => {
        const name = req.params.name.trim();
        const tag = req.params.tag.trim();
        const team = new PremierTeam({name, tag}, res);
        await team._superInit();
        await team._fetchHistory();
        if (team.API_HISTORY) await team._checkGames();
        await team.respondHistory();
    });
    fastify.get('/valorant/v1/premier/:id', async (req, res) => {
        const team = new PremierTeam(req.params.id, res);
        await team._patch();
        await team.respond();
    });
    fastify.get('/valorant/v1/premier/:id/history', async (req, res) => {
        const team = new PremierTeam(req.params.id, res);
        await team._superInit();
        await team._fetchHistory();
        if (team.API_HISTORY) await team._checkGames();
        await team.respondHistory();
    });
    fastify.get('/valorant/v1/premier/search', async (req, res) => {
        const conferences = getPremierConferences();
        const query = {season: getPremierSeason()};
        const errors = [];
        if (req.query.name) query.name = req.query.name.replaceAll('$', '');
        if (req.query.tag) query.tag = req.query.tag.replaceAll('$', '');
        if (req.query.division) {
            if (isNaN(req.query.division)) errors.push({instance: 'own', code: 121, details: null});
            if (Number(req.query.division) < 1 || Number(req.query.division) > 21)
                errors.push({
                    instance: 'own',
                    code: 122,
                    details: null,
                });
            query.div = Number(req.query.division);
        }
        if (req.query.conference) {
            if (!conferences.some(i => i.key == req.query.conference.toUpperCase()))
                errors.push({
                    instance: 'own',
                    code: 123,
                    details: [...conferences.map(i => i.key)],
                });
            query.conference = req.query.conference.toUpperCase().replaceAll('$', '');
        }
        if (req.query.id) {
            if (req.query.name || req.query.tag) errors.push({instance: 'own', code: 116, details: null});
            query.id = req.query.id;
        }
        if (errors.length) return errorhandler({res, status: 400, errors});
        const teams = (
            await getDB('premier_teams')
                .find(query)
                .limit(Object.values(query).length ? 0 : 50)
                .sort({div: -1, ranking: 1})
                .collation({locale: 'en', strength: 2, alternate: 'shifted'})
                .toArray()
        ).map(i => {
            const team = new BasePremierTeam(i);
            team._parse();
            return team.toJSON();
        });
        return res.code(200).send({status: 200, data: teams});
    });
    fastify.get('/valorant/v1/premier/conferences', async (req, res) => {
        const conferences = getPremierConferences().map(i => {
            return new Conference(i).toJSON();
        });
        return res.code(200).send({status: 200, data: conferences});
    });
    fastify.get('/valorant/v1/premier/seasons/:affinity', async (req, res) => {
        if (!affinities.includes(req.params.affinity))
            return errorhandler({
                res,
                status: 400,
                errors: [{instance: 'own', code: 104, details: [...affinities]}],
            });
        const seasons = (
            await getDB('premier_metadata')
                .find({
                    type: 'season',
                    affinity: req.params.affinity,
                })
                .toArray()
        ).map(i => {
            return new PremierSeason(i).toJSON();
        });
        return res.code(200).send({status: 200, data: seasons});
    });
    fastify.get('/valorant/v1/premier/leaderboard/:affinity', async (req, res) => {
        if (!affinities.includes(req.params.affinity))
            return errorhandler({
                res,
                status: 400,
                errors: [{instance: 'own', code: 104, details: [...affinities]}],
            });
        const teams = (
            await getDB('premier_teams')
                .find({
                    affinity: req.params.affinity,
                    season: getPremierSeason(),
                })
                .sort({div: -1, ranking: 1})
                .toArray()
        ).map(i => {
            const team = new BasePremierTeam(i);
            team._parse();
            return team.toJSON();
        });
        return res.code(200).send({status: 200, data: teams});
    });
    fastify.get('/valorant/v1/premier/leaderboard/:affinity/:conference', async (req, res) => {
        const errors = [];
        const conferences = getPremierConferences();
        if (!affinities.includes(req.params.affinity))
            errors.push({
                instance: 'own',
                code: 104,
                details: [...affinities],
            });
        if (!conferences.some(i => i.key == req.params.conference))
            errors.push({
                instance: 'own',
                code: 123,
                details: [...conferences.map(i => i.key)],
            });
        if (conferences.find(i => i.key == req.params.conference)?.affinity != req.params.affinity)
            errors.push({
                instance: 'own',
                code: 124,
                details: null,
            });
        if (errors.length) return errorhandler({res, status: 400, errors});
        const teams = (
            await getDB('premier_teams')
                .find({affinity: req.params.affinity, conference: req.params.conference, season: getPremierSeason()})
                .sort({div: -1, ranking: 1})
                .toArray()
        ).map(i => {
            const team = new BasePremierTeam(i);
            team._parse();
            return team.toJSON();
        });
        return res.code(200).send({status: 200, data: teams});
    });
    fastify.get('/valorant/v1/premier/leaderboard/:affinity/:conference/:division', async (req, res) => {
        const errors = [];
        const conferences = getPremierConferences();
        if (!affinities.includes(req.params.affinity))
            errors.push({
                instance: 'own',
                code: 104,
                details: [...affinities],
            });
        if (!conferences.some(i => i.key == req.params.conference))
            errors.push({
                instance: 'own',
                code: 123,
                details: [...conferences.map(i => i.key)],
            });
        if (conferences.find(i => i.key == req.params.conference)?.affinity != req.params.affinity)
            errors.push({
                instance: 'own',
                code: 124,
                details: null,
            });
        if (isNaN(req.params.division)) errors.push({instance: 'own', code: 121, details: null});
        if (Number(req.params.division) < 1 || Number(req.params.division) > 21)
            errors.push({
                instance: 'own',
                code: 122,
                details: null,
            });
        if (errors.length) return errorhandler({res, status: 400, errors});
        const teams = (
            await getDB('premier_teams')
                .find({
                    affinity: req.params.affinity,
                    conference: req.params.conference,
                    div: Number(req.params.division),
                    season: getPremierSeason(),
                })
                .sort({div: -1, ranking: 1})
                .toArray()
        ).map(i => {
            const team = new BasePremierTeam(i);
            team._parse();
            return team.toJSON();
        });
        return res.code(200).send({status: 200, data: teams});
    });
}
