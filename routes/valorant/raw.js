import {errorhandler, makeRequest, regions, fs, zlib, redis, existsMatchFile, getMatchFile, saveMatchFile} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/valorant/v1/raw', async (req, res) => {
        const rdata = req.body;
        const urls = {
            matchdetails: 'https://pd.${rdata.region}.a.pvp.net/match-details/v1/matches/${rdata.value}',
            matchhistory: 'https://pd.${rdata.region}.a.pvp.net/match-history/v1/history/${rdata.value}',
            mmr: 'https://pd.${rdata.region}.a.pvp.net/mmr/v1/players/${rdata.value}',
            competitiveupdates: 'https://pd.${rdata.region}.a.pvp.net/mmr/v1/players/${rdata.value}/competitiveupdates',
        };
        rdata.region = ['br', 'latam'].some(i => i == rdata.region.toLowerCase()) ? 'na' : rdata.region.toLowerCase();
        if (!regions.includes(rdata.region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        if (!['matchdetails', 'matchhistory', 'mmr', 'competitiveupdates'].includes(rdata.type.toLowerCase()))
            return errorhandler({res, status: 400, errors: [{instance: 'own', code: 112, details: ['matchdetails', 'matchhistory', 'mmr', 'competitiveupdates']}]});
        if (rdata.type == 'matchdetails') {
            const values = !Array.isArray(rdata.value) ? [rdata.value] : rdata.value;
            const fetch = [];
            for (let i = 0; values.length > i; i++) {
                fetch.push(
                    existsMatchFile(values[i])
                        ? {data: getMatchFile(values[i])}
                        : makeRequest({
                              url: `https://pd.${rdata.region}.a.pvp.net/match-details/v1/matches/${values[i]}`,
                              res,
                              region: rdata.region,
                              return_error: true,
                          })
                );
            }
            const results = [];
            const responses = (await Promise.allSettled(fetch)).map(i => i.value);
            if (responses.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            for (let i = 0; responses.length > i; i++) {
                if (responses[i].response) results.push({error: true, code: responses[i].response.status, id: responses[i].config.url.split('/')[6]});
                if (!responses[i].response) {
                    if (responses[i].config) await saveMatchFile(responses[i].data, responses[i].config.url.split('/')[6]);
                    results.push(responses[i].data);
                }
            }
            return res.code(200).send(!Array.isArray(rdata.value) ? results[0] : results);
        }
        if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(rdata.value))
            return errorhandler({res, status: 400, errors: [{instance: 'own', code: 113}]});
        let cache;
        switch (rdata.type.toLowerCase()) {
            case 'matchhistory': {
                cache = await redis.get(`history;${rdata.region};${rdata.value};${rdata.queries}`);
                break;
            }
            case 'mmr': {
                cache = await redis.get(`mmr;v2;${rdata.region};${rdata.value};${rdata.queries}`);
                break;
            }
            case 'competitiveupdates': {
                cache = await redis.get(`competitiveupdates;${rdata.region};${rdata.value};${rdata.queries}`);
                break;
            }
        }
        const fetch =
            JSON.parse(cache) ??
            (await makeRequest({
                url: `${eval('`' + urls[rdata.type.toLowerCase()] + '`')}${rdata.queries != null ? rdata.queries : ''}`,
                res,
                region: rdata.region,
                instance: 'riot',
            }));
        if (res.sent) return;
        switch (rdata.type.toLowerCase()) {
            case 'matchhistory': {
                if (!cache && fetch.data) redis.set(`history;${rdata.region};${rdata.value};${rdata.queries}`, JSON.stringify({data: fetch.data}), {EX: 300});
                break;
            }
            case 'mmr': {
                if (!cache && fetch.data) redis.set(`mmr;v2;${rdata.region};${rdata.value};${rdata.queries}`, JSON.stringify({data: fetch.data}), {EX: 300});
                break;
            }
            case 'competitiveupdates': {
                if (!cache && fetch.data) redis.set(`competitiveupdates;${rdata.region};${rdata.value};${rdata.queries}`, JSON.stringify({data: fetch.data}), {EX: 300});
                break;
            }
        }
        return res.code(200).send(fetch.data);
    });
}
