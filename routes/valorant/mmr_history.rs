use crate::methods::utils::RateLimiting;
use crate::structs::errors::{ error_handler, ErrorC<PERSON> };
use crate::structs::helper::{ fetch_mmr_history_by_puuid, get_valorant_account_by_id, get_valorant_account_by_name, update_lifetime_mmr_history };
use crate::structs::parser::{ parse_mmr_history, parse_mmr_history_v2 };
use crate::structs::paths::{ MMRHistoryV1ByIDPath, MMRHistoryV1Path, MMRHistoryV2ByIDPath, MMRHistoryV2Path };
use crate::{ check_affinity, check_platform, AppState };
use axum::body::Body;
use axum::extract::{ Path, State };
use axum::response::Response;
use axum::Extension;
use std::sync::Arc;
use uuid::Uuid;

pub async fn get_mmr_history_by_name(
	Path(path): Path<MMRHistoryV1Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let platform = "pc";

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, "pc", &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), platform).await;
	}
	let formatted_response = parse_mmr_history(mmr_history_data.data, &account).await;
	rl.redis_cache_ttl.store(mmr_history_data.ttl as isize, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&formatted_response).unwrap()))
		.unwrap()
}

pub async fn get_mmr_history_by_id(
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>,
	Path(path): Path<MMRHistoryV1ByIDPath>
) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let platform = "pc";

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, "pc", &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), platform).await;
	}
	let formatted_response = parse_mmr_history(mmr_history_data.data, &account).await;
	rl.redis_cache_ttl.store(mmr_history_data.ttl as isize, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&formatted_response).unwrap()))
		.unwrap()
}

pub async fn get_mmr_history_v2_by_name(
	Path(path): Path<MMRHistoryV2Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let platform = check_platform(&path.platform);
	if !platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let platform = path.platform.to_lowercase();

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &account.puuid, &platform, &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), &platform).await;
	}
	let formatted_response = parse_mmr_history_v2(mmr_history_data.data, &account).await;
	rl.redis_cache_ttl.store(mmr_history_data.ttl as isize, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&formatted_response).unwrap()))
		.unwrap()
}

pub async fn get_mmr_history_v2_by_id(
	Path(path): Path<MMRHistoryV2ByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let validate_region = check_affinity(&path.affinity);
	if !validate_region {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}
	let platform = check_platform(&path.platform);
	if !platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let platform = path.platform.to_lowercase();

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.err().unwrap()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;
	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mmr_history_data = fetch_mmr_history_by_puuid(conn, &path.puuid, &platform, &region).await;
	if mmr_history_data.is_err() {
		return error_handler(vec![mmr_history_data.err().unwrap()]);
	}
	let mmr_history_data = mmr_history_data.unwrap();
	if !mmr_history_data.is_from_redis {
		rl.background_requests.fetch_add(1, std::sync::atomic::Ordering::SeqCst);
		update_lifetime_mmr_history(&client, &account.puuid, mmr_history_data.data.matches.clone(), &platform).await;
	}
	let formatted_response = parse_mmr_history_v2(mmr_history_data.data, &account).await;
	rl.redis_cache_ttl.store(mmr_history_data.ttl as isize, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&formatted_response).unwrap()))
		.unwrap()
}
