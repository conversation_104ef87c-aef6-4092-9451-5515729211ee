import {moment, errorhandler, makeRequest} from '../../server.js';

export default async function (fastify, opts, done) {
    /*fastify.get('/valorant/v1/website/:countrycode', async (req, res) => {
        const ccode = req.params.countrycode;
        const countries = ['en-us', 'en-gb', 'de-de', 'es-es', 'fr-fr', 'it-it', 'ru-ru', 'tr-tr', 'es-mx', 'ja-jp', 'ko-kr', 'pt-br', 'pl-pl', 'vi-vn'];
        const category = ['game_updates', 'dev', 'esports', 'announcements', 'patch_notes'];
        if (!countries.includes(ccode)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 114, details: countries}]});
        const website = await makeRequest({
            url: `https://playvalorant.com/page-data/${ccode}/page-data.json`,
            res,
            proxy: false,
            instance: 'riot',
            overwrite_headers: {
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.102 Safari/537.36 Edg/104.0.1293.70',
            },
        });
        if (res.sent) return;
        let articles = website.data.result.data.allContentstackArticles.nodes.filter(item => item.article_tags || item.category);
        articles = articles.sort((item1, item2) => moment(item2.date).unix() - moment(item1.date).unix());
        if (req.query.filter && !category.includes(req.query.filter)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 105, details: category}]});
        if (req.query.category && !category.includes(req.query.category))
            return errorhandler({res, status: 400, errors: [{instance: 'own', code: 105, details: category}]});
        const filter = req.query.filter ?? req.query.category ?? null;
        if (category.includes(filter)) {
            const filtered_articles =
                filter == 'patch_notes'
                    ? articles.filter(item => item.article_tags && item.article_tags.some(item2 => item2.machine_name == filter))
                    : articles.filter(item => item.category && item.category.find(item2 => item2.machine_name == filter));
            const final_articles = [];
            for (let i = 0; filtered_articles.length > i; i++) {
                final_articles.push({
                    banner_url: filtered_articles[i].banner.url,
                    category: filtered_articles[i].article_tags?.some(k => k.machine_name == 'patch_notes')
                        ? 'patch_notes'
                        : filtered_articles[i].category[0].machine_name,
                    date: filtered_articles[i].date,
                    external_link: filtered_articles[i].external_link.length ? filtered_articles[i].external_link : null,
                    title: filtered_articles[i].title,
                    url: `https://playvalorant.com/${ccode}${filtered_articles[i].url.url}`,
                });
            }
            return res.code(200).send({status: 200, data: final_articles});
        }
        const final_articles = [];
        for (let i = 0; articles.length > i; i++) {
            final_articles.push({
                banner_url: articles[i].banner.url,
                category: articles[i].article_tags?.some(k => k.machine_name == 'patch_notes') ? 'patch_notes' : articles[i].category[0].machine_name,
                date: articles[i].date,
                external_link: articles[i].external_link.length ? articles[i].external_link : null,
                title: articles[i].title,
                url: `https://playvalorant.com/${ccode}${articles[i].url.url}`,
            });
        }
        return res.code(200).send({status: 200, data: final_articles});
    });*/
    fastify.get('/valorant/v1/website/:countrycode', async (req, res) => {
        const ccode = req.params.countrycode;
        const countries = ['en-us', 'en-gb', 'de-de', 'es-es', 'fr-fr', 'it-it', 'ru-ru', 'tr-tr', 'es-mx', 'ja-jp', 'ko-kr', 'pt-br', 'pl-pl', 'vi-vn'];
        const category = ['game_updates', 'dev', 'esports', 'announcements', 'patch_notes'];
        if (!countries.includes(ccode))
            return errorhandler({
                res,
                status: 400,
                errors: [{instance: 'own', code: 114, details: countries}],
            });
        if (req.query.filter && !category.includes(req.query.filter))
            return errorhandler({
                res,
                status: 400,
                errors: [{instance: 'own', code: 105, details: category}],
            });
        if (req.query.category && !category.includes(req.query.category))
            return errorhandler({res, status: 400, errors: [{instance: 'own', code: 105, details: category}]});
        const website = await makeRequest({
            url: `https://content.publishing.riotgames.com/publishing-content/v1.0/public/newsfeed?multigamePromoChannelId=riot_mobile_news_feeds&multigameContentGroupId=valorant&locale=${ccode}&categories=announcements&categories=community&categories=media&categories=lore&categories=riot_games&categories=merch&categories=dev&categories=game-updates`,
            res,
            proxy: false,
            instance: 'riot',
            overwrite_headers: {
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.102 Safari/537.36 Edg/104.0.1293.70',
            },
        });
        if (res.sent) return;
        let articles = website.data.data.items.sort((a, b) => new Date(b.publishedAt).getMilliseconds() - new Date(a.publishedAt).getMilliseconds());
        const filter = req.query.filter ?? req.query.category ?? null;
        if (category.includes(filter)) {
            const filtered_articles =
                filter == 'patch_notes' ? articles.filter(i => i.headline.toLowerCase().includes('patch')) : articles.filter(i => i.category.id == filter);
            const final_articles = [];
            for (let i = 0; filtered_articles.length > i; i++) {
                final_articles.push({
                    banner_url: filtered_articles[i].featureImage.url,
                    category: filtered_articles[i].headline.toLowerCase().includes('patch') ? 'patch_notes' : filtered_articles[i].category.id,
                    date: filtered_articles[i].publishedAt,
                    external_link: filtered_articles[i].action.type != 'weblink' ? filtered_articles[i].action.url : null,
                    title: filtered_articles[i].headline,
                    url: filtered_articles[i].action.url,
                });
            }
            return res.code(200).send({status: 200, data: final_articles});
        }
        const final_articles = [];
        for (let i = 0; articles.length > i; i++) {
            final_articles.push({
                banner_url: articles[i].featureImage.url,
                category: articles[i].headline.toLowerCase().includes('patch') ? 'patch_notes' : articles[i].category.id,
                date: articles[i].publishedAt,
                external_link: articles[i].action.type != 'weblink' ? articles[i].action.url : null,
                title: articles[i].headline,
                url: articles[i].action.url,
            });
        }
        return res.code(200).send({status: 200, data: final_articles});
    });
}
