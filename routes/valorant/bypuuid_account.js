import {errorhandler, moment, regions, makeRequest, getDB, fs, zlib, existsMatchFile, getMatchFile, saveMatchFile} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/by-puuid/account/:puuid', async (req, res) => {
        const puuid = req.params.puuid;
        const db = await getDB('puuids').findOne({puuid});
        async function getRegion() {
            const axiosf = [];
            for (const region of regions) {
                axiosf.push(makeRequest({url: `https://pd.${region}.a.pvp.net/match-history/v1/history/${puuid}`, return_error: true, res, region}));
            }
            let results = await Promise.all(axiosf);
            results = results.filter(i => i.response || i.code).length == 4 ? results[0] : results.find(i => i.status == 200);
            if (results.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (results.response) return errorhandler({status: results.response.status, res, errors: [{instance: 'riot'}]});
            if (!results.data.History.length) {
                await getDB('puuids').updateOne(
                    {puuid: puuid},
                    {
                        $set: {
                            region: results.data.region,
                        },
                    },
                    {upsert: true}
                );
                return res.code(200).send({
                    status: 200,
                    data: {
                        puuid,
                        region: results.data.region,
                        account_level: db?.account_level ?? 0,
                        name: db?.name ?? '',
                        tag: db?.tag ?? '',
                        card: {
                            small: db?.card ? `https://media.valorant-api.com/playercards/${db.card}/smallart.png` : null,
                            large: db?.card ? `https://media.valorant-api.com/playercards/${db.card}/largeart.png` : null,
                            wide: db?.card ? `https://media.valorant-api.com/playercards/${db.card}/wideart.png` : null,
                            id: db?.card ?? null,
                        },
                        last_update: 'Now',
                        last_update_raw: moment().unix(),
                    },
                });
            }
            const matchdata = existsMatchFile(results.data.History[0].MatchID)
                ? {data: getMatchFile(results.data.History[0].MatchID)}
                : await makeRequest({
                      url: `https://pd.${results.data.region}.a.pvp.net/match-details/v1/matches/${results.data.History[0].MatchID}`,
                      res,
                      region: results.data.region,
                  });
            if (res.sent) return;
            if (matchdata.config && !matchdata.response && matchdata.data) await saveMatchFile(matchdata.data, results.data.History[0].MatchID);
            const player = matchdata.data.players.find(item => item.subject == puuid);
            if (player == null) return errorhandler({status: 500, res, errors: [{instance: 'riot', code: 103}]});
            await getDB('puuids').updateOne(
                {puuid},
                {
                    $set: {
                        region: results.data.region,
                        account_level: player ? player.accountLevel : null,
                        name: player.gameName,
                        tag: player.tagLine,
                        card: player.playerCard,
                        last_update: moment().unix(),
                        last_match: matchdata.data.matchInfo.gameStartMillis,
                    },
                },
                {upsert: true}
            );
            return res.code(200).send({
                status: 200,
                data: {
                    puuid: puuid,
                    region: results.data.region,
                    account_level: player ? player.accountLevel : null,
                    name: player.gameName,
                    tag: player.tagLine,
                    card: {
                        small: `https://media.valorant-api.com/playercards/${player.playerCard}/smallart.png`,
                        large: `https://media.valorant-api.com/playercards/${player.playerCard}/largeart.png`,
                        wide: `https://media.valorant-api.com/playercards/${player.playerCard}/wideart.png`,
                        id: player.playerCard,
                    },
                    last_update: 'Now',
                    last_update_raw: moment().unix(),
                },
            });
        }
        if (!db?.region) return getRegion();
        if ((req.query.force && req.query.force == 'true') || !db.last_update || moment().unix() - db.last_update > 3600) {
            const matchlist = await makeRequest({
                url: `https://pd.${db.region}.a.pvp.net/match-history/v1/history/${db.puuid}`,
                res,
                region: db.region,
            });
            if (res.sent) return;
            if (!matchlist.data?.History?.length)
                return res.status(200).send({status: 200, data: {puuid: db.puuid, region: db.region, account_level: db.account_level, name: db.name, tag: db.tag}});
            const matchfetch = existsMatchFile(matchlist.data.History[0].MatchID)
                ? {data: getMatchFile(matchlist.data.History[0].MatchID)}
                : await makeRequest({
                      url: `https://pd.${db.region}.a.pvp.net/match-details/v1/matches/${matchlist.data.History[0].MatchID}`,
                      res,
                      region: db.region,
                  });
            if (res.sent) return;
            if (matchfetch.response)
                return res.status(200).send({status: 200, data: {puuid: db.puuid, region: db.region, account_level: db.account_level, name: db.name, tag: db.tag}});
            if (matchfetch.config && !matchfetch.response && matchfetch.data) await saveMatchFile(matchfetch.data, matchlist.data.History[0].MatchID);
            const player = matchfetch.data.players.find(item => item.subject == db.puuid);
            await getDB('puuids').updateOne(
                {puuid: db.puuid},
                {
                    $set: {
                        name: player.gameName,
                        tag: player.tagLine,
                        account_level: player != null ? player.accountLevel : null,
                        card: player.playerCard,
                        last_update: moment().unix(),
                        last_match: matchfetch.data.matchInfo.gameStartMillis,
                    },
                }
            );
            return res.code(200).send({
                status: 200,
                data: {
                    puuid: db.puuid,
                    region: db.region,
                    account_level: player != null ? player.accountLevel : null,
                    name: player.gameName,
                    tag: player.tagLine,
                    card: {
                        small: `https://media.valorant-api.com/playercards/${player.playerCard}/smallart.png`,
                        large: `https://media.valorant-api.com/playercards/${player.playerCard}/largeart.png`,
                        wide: `https://media.valorant-api.com/playercards/${player.playerCard}/wideart.png`,
                        id: player.playerCard,
                    },
                    last_update: 'Now',
                    last_update_raw: moment().unix(),
                },
            });
        }
        return res.code(200).send({
            status: 200,
            data: {
                puuid: db.puuid,
                region: db.region,
                account_level: db.account_level,
                name: db.name,
                tag: db.tag,
                card: {
                    small: `https://media.valorant-api.com/playercards/${db.card}/smallart.png`,
                    large: `https://media.valorant-api.com/playercards/${db.card}/largeart.png`,
                    wide: `https://media.valorant-api.com/playercards/${db.card}/wideart.png`,
                    id: db.card,
                },
                last_update: moment(moment.unix(db.last_update).toISOString()).from(new Date().toISOString()),
                last_update_raw: db.last_update,
            },
        });
    });
}
