import {ValorantQueueStatus} from '../../methods/queue_status.js';
import {makeRequest, errorhandler, affinities, glz_urls, redis} from '../../server.js';

export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/queue-status/:affinity', async (req, res) => {
        const region = req.params.affinity.toLowerCase();
        if (!affinities.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: affinities}]});
        const redis_cache = await redis.get(`queue;status;${region}`);
        const data =
            (await JSON.parse(redis_cache)) ??
            (await makeRequest({
                url: `${glz_urls[region]}/parties/v1/parties/customgameconfigs`,
                region,
                return_error: true,
                res,
            }));
        if (data.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
        if (data.response || data.code) return errorhandler({status: data.response?.status ?? 500, res, errors: [{instance: 'riot'}]});
        if (!redis_cache && data.data) redis.set(`queue;status;${region}`, JSON.stringify({data: data.data}), {EX: 300});
        const status = new ValorantQueueStatus(data.data);
        res.code(200).send({status: 200, data: status.toJSON()});
    });
}
