use crate::methods::utils::RateLimiting;
use crate::structs::errors::get_base_error;
use crate::structs::helper::{get_maps, get_queues};
use crate::structs::http_clients::{redis_fetch, RedisFetchOptions};
use crate::structs::paths::VersionPath;
use crate::structs::pvp_api::{PVPCustomGameConfig, PVPCustomGameConfigQueue};
use crate::structs::responses::{
    QueueStatusV1, QueueStatusV1Data, QueueStatusV1GameRules, QueueStatusV1HighSkill,
    QueueStatusV1IDNamePair, QueueStatusV1Map, QueueStatusV1Maps, QueueStatusV1PartySize,
    QueueStatusV1SkillDisparity,
};
use crate::{check_affinity, error_handler, AppState, ErrorCodes, GLZ_URLS, VALORANT_TIERS};
use axum::body::Body;
use axum::extract::{Path, State};
use axum::response::Response;
use axum::Extension;
use std::sync::Arc;

#[allow(non_snake_case)]
pub async fn QueueStatus(
    Path(path): Path<VersionPath>,
    State(app_state): State<Arc<AppState>>,
    Extension(extension): Extension<Arc<RateLimiting>>,
) -> Response {
    let validate_region = check_affinity(&path.affinity);
    if !validate_region {
        return error_handler(vec![ErrorCodes::InvalidRegion]);
    }

    let rl = extension.clone();
    //let redis = app_state.pool.clone();
    //let conn = redis.get().await.expect("Failed to get Redis connection from pool");
    let conn = app_state.redis.clone();

    let status = redis_fetch::<PVPCustomGameConfig>(RedisFetchOptions {
        redis_client: Some(conn),
        url: format!(
            "{}/parties/v1/parties/customgameconfigs",
            GLZ_URLS.get(path.affinity.as_str()).unwrap()
        ),
        store: format!("queue;status;{}", path.affinity),
        ..RedisFetchOptions::default()
    })
    .await;
    match status {
        Ok(v) => {
            rl.redis_cache_ttl
                .store(v.ttl as isize, std::sync::atomic::Ordering::SeqCst);
            if !v.is_from_redis {
                rl.background_requests
                    .fetch_add(1, std::sync::atomic::Ordering::SeqCst);
            }
            let mut formatted_queues = vec![];
            for i in v.data.Queues.iter() {
                formatted_queues.push(format_queue_status(i).await);
            }
            Response::builder()
                .status(200)
                .header("Content-Type", "application/json")
                .body(Body::from(
                    serde_json::to_string(&QueueStatusV1 {
                        status: 200,
                        data: formatted_queues,
                    })
                    .unwrap(),
                ))
                .unwrap()
        }
        Err(v) => error_handler(vec![get_base_error(v.status)]),
    }
}

pub async fn format_queue_status(data: &PVPCustomGameConfigQueue) -> QueueStatusV1Data {
    let queues = get_queues().await;
    let mode = queues
        .iter()
        .find(|i| i.id == data.QueueID.split("-").collect::<String>());
    QueueStatusV1Data {
        mode: match mode {
            Some(v) => v.name.clone(),
            None => "Unknown".to_string(),
        },
        mode_id: data.QueueID.to_string(),
        enabled: data.Enabled,
        team_size: data.TeamSize,
        number_of_teams: data.NumTeams,
        party_size: QueueStatusV1PartySize {
            min: data.MinPartySize,
            max: data.MaxPartySize,
            invalid: data.InvalidPartySizes.clone(),
            full_party_bypass: data.AllowFullPartyBypassSkillRestrictions,
        },
        high_skill: QueueStatusV1HighSkill {
            max_party_size: data.MaxPartySizeHighSkill,
            min_tier: data.HighSkillTier,
            max_tier: data.MaxSkillTier,
        },
        ranked: data.IsRanked,
        tournament: data.IsTournament,
        skill_disparity: format_skill_disparity(
            data.PartySkillDisparityCompetitiveTiersCeilings.clone(),
        ),
        required_account_level: data.MinimumAccountLevelRequired,
        game_rules: QueueStatusV1GameRules {
            overtime_win_by_two: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
            allow_lenient_surrender: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
            allow_drop_out: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
            assign_random_agents: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
            skip_pregame: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
            allow_overtime_draw_vote: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
            overtime_win_by_two_capped: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
            premier_mode: data
                .GameRules
                .get("IsOvertimeWinByTwo")
                .unwrap_or(&"false".to_string())
                == "true",
        },
        platforms: data.SupportedPlatformTypes.clone(),
        maps: format_maps(data.MapWeights.clone()).await,
    }
}

fn format_skill_disparity(
    i: std::collections::HashMap<String, i32>,
) -> Vec<QueueStatusV1SkillDisparity> {
    let keys = i.keys().collect::<Vec<&String>>();
    let mut mapping: Vec<QueueStatusV1SkillDisparity> = keys
        .iter()
        .map(|k| {
            let tier = i.get(*k).unwrap();
            QueueStatusV1SkillDisparity {
                tier: k.parse::<i32>().unwrap(),
                name: match VALORANT_TIERS.get(&k.parse::<usize>().unwrap()) {
                    Some(&v) => String::from(v.0),
                    None => String::from("Unknown"),
                },
                max_tier: QueueStatusV1IDNamePair {
                    id: if tier < &0 { 0 } else { *tier as usize },
                    name: if tier < &0 {
                        String::from("Unrated")
                    } else {
                        match VALORANT_TIERS.get(&(*tier as usize)) {
                            Some(&v) => String::from(v.0),
                            None => String::from("Unknown"),
                        }
                    },
                },
            }
        })
        .collect();
    mapping.sort_by(|a, b| a.tier.partial_cmp(&b.tier).unwrap());
    mapping
}

async fn format_maps(weights: Vec<String>) -> Vec<QueueStatusV1Maps> {
    let mut weights_: Vec<QueueStatusV1Maps> = vec![];
    for i in weights {
        let split = i.split(":").collect::<Vec<&str>>();
        let maps = get_maps().await;
        let map = maps
            .iter()
            .find(|i| i.mapUrl.split("/").collect::<Vec<&str>>()[4] == split[0]);
        match map {
            Some(v) => {
                weights_.push(QueueStatusV1Maps {
                    map: QueueStatusV1Map {
                        id: v.uuid.clone(),
                        name: v.displayName.clone(),
                    },
                    enabled: split[1] == "1",
                });
            }
            None => {
                weights_.push(QueueStatusV1Maps {
                    map: QueueStatusV1Map {
                        id: "Unknown".to_string(),
                        name: "Unknown".to_string(),
                    },
                    enabled: false,
                });
            }
        }
    }
    weights_
}
