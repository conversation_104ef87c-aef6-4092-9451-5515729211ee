import {
    errorhandler,
    tiers,
    moment,
    regions,
    calculateElo,
    getMaps,
    old_tiers,
    redis,
    patchData,
    isOld,
    makeRequest,
    getDB,
    getActIDs,
    getCategories,
    convert_old_rank,
    base_url,
    getGamemodes,
    getQueues,
} from '../../server.js';
import {ValorantMMRHistory} from '../../methods/mmr-history.js';

export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/by-puuid/:mode/:region/:puuid', async (req, res) => {
        const puuid = req.params.puuid;
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        const mode = req.params.mode;
        const version = req.params.version;
        const actids = getActIDs();
        const category = getCategories();
        const queues = getQueues();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        const redis_data_names = await redis.get(`names;${puuid}`);
        switch (mode) {
            case 'mmr': {
                switch (version) {
                    case 'v1': {
                        const redis_data = await redis.get(`mmr;v1;${region};${puuid}`);
                        const request = (
                            await Promise.allSettled([
                                JSON.parse(redis_data) ??
                                    makeRequest({
                                        url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${puuid}/competitiveupdates?queue=competitive&startIndex=0&endIndex=20`,
                                        region,
                                        return_error: true,
                                        res,
                                    }),
                                JSON.parse(redis_data_names) ??
                                    makeRequest({
                                        method: 'PUT',
                                        url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                                        res,
                                        body: [puuid],
                                        return_error: true,
                                        region: 'eu',
                                    }),
                            ])
                        ).map(i => i.value);
                        if (res.sent) return;
                        if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
                        if (request[0].response || request[0].code) return errorhandler({status: request[0].response?.status ?? 500, res, errors: [{instance: 'riot'}]});
                        if (!redis_data && request[0].data) redis.set(`mmr;v1;${region};${puuid}`, JSON.stringify({data: request[0].data}), {EX: 300});
                        if (!redis_data_names && request[1].data) redis.set(`names;${puuid}`, JSON.stringify({data: request[1].data}), {EX: 300});
                        if (!request[0].data.Matches.length)
                            return res.code(200).send({
                                status: 200,
                                data: {
                                    currenttier: null,
                                    currenttierpatched: null,
                                    images: null,
                                    ranking_in_tier: null,
                                    mmr_change_to_last_game: null,
                                    elo: null,
                                    name: null,
                                    tag: null,
                                    old: true,
                                },
                            });
                        const comp = request[0].data.Matches.filter(item => item.TierAfterUpdate != 0 && item.SeasonID != '').sort(
                            (item1, item2) => item2.MatchStartTime - item1.MatchStartTime
                        );
                        if (!comp.length)
                            return res.code(200).send({
                                status: 200,
                                data: {
                                    currenttier: null,
                                    currenttierpatched: null,
                                    images: null,
                                    ranking_in_tier: null,
                                    mmr_change_to_last_game: null,
                                    elo: null,
                                    name: null,
                                    tag: null,
                                    old: true,
                                },
                            });
                        let riotid = {
                            name: request[1].data?.[0]?.GameName ?? null,
                            tag: request[1].data?.[0]?.TagLine ?? null,
                        };
                        if (riotid.name != null && riotid.tag != null) getDB('puuids').updateOne({puuid}, {$set: {name: riotid.name, tag: riotid.tag}});
                        return res.code(200).send({
                            status: 200,
                            data: {
                                currenttier: comp[0].TierAfterUpdate,
                                currenttierpatched: isOld({season: comp[0].SeasonID}) ? old_tiers[comp[0].TierAfterUpdate] : tiers[comp[0].TierAfterUpdate],
                                images: {
                                    small: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/smallicon.png`,
                                    large: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/largeicon.png`,
                                    triangle_down: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/ranktriangledownicon.png`,
                                    triangle_up: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[0].TierAfterUpdate}/ranktriangleupicon.png`,
                                },
                                ranking_in_tier: comp[0].RankedRatingAfterUpdate,
                                mmr_change_to_last_game: comp[0].RankedRatingEarned,
                                elo: calculateElo({tier: comp[0].TierAfterUpdate, progress: comp[0].RankedRatingAfterUpdate}),
                                name: riotid.name,
                                tag: riotid.tag,
                                old: isOld({season: comp[0].SeasonID}),
                            },
                        });
                    }
                    case 'v2': {
                        if (req.query.filter && !category.includes(req.query.filter))
                            return errorhandler({status: 400, res, errors: [{instance: 'own', code: 105, details: category}]});
                        if (req.query.season && !category.includes(req.query.season))
                            return errorhandler({status: 400, res, errors: [{instance: 'own', code: 115, details: category}]});
                        const filter = req.query.filter ?? req.query.season ?? null;
                        const redis_data = await redis.get(`mmr;v2;${region};${puuid}`);
                        const request = (
                            await Promise.allSettled([
                                JSON.parse(redis_data) ??
                                    makeRequest({
                                        url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${puuid}`,
                                        region,
                                        return_error: true,
                                        res,
                                    }),
                                JSON.parse(redis_data_names) ??
                                    makeRequest({
                                        url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                                        method: 'PUT',
                                        body: [puuid],
                                        return_error: true,
                                        res,
                                        region: 'eu',
                                    }),
                                makeRequest({
                                    url: `${base_url}/valorant/v1/by-puuid/mmr/${region}/${puuid}`,
                                    proxy: false,
                                    return_error: true,
                                }),
                            ])
                        ).map(i => i.value);
                        if (res.sent) return;
                        if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
                        if (request[0].response || request[0].code) return errorhandler({status: request[0].response?.status ?? 500, res, errors: [{instance: 'riot'}]});
                        if (!redis_data && request[0].data) redis.set(`mmr;v2;${region};${puuid}`, JSON.stringify({data: request[0].data}), {EX: 300});
                        if (!redis_data_names && request[1].data) redis.set(`names;${puuid}`, JSON.stringify({data: request[1].data}), {EX: 300});
                        let riotid = {
                            name: request[1].data?.[0]?.GameName ?? null,
                            tag: request[1].data?.[0]?.TagLine ?? null,
                        };
                        if (riotid.name != null && riotid.tag != null) getDB('puuids').updateOne({puuid}, {$set: {name: riotid.name, tag: riotid.tag}});
                        if (filter) {
                            const act_id = actids[filter.toLowerCase()];
                            const selected_rank_act = [];
                            const keys =
                                request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]?.WinsByTier != null
                                    ? Object.keys(request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].WinsByTier)
                                    : 0;
                            const value =
                                request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]?.WinsByTier != null
                                    ? Object.values(request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].WinsByTier)
                                    : 0;
                            for (let i = 0; keys.length > i; i++) {
                                for (let j = 0; value[i] > j; j++) {
                                    var patched_tier = isOld({id: filter.toLowerCase()}) ? old_tiers[Number(keys[i])] : tiers[Number(keys[i])];
                                    selected_rank_act.push({patched_tier: patched_tier, tier: Number(keys[i])});
                                }
                            }
                            return res.code(200).send({
                                status: 200,
                                data: {
                                    wins: request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                        ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].NumberOfWins
                                        : null,
                                    number_of_games: request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                        ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].NumberOfGames
                                        : null,
                                    final_rank: request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                        ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].CompetitiveTier
                                        : null,
                                    final_rank_patched: isOld({id: filter.toLowerCase()})
                                        ? request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                            ? old_tiers[request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].CompetitiveTier]
                                            : null
                                        : request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id]
                                        ? tiers[request[0].data.QueueSkills.competitive.SeasonalInfoBySeasonID[act_id].CompetitiveTier]
                                        : null,
                                    act_rank_wins: selected_rank_act,
                                    old: isOld({id: filter.toLowerCase()}),
                                },
                            });
                        }
                        const season_data = [];
                        for (let i = 0; category.length > i; i++) {
                            const season_data_single = patchData({act_id: actids[category[i]], raw: request[0].data, old: isOld({id: category[i]})});
                            season_data.push({id: category[i], data: season_data_single});
                        }
                        if (request[2].response) request[2].error = true;
                        let by_season = {};
                        let highest_rank = {old: false, tier: 0, patched_tier: 'Unranked', season: null, converted: 0};
                        for (const act of category) {
                            let act_data = season_data.find(item => item.id == act).data;
                            by_season[act] = act_data;
                            const converted = convert_old_rank({tier: act_data.act_rank_wins?.[0]?.tier, old: act_data.old});
                            if (converted.tier > highest_rank.converted)
                                highest_rank = {
                                    old: act_data.old,
                                    tier: act_data.act_rank_wins[0].tier,
                                    patched_tier: act_data.act_rank_wins[0].patched_tier,
                                    season: act,
                                    converted: converted.tier,
                                };
                        }
                        return res.code(200).send({
                            status: 200,
                            data: {
                                name: riotid.name,
                                tag: riotid.tag,
                                current_data: {
                                    currenttier: !request[2].error ? request[2].data.data.currenttier : null,
                                    currenttierpatched: !request[2].error ? request[2].data.data.currenttierpatched : null,
                                    images: !request[2].error
                                        ? {
                                              small: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                                  request[2].data.data.currenttier ?? 0
                                              }/smallicon.png`,
                                              large: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                                  request[2].data.data.currenttier ?? 0
                                              }/largeicon.png`,
                                              triangle_down: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                                  request[2].data.data.currenttier ?? 0
                                              }/ranktriangledownicon.png`,
                                              triangle_up: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${
                                                  request[2].data.data.currenttier ?? 0
                                              }/ranktriangleupicon.png`,
                                          }
                                        : null,
                                    ranking_in_tier: !request[2].error ? request[2].data.data.ranking_in_tier : null,
                                    mmr_change_to_last_game: !request[2].error ? request[2].data.data.mmr_change_to_last_game : null,
                                    elo: !request[2].error ? request[2].data.data.elo : null,
                                    games_needed_for_rating: request[0].data.QueueSkills.competitive.CurrentSeasonGamesNeededForRating,
                                    old: !request[2].error ? request[2].data.data.old : true,
                                },
                                highest_rank,
                                by_season,
                            },
                        });
                    }
                    default: {
                        return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
                    }
                }
                return;
            }
            case 'mmr-history': {
                /*const maps = getMaps();
                const request = (
                    await Promise.allSettled([
                        makeRequest({
                            url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${puuid}/competitiveupdates?queue=competitive`,
                            region,
                            return_error: true,
                            res,
                        }),
                        makeRequest({
                            url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                            method: 'PUT',
                            body: [puuid],
                            return_error: true,
                            res,
                        }),
                    ])
                ).map(i => i.value);
                if (res.sent) return;
                if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
                if (request[0].response || request[0].code) return errorhandler({status: request[0].response?.status ?? 500, res, errors: [{instance: 'riot'}]});
                if (!request[0].data.Matches.length) return res.code(200).send({status: 200, name: null, tag: null, data: []});
                const comp = request[0].data.Matches.filter(item => item.TierAfterUpdate != 0 && item.SeasonID != '').sort(
                    (item1, item2) => item2.MatchStartTime - item1.MatchStartTime
                );
                if (!comp.length) return res.code(200).send({status: 200, name: null, tag: null, data: []});
                const history = [];
                for (let i = 0; comp.length > i; i++) {
                    const map = maps.find(k => k.mapUrl == comp[i].MapID);
                    history.push({
                        currenttier: comp[i].TierAfterUpdate,
                        currenttierpatched: tiers[comp[i].TierAfterUpdate],
                        images: {
                            small: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/smallicon.png`,
                            large: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/largeicon.png`,
                            triangle_down: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/ranktriangledownicon.png`,
                            triangle_up: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/ranktriangleupicon.png`,
                        },
                        match_id: comp[i].MatchID,
                        map: {
                            name: map.displayName,
                            id: map.uuid,
                        },
                        season_id: comp[i].SeasonID,
                        ranking_in_tier: comp[i].RankedRatingAfterUpdate,
                        mmr_change_to_last_game: comp[i].RankedRatingEarned,
                        elo: calculateElo({tier: comp[i].TierAfterUpdate, progress: comp[i].RankedRatingAfterUpdate}),
                        date: moment(comp[i].MatchStartTime).format('LLLL'),
                        date_raw: Number((comp[i].MatchStartTime / 1000).toFixed(0)),
                    });
                }
                let riotid = {
                    name: request[1].data?.[0]?.GameName ?? null,
                    tag: request[1].data?.[0]?.TagLine ?? null,
                };
                if (riotid.name != null && riotid.tag != null) getDB('puuids').updateOne({puuid}, {$set: {name: riotid.name, tag: riotid.tag}});
                return res.code(200).send({status: 200, name: riotid.name, tag: riotid.tag, data: history});*/
                const mmr_history = new ValorantMMRHistory(puuid, region, res);
                await mmr_history._init();
                return;
            }
            case 'matches': {
                switch (version) {
                    case 'v3': {
                        let matchdata = [];
                        const filter = req.query.filter ?? req.query.mode ?? null;
                        if (filter && !req.query.map) {
                            if (!queues.some(i => i.api == filter.toLowerCase()))
                                return errorhandler({status: 400, res, errors: [{instance: 'own', code: 106, details: queues.map(i => i.api)}]});
                            const historyreq = await makeRequest({
                                url: `https://pd.${region}.a.pvp.net/match-history/v1/history/${puuid}?queue=${queues.find(i => i.api == filter.toLowerCase()).id}`,
                                region,
                                res,
                            });
                            if (res.sent) return;
                            if (!historyreq?.data?.History?.length) return res.code(200).send({status: 200, data: []});
                            let history = historyreq.data.History.sort((item1, item2) => item2.GameStartTime - item1.GameStartTime);
                            history.length = req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5;
                            matchdata = history;
                        }
                        if (!filter && req.query.map) {
                            const maps = getMaps();
                            if (!maps.some(item => item.displayName.toLowerCase() == req.query.map.toLowerCase()))
                                return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 107, details: maps.map(item => item.displayName)}]});
                            const historyreq = await makeRequest({
                                url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${puuid}/competitiveupdates`,
                                region,
                                res,
                            });
                            if (res.sent) return;
                            if (!historyreq?.data?.Matches?.length) return res.code(200).send({status: 200, data: []});
                            let history = historyreq.data.Matches.sort((a, b) => b.MatchStartTime - a.MatchStartTime);
                            history = history.filter(item => item.MapID == maps.find(item => item.displayName.toLowerCase() == req.query.map.toLowerCase()).mapUrl);
                            history.length = req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5;
                            matchdata = history;
                        }
                        if (filter && req.query.map) {
                            const errors = [];
                            const maps = getMaps();
                            if (!queues.some(i => i.api == filter.toLowerCase())) errors.push({instance: 'riot', code: 106, details: queues.map(i => i.api)});
                            if (!maps.some(item => item.displayName.toLowerCase() == req.query.map.toLowerCase()))
                                errors.push({instance: 'riot', code: 107, details: maps.map(i => i.displayName)});
                            if (errors.length) return errorhandler({status: 400, res, errors});
                            const historyreq = await makeRequest({
                                url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${puuid}/competitiveupdates?queue=${
                                    queues.find(i => i.api == filter.toLowerCase()).id
                                }`,
                                region,
                                res,
                            });
                            if (res.sent) return;
                            if (!historyreq?.data?.Matches?.length) return res.code(200).send({status: 200, data: []});
                            let history = historyreq.data.Matches.sort((a, b) => b.MatchStartTime - a.MatchStartTime);
                            history = history.filter(item => item.MapID == maps.find(item => item.displayName.toLowerCase() == req.query.map.toLowerCase()).mapUrl);
                            history.length = req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5;
                            matchdata = history;
                        }
                        if (!matchdata.length) {
                            const historyreq = await makeRequest({
                                url: `https://pd.${region}.a.pvp.net/match-history/v1/history/${puuid}?startIndex=0&endIndex=${
                                    req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5
                                }`,
                                region,
                                res,
                            });
                            if (res.sent) return;
                            if (!historyreq?.data?.History?.length) return res.code(200).send({status: 200, data: []});
                            let history = historyreq.data.History.sort((item1, item2) => item2.GameStartTime - item1.GameStartTime);
                            matchdata = history;
                        }
                        const axiosarray = [];
                        const matches = [];
                        for (let i = 0; matchdata.length > i && matchdata[i]; i++) {
                            const matchesdb = await redis.get(`match;${matchdata[i].MatchID}`);
                            if (matchesdb) matches[i] = JSON.parse(matchesdb);
                            else {
                                axiosarray.push(
                                    makeRequest({
                                        url: `${base_url}/valorantlabs/v1/match/${region}/${matchdata[i].MatchID}`,
                                        return_error: true,
                                        proxy: false,
                                    })
                                );
                            }
                        }
                        const axiosfetch = (await Promise.allSettled(axiosarray)).map(i => i.value);
                        if (axiosfetch.some(item => item.code == 'ECONNABORTED'))
                            return errorhandler({
                                res,
                                status: 408,
                                errors: [{instance: 'riot'}],
                            });
                        for (let i = 0; axiosfetch.length > i; i++) {
                            let args = axiosfetch[i].config.url.split('/');
                            let index = matchdata.findIndex(item => item.MatchID == args[7]);
                            //if (axiosfetch[i].code) console.log(axiosfetch[i]);
                            if (axiosfetch[i].response || axiosfetch[i].code) {
                                matches[index] = {is_available: false};
                            } else {
                                matches[index] = {is_available: true, ...axiosfetch[i].data.data};
                                await redis.set(`match;${args[7]}`, JSON.stringify(axiosfetch[i].data.data), {EX: 7200});
                            }
                        }
                        return res.code(200).send({status: 200, data: matches});
                    }
                    default: {
                        return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
                    }
                }
                return;
            }
        }
    });
}
