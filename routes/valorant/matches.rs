use std::collections::HashMap;
use std::sync::Arc;
use crate::methods::utils::{ get_match_b2, get_matches_b2_only_bulk, save_match_b2, RateLimiting };
use crate::structs::errors::{ error_handler, ErrorCodes };
use crate::structs::helper::{
	fetch_matches_by_puuid,
	fetch_matches_history_by_puuid,
	get_map_by_name,
	get_maps,
	get_queue_by_text,
	get_valorant_account_by_id,
	get_valorant_account_by_name,
	query_string_builder,
};
use crate::structs::parser::{ parse_match_v2, parse_match_v4 };
use crate::structs::paths::{ MatchesV3ByIDPath, MatchesV3Path, MatchesV3Query, MatchesV4ByIDPath, MatchesV4Path, MatchesV4Query };
use crate::structs::responses::{ MatchesV4Data, MatchesV4HistoryResponse };
use crate::{ check_affinity, check_platform, AppState };
use axum::body::Body;
use axum::extract::{ Path, Query, State };
use axum::response::Response;
use axum::Extension;
use futures::future::join_all;
use mongodb::bson::doc;
use serde_json::{ json, Value };
use uuid::Uuid;

pub struct MatchesL {
	pub id: String,
	pub date: i64,
}

pub async fn get_matches_v3_by_name(
	Query(query): Query<MatchesV3Query>,
	Path(path): Path<MatchesV3Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let affinity_check = check_affinity(&path.affinity);
	if !affinity_check {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };

	let client = app_state.client.clone();
	let redis = app_state.redis.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;

	let mut query_map: HashMap<String, String> = HashMap::new();
	query_map.insert("startIndex".to_string(), "0".to_string());
	query_map.insert("endIndex".to_string(), "10".to_string());
	if query.mode.is_some() {
		let mode = get_queue_by_text(&query.mode.clone().unwrap()).await;
		if mode.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMode]);
		}
		query_map.insert("queue".to_string(), mode.unwrap().queueId);
	}
	if query.map.is_some() {
		let all_maps = get_maps().await;
		if !all_maps.iter().any(|i| i.displayName.to_lowercase() == query.map.clone().unwrap().to_lowercase()) {
			return error_handler(vec![ErrorCodes::InvalidMap]);
		}
	}
	let query_string = query_string_builder(query_map);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mut matches: Vec<String> = vec![];
	let mut ttl = 0;

	if query.mode.is_some() && query.mode.clone().unwrap() == "custom" {
		let history = fetch_matches_history_by_puuid(conn.clone(), &account.puuid, &region, &query_string, "pc").await;
		if history.is_err() {
			return error_handler(vec![history.unwrap_err()]);
		}
		let history = history.unwrap();
		ttl = history.ttl;
		let mut matches_ = history.data.history.clone();
		matches_.sort_by(|a, b| b.game_start_time.cmp(&a.game_start_time));
		matches = matches_
			.iter()
			.map(|x| x.match_id.to_string())
			.collect::<Vec<String>>();
	} else {
		let history = fetch_matches_by_puuid(conn.clone(), &account.puuid, &region, &query_string, "pc").await;
		if history.is_err() {
			return error_handler(vec![history.unwrap_err()]);
		}
		let history = history.unwrap();
		ttl = history.ttl;
		let mut matches_ = history.data.matches.clone();
		matches_.sort_by(|a, b| b.match_start_time.cmp(&a.match_start_time));
		if query.map.is_some() {
			let map = get_map_by_name(&query.map.clone().unwrap()).await;
			if map.is_none() {
				return error_handler(vec![ErrorCodes::InvalidMap]);
			}
			let map = map.unwrap();
			matches_.retain(|x| x.map_id == map.mapUrl);
		}
		matches = matches_
			.iter()
			.map(|x| x.match_id.to_string())
			.collect();
	}

	let size = if query.size.is_some() {
		let size = query.size.clone().unwrap().parse::<i32>().unwrap_or(5);
		if size > 10 {
			10
		} else {
			size
		}
	} else {
		5
	};
	if (matches.len() as i32) > size {
		matches = matches[..size as usize].to_vec();
	}

	let mut fetch_matches = vec![];
	for i in matches.clone() {
		fetch_matches.push(get_match_b2(&client, conn.clone(), i, region.clone()));
	}
	let fetch_matches = join_all(fetch_matches).await;
	let mut matches_data: Vec<Value> = vec![];
	let mut http_request_count: usize = 0;
	for x in fetch_matches.iter() {
		if !x.is_from_db {
			http_request_count += 1;
		}
		if x.error {
			matches_data.push(json!({"is_available": false}));
			continue;
		}
		let parse = parse_match_v2(x.data.clone().unwrap(), &region).await;
		matches_data.push(
			json!({
            "is_available": true,
            "metadata": parse.metadata,
            "players": parse.players,
            "observers": parse.observers,
            "coaches": parse.coaches,
            "teams": parse.teams,
            "rounds": parse.rounds,
            "kills": parse.kills,
        })
		);
	}
	rl.background_requests.fetch_add(http_request_count, std::sync::atomic::Ordering::SeqCst);
	rl.redis_cache_ttl.store(ttl as isize, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&json!({"status": 200, "data": matches_data})).unwrap()))
		.unwrap()
}

//get matches by id
pub async fn get_matches_v3_by_id(
	Query(query): Query<MatchesV3Query>,
	Path(path): Path<MatchesV3ByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let affinity_check = check_affinity(&path.affinity);
	if !affinity_check {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;

	let mut query_map: HashMap<String, String> = HashMap::new();
	query_map.insert("startIndex".to_string(), "0".to_string());
	query_map.insert("endIndex".to_string(), "10".to_string());
	if query.mode.is_some() {
		let mode = get_queue_by_text(&query.mode.clone().unwrap()).await;
		if mode.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMode]);
		}
		query_map.insert("queue".to_string(), mode.unwrap().queueId);
	}
	if query.map.is_some() {
		let all_maps = get_maps().await;
		if !all_maps.iter().any(|i| i.displayName.to_lowercase() == query.map.clone().unwrap().to_lowercase()) {
			return error_handler(vec![ErrorCodes::InvalidMap]);
		}
	}
	let query_string = query_string_builder(query_map);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mut matches: Vec<String> = vec![];
	let mut ttl = 0;

	if query.mode.is_some() && query.mode.clone().unwrap() == "custom" {
		let history = fetch_matches_history_by_puuid(conn.clone(), &account.puuid, &region, &query_string, "pc").await;
		if history.is_err() {
			return error_handler(vec![history.unwrap_err()]);
		}
		let history = history.unwrap();
		ttl = history.ttl;
		let mut matches_ = history.data.history.clone();
		matches_.sort_by(|a, b| b.game_start_time.cmp(&a.game_start_time));
		matches = matches_
			.iter()
			.map(|x| x.match_id.to_string())
			.collect::<Vec<String>>();
	} else {
		let history = fetch_matches_by_puuid(conn.clone(), &account.puuid, &region, &query_string, "pc").await;
		if history.is_err() {
			return error_handler(vec![history.unwrap_err()]);
		}
		let history = history.unwrap();
		ttl = history.ttl;
		let mut matches_ = history.data.matches.clone();
		matches_.sort_by(|a, b| b.match_start_time.cmp(&a.match_start_time));
		if query.map.is_some() {
			let map = get_map_by_name(&query.map.clone().unwrap()).await;
			if map.is_none() {
				return error_handler(vec![ErrorCodes::InvalidMap]);
			}
			let map = map.unwrap();
			matches_.retain(|x| x.map_id == map.mapUrl);
		}
		matches = matches_
			.iter()
			.map(|x| x.match_id.to_string())
			.collect();
	}

	let size = if query.size.is_some() {
		let size = query.size.clone().unwrap().parse::<i32>().unwrap_or(5);
		if size > 10 {
			10
		} else {
			size
		}
	} else {
		5
	};
	if (matches.len() as i32) > size {
		matches = matches[..size as usize].to_vec();
	}
	let mut fetch_matches: Vec<_> = vec![];
	for x in matches.iter() {
		fetch_matches.push(get_match_b2(&client, conn.clone(), x.clone(), region.clone()));
	}
	let fetch_matches = join_all(fetch_matches).await;
	let mut matches_data: Vec<Value> = vec![];
	let mut http_request_count: usize = 0;
	for x in fetch_matches.iter() {
		if !x.is_from_db {
			http_request_count += 1;
		}
		if x.error {
			matches_data.push(json!({"is_available": false}));
			continue;
		}
		let parse = parse_match_v2(x.data.clone().unwrap(), &region).await;
		matches_data.push(
			json!({
            "is_available": true,
            "metadata": parse.metadata,
            "players": parse.players,
            "observers": parse.observers,
            "coaches": parse.coaches,
            "teams": parse.teams,
            "rounds": parse.rounds,
            "kills": parse.kills,
        })
		);
		drop(parse);
	}
	drop(fetch_matches);
	rl.background_requests.fetch_add(http_request_count, std::sync::atomic::Ordering::SeqCst);
	rl.redis_cache_ttl.store(ttl as isize, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&json!({"status": 200, "data": matches_data})).unwrap()))
		.unwrap()
}

pub async fn get_matches_v4_by_name(
	Query(query): Query<MatchesV4Query>,
	Path(path): Path<MatchesV4Path>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let affinity_check = check_affinity(&path.affinity);
	if !affinity_check {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let platform = check_platform(&path.platform);
	if !platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let platform = path.platform.to_lowercase();
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };

	let client = app_state.client.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_name(&client, app_state.redis.clone(), &path.name, &path.tag, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;

	let mut query_map: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {
        "meta.p": &platform,
        "players.id": &account.puuid,
    };
	let mut db_skip: u64 = 0;

	let size = if query.size.is_some() {
		let size = query.size.clone().unwrap().parse::<i32>().unwrap_or(5);
		if size > 10 {
			10
		} else {
			size
		}
	} else {
		5
	};
	if query.start.is_some() {
		let api_start = query.start.clone().unwrap().parse::<i32>();
		if let Ok(start) = api_start {
			if start < 0 {
				return error_handler(vec![ErrorCodes::InvalidStartIndex]);
			}
			query_map.insert("startIndex".to_string(), start.to_string());
			query_map.insert("endIndex".to_string(), (start + size).to_string());
			db_skip = start as u64;
		} else {
			return error_handler(vec![ErrorCodes::InvalidStartIndex]);
		}
	} else {
		query_map.insert("startIndex".to_string(), "0".to_string());
		query_map.insert("endIndex".to_string(), "10".to_string());
		db_skip = 0;
	}

	if query.mode.is_some() {
		let mode = get_queue_by_text(&query.mode.clone().unwrap()).await;
		if mode.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMode]);
		}
		let q_id = mode.clone().unwrap().queueId;
		db_query.insert("meta.q", Some(q_id.clone()));
		query_map.insert("queue".to_string(), q_id);
	}
	if query.map.is_some() {
		let all_maps = get_maps().await;
		if !all_maps.iter().any(|i| i.displayName.to_lowercase() == query.map.clone().unwrap().to_lowercase()) {
			return error_handler(vec![ErrorCodes::InvalidMap]);
		}
		db_query.insert("meta.map_id", get_map_by_name(&query.map.clone().unwrap()).await.unwrap().mapUrl);
	}
	let query_string = query_string_builder(query_map);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mut matches: Vec<String> = vec![];
	let mut ttl = 0;

	let history = fetch_matches_history_by_puuid(conn.clone(), &account.puuid, &region, &query_string, &platform).await;
	if history.is_err() {
		return error_handler(vec![history.unwrap_err()]);
	}
	let history = history.unwrap();
	ttl = history.ttl;
	let mut matches_ = history.data.history.clone();
	matches_.sort_by(|a, b| b.game_start_time.cmp(&a.game_start_time));
	matches = matches_
		.iter()
		.map(|x| x.match_id.to_string())
		.collect::<Vec<String>>();

	if (matches.len() as i32) > size {
		matches = matches[..size as usize].to_vec();
	}

	let mut save_matches: Vec<_> = vec![];
	for x in matches.iter() {
		save_matches.push(save_match_b2(&client, conn.clone(), x, &region, None));
	}
	let save_matches = join_all(save_matches).await;

	let mut http_request_count: usize = 0;
	for x in save_matches.iter() {
		if !x.is_from_db {
			http_request_count += 1;
		}
		if x.error {
			continue;
		}
	}

	let bulk = get_matches_b2_only_bulk(&client, db_query.clone(), size as i64, db_skip, doc! { "meta.s_ms": -1 }, conn.clone(), None).await;
	if bulk.error {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let mut matches_data: Vec<MatchesV4Data> = vec![];
	for x in bulk.data.iter() {
		let parse = parse_match_v4(x.clone(), &region).await;
		matches_data.push(parse);
	}

	rl.redis_cache_ttl.store(ttl as isize, std::sync::atomic::Ordering::SeqCst);
	rl.background_requests.fetch_add(http_request_count, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(
			Body::from(
				serde_json
					::to_string(
						&(MatchesV4HistoryResponse {
							status: 200,
							data: matches_data,
						})
					)
					.unwrap()
			)
		)
		.unwrap()
}

pub async fn get_matches_v4_by_id(
	Query(query): Query<MatchesV4Query>,
	Path(path): Path<MatchesV4ByIDPath>,
	State(app_state): State<Arc<AppState>>,
	Extension(extension): Extension<Arc<RateLimiting>>
) -> Response {
	let affinity_check = check_affinity(&path.affinity);
	if !affinity_check {
		return error_handler(vec![ErrorCodes::InvalidRegion]);
	}
	let platform = check_platform(&path.platform);
	if !platform {
		return error_handler(vec![ErrorCodes::InvalidPlatform]);
	}
	let platform = path.platform.to_lowercase();
	let region = if ["br", "latam"].iter().any(|x| x == &path.affinity.to_lowercase()) { String::from("na") } else { path.affinity.to_lowercase() };
	let validate_uuid = Uuid::parse_str(&path.puuid);
	if validate_uuid.is_err() {
		return error_handler(vec![ErrorCodes::InvalidUUID]);
	}

	let client = app_state.client.clone();
	//let redis = app_state.pool.clone();
	let rl = extension.clone();

	let account = get_valorant_account_by_id(&client, app_state.redis.clone(), &path.puuid, false, false).await;
	if account.is_err() {
		return error_handler(vec![account.unwrap_err()]);
	}
	let account_wr = account.unwrap();
	rl.background_requests.fetch_add(account_wr.background_requests, std::sync::atomic::Ordering::SeqCst);
	let account = account_wr.data;

	let mut query_map: HashMap<String, String> = HashMap::new();
	let mut db_query = doc! {
        "meta.p": &platform,
        "players.id": &account.puuid,
    };
	let mut db_skip: u64 = 0;

	let size = if query.size.is_some() {
		let size = query.size.clone().unwrap().parse::<i32>().unwrap_or(5);
		if size > 10 {
			10
		} else {
			size
		}
	} else {
		5
	};
	if query.start.is_some() {
		let api_start = query.start.clone().unwrap().parse::<i32>();
		if let Ok(start) = api_start {
			if start < 0 {
				return error_handler(vec![ErrorCodes::InvalidStartIndex]);
			}
			query_map.insert("startIndex".to_string(), start.to_string());
			query_map.insert("endIndex".to_string(), (start + size).to_string());
			db_skip = start as u64;
		} else {
			return error_handler(vec![ErrorCodes::InvalidStartIndex]);
		}
	} else {
		query_map.insert("startIndex".to_string(), "0".to_string());
		query_map.insert("endIndex".to_string(), "10".to_string());
		db_skip = 0;
	}

	if query.mode.is_some() {
		let mode = get_queue_by_text(&query.mode.clone().unwrap()).await;
		if mode.is_none() {
			return error_handler(vec![ErrorCodes::InvalidMode]);
		}
		let q_id = mode.clone().unwrap().queueId;
		db_query.insert("meta.q", Some(q_id.clone()));
		query_map.insert("queue".to_string(), q_id);
	}
	if query.map.is_some() {
		let all_maps = get_maps().await;
		if !all_maps.iter().any(|i| i.displayName.to_lowercase() == query.map.clone().unwrap().to_lowercase()) {
			return error_handler(vec![ErrorCodes::InvalidMap]);
		}
		db_query.insert("meta.map_id", get_map_by_name(&query.map.clone().unwrap()).await.unwrap().mapUrl);
	}
	let query_string = query_string_builder(query_map);

	//let conn = redis.get().await.expect("Failed to get Redis connection from pool");
	let conn = app_state.redis.clone();
	let mut matches: Vec<String> = vec![];
	let mut ttl = 0;

	let history = fetch_matches_history_by_puuid(conn.clone(), &account.puuid, &region, &query_string, &platform).await;
	if history.is_err() {
		return error_handler(vec![history.unwrap_err()]);
	}
	let history = history.unwrap();
	ttl = history.ttl;
	let mut matches_ = history.data.history.clone();
	matches_.sort_by(|a, b| b.game_start_time.cmp(&a.game_start_time));
	matches = matches_
		.iter()
		.map(|x| x.match_id.to_string())
		.collect::<Vec<String>>();

	if (matches.len() as i32) > size {
		matches = matches[..size as usize].to_vec();
	}

	let mut save_matches: Vec<_> = vec![];
	for x in matches.iter() {
		save_matches.push(save_match_b2(&client, conn.clone(), x, &region, None));
	}
	let save_matches = join_all(save_matches).await;

	let mut http_request_count: usize = 0;
	for x in save_matches.iter() {
		if !x.is_from_db {
			http_request_count += 1;
		}
		if x.error {
			continue;
		}
	}

	let bulk = get_matches_b2_only_bulk(&client, db_query.clone(), size as i64, db_skip, doc! { "meta.s_ms": -1 }, conn.clone(), None).await;
	if bulk.error {
		return error_handler(vec![ErrorCodes::DatabaseError]);
	}
	let mut matches_data: Vec<MatchesV4Data> = vec![];
	for x in bulk.data.iter() {
		let parse = parse_match_v4(x.clone(), &region).await;
		matches_data.push(parse);
	}

	rl.redis_cache_ttl.store(ttl as isize, std::sync::atomic::Ordering::SeqCst);
	rl.background_requests.fetch_add(http_request_count, std::sync::atomic::Ordering::SeqCst);
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(
			Body::from(
				serde_json
					::to_string(
						&(MatchesV4HistoryResponse {
							status: 200,
							data: matches_data,
						})
					)
					.unwrap()
			)
		)
		.unwrap()
}
