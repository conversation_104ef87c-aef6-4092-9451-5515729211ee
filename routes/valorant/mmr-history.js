import {ValorantMMRHistory} from '../../methods/mmr-history.js';
import {regions, getPuuid, tiers, calculateElo, moment, errorhandler, makeRequest, getDB, getMaps, base_url, affinities} from '../../server.js';
export default async function (fastify, opts, done) {
    /*fastify.get('/valorant/v1/mmr-history/:region/:name/:tag', async (req, res) => {
        const name = req.params.name.replaceAll('_', '').replaceAll('+', ' ').trim();
        const tag = req.params.tag.replaceAll('_', '').replaceAll('+', ' ').trim();
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: affinities}]});
        const maps = getMaps();
        let db = await getPuuid({name, tag});
        if (!db) {
            const puuid = await makeRequest({
                url: `${base_url}/valorant/v1/account/${encodeURI(name)}/${encodeURI(tag)}`,
                proxy: false,
                return_error: true,
                overwrite_headers: req.headers,
                timeout: 7500,
                res,
            });
            if (puuid.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (puuid.response) return errorhandler({status: puuid.response.status, res, errors: [{instance: 'riot'}]});
            db = puuid.data.data;
        }
        const request = (
            await Promise.allSettled([
                makeRequest({
                    url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${db.puuid}/competitiveupdates?queue=competitive&startIndex=0&endIndex=20`,
                    res,
                    region,
                    return_error: true,
                }),
                makeRequest({
                    url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                    method: 'PUT',
                    body: [db.puuid],
                    return_error: true,
                    res,
                }),
            ])
        ).map(i => i.value);
        if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
        if (request[0].response) return errorhandler({status: request[0].response.status, res, errors: [{instance: 'riot'}]});
        if (!request[0].data?.Matches?.length) return res.code(200).send({status: 200, name: null, tag: null, data: []});
        const comp = request[0].data.Matches.filter(item => item.TierAfterUpdate != 0 && item.SeasonID != '').sort(
            (item1, item2) => item2.MatchStartTime - item1.MatchStartTime
        );
        if (!comp.length) return res.code(200).send({status: 200, name: null, tag: null, data: []});
        const history = [];
        for (let i = 0; comp.length > i; i++) {
            const map = maps.find(k => k.mapUrl == comp[i].MapID);
            history.push({
                currenttier: comp[i].TierAfterUpdate,
                currenttierpatched: tiers[comp[i].TierAfterUpdate],
                images: {
                    small: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/smallicon.png`,
                    large: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/largeicon.png`,
                    triangle_down: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/ranktriangledownicon.png`,
                    triangle_up: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${comp[i].TierAfterUpdate}/ranktriangleupicon.png`,
                },
                match_id: comp[i].MatchID,
                map: {
                    name: map.displayName,
                    id: map.uuid,
                },
                season_id: comp[i].SeasonID,
                ranking_in_tier: comp[i].RankedRatingAfterUpdate,
                mmr_change_to_last_game: comp[i].RankedRatingEarned,
                elo: calculateElo({tier: comp[i].TierAfterUpdate, progress: comp[i].RankedRatingAfterUpdate}),
                date: moment(comp[i].MatchStartTime).format('LLLL'),
                date_raw: Number((comp[i].MatchStartTime / 1000).toFixed(0)),
            });
        }
        let riotid = {
            name: request[1].data?.[0]?.GameName ?? null,
            tag: request[1].data?.[0]?.TagLine ?? null,
        };
        if (riotid.name != null && riotid.tag != null) getDB('puuids').updateOne({puuid: db.puuid}, {$set: {name: riotid.name, tag: riotid.tag}});
        return res.code(200).send({status: 200, name: riotid.name, tag: riotid.tag, data: history});
    });*/
    fastify.get('/valorant/v1/mmr-history/:region/:name/:tag', async (req, res) => {
        const name = req.params.name.replaceAll('+', ' ').trim();
        const tag = req.params.tag.replaceAll('+', ' ').trim();
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: affinities}]});
        const mmr_history = new ValorantMMRHistory({name, tag}, region, res);
        await mmr_history._init();
        return;
    });
}
