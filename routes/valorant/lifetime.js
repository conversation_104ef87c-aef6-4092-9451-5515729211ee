import {errorhandler, regions, getPuuid, makeRequest, base_url} from '../../server.js';
import {lifetime_matches, lifetime_mmr_history} from '../../methods/lifetime.js';

export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/lifetime/:mode/:region/:name/:tag', async (req, res) => {
        const name = req.params.name.replaceAll('_', '').replaceAll('+', ' ').trim();
        const tag = req.params.tag.replaceAll('_', '').replaceAll('+', ' ').trim();
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        let db = await getPuuid({name, tag});
        if (!db) {
            const puuid = await makeRequest({
                url: `${base_url}/valorant/v1/account/${encodeURI(name)}/${encodeURI(tag)}`,
                proxy: false,
                return_error: true,
                overwrite_headers: req.headers,
                timeout: 7500,
                res,
            });
            if (puuid.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (puuid.response) return errorhandler({status: puuid.response.status, res, errors: [{instance: 'riot'}]});
            db = puuid.data.data;
        }
        switch (req.params.mode) {
            case 'matches': {
                await lifetime_matches({puuid: db.puuid, res, queries: req.query, region});
                return;
            }
            case 'mmr-history': {
                await lifetime_mmr_history({puuid: db.puuid, res, queries: req.query, region});
            }
            default: {
                return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
            }
        }
    });
    fastify.get('/valorant/v1/by-puuid/lifetime/:mode/:region/:puuid', async (req, res) => {
        const puuid = req.params.puuid;
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        const mode = req.params.mode;
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        switch (req.params.mode) {
            case 'matches': {
                await lifetime_matches({puuid, res, queries: req.query, region});
                return;
            }
            case 'mmr-history': {
                await lifetime_mmr_history({puuid, res, queries: req.query, region});
            }
            default: {
                return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
            }
        }
    });
}
