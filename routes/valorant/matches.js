import {getMaps, errorhandler, regions, getPuuid, makeRequest, redis, base_url, getQueues} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/matches/:region/:name/:tag', async (req, res) => {
        const name = req.params.name.replaceAll('_', '').replaceAll('+', ' ').trim();
        const tag = req.params.tag.replaceAll('_', '').replaceAll('+', ' ').trim();
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        const queues = getQueues();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        if (req.params.version != 'v3') return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
        let db = await getPuuid({name, tag});
        if (!db) {
            const puuid = await makeRequest({
                url: `${base_url}/valorant/v1/account/${encodeURI(name)}/${encodeURI(tag)}`,
                proxy: false,
                return_error: true,
                overwrite_headers: req.headers,
                timeout: 7500,
                res,
            });
            if (puuid.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (puuid.response) return errorhandler({status: puuid.response.status, res, errors: [{instance: 'riot'}]});
            db = puuid.data.data;
        }
        let matchdata = [];
        const filter = req.query.filter ?? req.query.mode ?? null;
        if (filter && !req.query.map) {
            if (!queues.some(i => i.api == filter.toLowerCase()))
                return errorhandler({status: 400, res, errors: [{instance: 'own', code: 106, details: queues.map(i => i.api)}]});
            const historyreq = await makeRequest({
                url: `https://pd.${region}.a.pvp.net/match-history/v1/history/${db.puuid}?queue=${queues.find(i => i.api == filter.toLowerCase()).id}`,
                region,
                res,
            });
            if (res.sent) return;
            if (!historyreq?.data?.History?.length) return res.code(200).send({status: 200, data: []});
            let history = historyreq.data.History.sort((item1, item2) => item2.GameStartTime - item1.GameStartTime);
            history.length = req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5;
            matchdata = history;
        }
        if (!filter && req.query.map) {
            const maps = getMaps();
            if (!maps.some(item => item.displayName.toLowerCase() == req.query.map.toLowerCase()))
                return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 107, details: maps.map(item => item.displayName)}]});
            const historyreq = await makeRequest({
                url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${db.puuid}/competitiveupdates`,
                region,
                res,
            });
            if (res.sent) return;
            if (!historyreq?.data?.Matches?.length) return res.code(200).send({status: 200, data: []});
            let history = historyreq.data.Matches.sort((a, b) => b.MatchStartTime - a.MatchStartTime);
            history = history.filter(item => item.MapID == maps.find(item => item.displayName.toLowerCase() == req.query.map.toLowerCase()).mapUrl);
            history.length = req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5;
            matchdata = history;
        }
        if (filter && req.query.map) {
            const errors = [];
            const maps = getMaps();
            if (!queues.some(i => i.api == filter.toLowerCase())) errors.push({instance: 'riot', code: 106, details: queues.map(item => item.api)});
            if (!maps.some(i => i.displayName.toLowerCase() == req.query.map.toLowerCase()))
                errors.push({instance: 'riot', code: 107, details: maps.map(i => i.displayName)});
            if (errors.length) return errorhandler({status: 400, res, errors});
            const historyreq = await makeRequest({
                url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${db.puuid}/competitiveupdates?queue=${queues.find(item => item.api == filter.toLowerCase()).id}`,
                region,
                res,
            });
            if (res.sent) return;
            if (!historyreq?.data?.Matches?.length) return res.code(200).send({status: 200, data: []});
            let history = historyreq.data.Matches.sort((a, b) => b.MatchStartTime - a.MatchStartTime);
            history = history.filter(item => item.MapID == maps.find(item => item.displayName.toLowerCase() == req.query.map.toLowerCase()).mapUrl);
            history.length = req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5;
            matchdata = history;
        }
        if (!matchdata.length) {
            const historyreq = await makeRequest({
                url: `https://pd.${region}.a.pvp.net/match-history/v1/history/${db.puuid}?startIndex=0&endIndex=${
                    req.query.size ? (req.query.size <= 10 ? req.query.size : 10) : 5
                }`,
                region,
                res,
            });
            if (res.sent) return;
            if (!historyreq?.data?.History?.length) return res.code(200).send({status: 200, data: []});
            let history = historyreq.data.History.sort((item1, item2) => item2.GameStartTime - item1.GameStartTime);
            matchdata = history;
        }
        const axiosarray = [];
        const matches = [];
        for (let i = 0; matchdata.length > i && matchdata[i]; i++) {
            const matchesdb = await redis.get(`match;${matchdata[i].MatchID}`);
            if (matchesdb) matches[i] = JSON.parse(matchesdb);
            else {
                axiosarray.push(
                    makeRequest({
                        url: `${base_url}/valorantlabs/v1/match/${region}/${matchdata[i].MatchID}`,
                        return_error: true,
                        proxy: false,
                    })
                );
            }
        }
        const axiosfetch = (await Promise.allSettled(axiosarray)).map(i => i.value);
        if (axiosfetch.some(item => item.code == 'ECONNABORTED'))
            return errorhandler({
                res,
                status: 408,
                errors: [{instance: 'riot'}],
            });
        for (let i = 0; axiosfetch.length > i; i++) {
            let args = axiosfetch[i].config.url.split('/');
            let index = matchdata.findIndex(item => item.MatchID == args[7]);
            if (axiosfetch[i].response || axiosfetch[i].code) {
                matches[index] = {is_available: false};
            } else {
                matches[index] = {is_available: true, ...axiosfetch[i].data.data};
                await redis.set(`match;${args[7]}`, JSON.stringify(axiosfetch[i].data.data), {EX: 7200});
            }
        }
        return res.code(200).send({status: 200, data: matches});
    });
}
