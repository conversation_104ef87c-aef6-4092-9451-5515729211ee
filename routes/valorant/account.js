import {
    errorhandler,
    moment,
    endpointspecific,
    basedata,
    getDB,
    getPuuid,
    check404,
    makeRequest,
    regions,
    fs,
    zlib,
    existsMatchFile,
    getMatchFile,
    saveMatchFile,
} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/account/:name/:tag', async (req, res) => {
        if (!['v1'].some(item => item == req.params.version)) return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
        const name = req.params.name.trim();
        const tag = req.params.tag.trim();
        if (!encodeURI(tag).length)
            return errorhandler({
                res,
                status: 400,
                errors: [{instance: 'own', code: 16, details: 'The given tag seems to be empty, make sure to also request a tag and not just a name'}],
            });
        async function normalfetch() {
            /*const check404 = await makeRequest({
                url: `https://api.tracker.gg/api/v2/valorant/standard/profile/riot/${encodeURI(name)}%23${encodeURI(tag)}/`,
                return_error: true,
                httpAgent: true,
                proxy: true,
                res,
            });
            if (res.sent) return;
            if (check404.response && check404.response.status == 404) return errorhandler({status: 404, res, errors: [{instance: 'riot'}]});*/
            let api_key = req.headers['authorization'] ?? req.query.api_key;
            if (!api_key && !res.admin)
                await endpointspecific({token: req.headers['cf-connecting-ip'], instance: 'valorant', endpoint: 'account', limit: 2, res});
            if (res.sent) return;
            const ratelimit = await getDB('rate-limit-key', 'VALORANT-LABS').findOne({});
            if (!res.admin && !api_key) {
                await getDB('rate-limit-key', 'VALORANT-LABS').updateOne({key: 'key'}, {$inc: {current: 1}});
            }
            if (ratelimit.current >= 100 && !res.admin) return errorhandler({status: 429, res, errors: [{instance: 'riot', global: true}]});
            const puuid = await makeRequest({
                url: `https://americas.api.riotgames.com/riot/account/v1/accounts/by-riot-id/${encodeURI(name)}/${encodeURI(tag)}`,
                return_error: true,
                proxy: false,
                timeout: 5000,
                overwrite_headers: {'X-Riot-Token': basedata.riottoken},
            });
            if (puuid.response && puuid.response.status == 404)
                await getDB('notfound').updateOne(
                    {name, tag},
                    {$setOnInsert: {name, tag, expireAt: new Date()}},
                    {upsert: true, collation: {locale: 'en', strength: 2, alternate: 'shifted'}}
                );
            if (puuid.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (puuid.response) return errorhandler({status: puuid.response.status, res, errors: [{instance: 'riot'}]});
            const regionfetch = await makeRequest({
                url: `https://americas.api.riotgames.com/riot/account/v1/active-shards/by-game/val/by-puuid/${puuid.data.puuid}`,
                return_error: true,
                proxy: false,
                timeout: 5000,
                overwrite_headers: {'X-Riot-Token': basedata.riottoken},
            });
            if (regionfetch.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (regionfetch.response && regionfetch.response.status == 404) return errorhandler({status: 404, res, errors: [{instance: 'riot', code: 101}]});
            if (regionfetch.response) return errorhandler({status: regionfetch.response.status, res, errors: [{instance: 'riot'}]});
            const matchlist = await makeRequest({
                url: `https://${regionfetch.data.activeShard}.api.riotgames.com/val/match/v1/matchlists/by-puuid/${puuid.data.puuid}`,
                return_error: true,
                proxy: false,
                timeout: 5000,
                overwrite_headers: {'X-Riot-Token': basedata.riottoken},
            });
            if (matchlist.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (matchlist.response) return errorhandler({status: matchlist.response.status, res, errors: [{instance: 'riot'}]});
            if (!matchlist.data.history.length) return errorhandler({status: 500, res, errors: [{instance: 'riot', code: 102}]});
            const matchdata = await makeRequest({
                url: `https://${regionfetch.data.activeShard}.api.riotgames.com/val/match/v1/matches/${matchlist.data.history[0].matchId}`,
                return_error: true,
                proxy: false,
                timeout: 5000,
                overwrite_headers: {'X-Riot-Token': basedata.riottoken},
            });
            if (matchdata.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (matchdata.response) return errorhandler({status: matchdata.response.status, res, errors: [{instance: 'riot'}]});
            const oplayer = matchdata.data.players.find(item => item.puuid == puuid.data.puuid);
            if (regionfetch.data.activeShard == 'latam' || regionfetch.data.activeShard == 'br') regionfetch.data.activeShard = 'na';
            const matchfetch = existsMatchFile(matchlist.data.history[0].matchId)
                ? {data: getMatchFile(matchlist.data.history[0].matchId)}
                : await makeRequest({
                      url: `https://pd.${regionfetch.data.activeShard}.a.pvp.net/match-details/v1/matches/${matchlist.data.history[0].matchId}`,
                      res,
                      timeout: 5000,
                      region: regionfetch.data.activeShard,
                  });
            if (res.sent) return;
            if (matchfetch.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (matchfetch.config && !matchfetch.response && matchfetch.data) await saveMatchFile(matchfetch.data, matchlist.data.history[0].matchId);
            if (!matchfetch.data) console.log(matchfetch);
            const player = matchfetch.data.players.find(item => oplayer.gameName == item.gameName && oplayer.tagLine == item.tagLine);
            if (player == null) return errorhandler({status: 500, res, errors: [{instance: 'riot', code: 103}]});
            await getDB('puuids').updateOne(
                {puuid: player.subject},
                {
                    $set: {
                        region: regionfetch.data.activeShard,
                        account_level: player != null ? player.accountLevel : null,
                        name: puuid.data.gameName ? puuid.data.gameName : player.gameName,
                        tag: puuid.data.tagLine ? puuid.data.tagLine : player.tagLine,
                        card: player.playerCard,
                        last_update: moment().unix(),
                        last_match: matchfetch.data.matchInfo.gameStartMillis,
                    },
                },
                {upsert: true}
            );
            return res.code(200).send({
                status: 200,
                data: {
                    puuid: player.subject,
                    region: regionfetch.data.activeShard,
                    account_level: player != null ? player.accountLevel : null,
                    name: puuid.data.gameName ? puuid.data.gameName : player.gameName,
                    tag: puuid.data.tagLine ? puuid.data.tagLine : player.tagLine,
                    card: {
                        small: `https://media.valorant-api.com/playercards/${player.playerCard}/smallart.png`,
                        large: `https://media.valorant-api.com/playercards/${player.playerCard}/largeart.png`,
                        wide: `https://media.valorant-api.com/playercards/${player.playerCard}/wideart.png`,
                        id: player.playerCard,
                    },
                    last_update: 'Now',
                    last_update_raw: moment().unix(),
                },
            });
        }
        async function getRegion() {
            const axiosf = [];
            for (const region of regions) {
                axiosf.push(makeRequest({url: `https://pd.${region}.a.pvp.net/match-history/v1/history/${db.puuid}`, return_error: true, res, region}));
            }
            let results = await Promise.all(axiosf);
            results = results.filter(i => i.response).length == 4 ? results[0] : results.find(i => i.status == 200);
            if (results.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (results.response) return errorhandler({status: requests.response.status, res, errors: [{instance: 'riot'}]});
            if (!results.data.History.length) {
                await getDB('puuids').updateOne(
                    {puuid: db.puuid},
                    {
                        $set: {
                            region: results.data.region,
                        },
                    },
                    {upsert: true}
                );
                return res.code(200).send({
                    status: 200,
                    data: {
                        puuid: db.puuid,
                        region: results.data.region,
                        account_level: db.account_level,
                        name: db.name,
                        tag: db.tag,
                        card: {
                            small: db.card ? `https://media.valorant-api.com/playercards/${db.card}/smallart.png` : null,
                            large: db.card ? `https://media.valorant-api.com/playercards/${db.card}/largeart.png` : null,
                            wide: db.card ? `https://media.valorant-api.com/playercards/${db.card}/wideart.png` : null,
                            id: db.card ?? null,
                        },
                        last_update: 'Now',
                        last_update_raw: moment().unix(),
                    },
                });
            }
            const matchdata = existsMatchFile(results.data.History[0].MatchID)
                ? {data: getMatchFile(results.data.History[0].MatchID)}
                : await makeRequest({
                      url: `https://pd.${results.data.region}.a.pvp.net/match-details/v1/matches/${results.data.History[0].MatchID}`,
                      res,
                      region: results.data.region,
                  });
            if (res.sent) return;
            if (matchdata.config && !matchdata.response && matchdata.data) await saveMatchFile(matchdata.data, results.data.History[0].MatchID);
            const player = matchdata.data.players.find(item => item.subject == db.puuid);
            if (player == null) return errorhandler({status: 500, res, errors: [{instance: 'riot', code: 103}]});
            await getDB('puuids').updateOne(
                {puuid: db.puuid},
                {
                    $set: {
                        region: results.data.region,
                        account_level: player ? player.accountLevel : null,
                        name: player.gameName,
                        tag: player.tagLine,
                        card: player.playerCard,
                        last_update: moment().unix(),
                        last_match: matchdata.data.matchInfo.gameStartMillis,
                    },
                },
                {upsert: true}
            );
            return res.code(200).send({
                status: 200,
                data: {
                    puuid: db.puuid,
                    region: results.data.region,
                    account_level: player ? player.accountLevel : null,
                    name: player.gameName,
                    tag: player.tagLine,
                    card: {
                        small: `https://media.valorant-api.com/playercards/${player.playerCard}/smallart.png`,
                        large: `https://media.valorant-api.com/playercards/${player.playerCard}/largeart.png`,
                        wide: `https://media.valorant-api.com/playercards/${player.playerCard}/wideart.png`,
                        id: player.playerCard,
                    },
                    last_update: moment(moment.unix(db.last_update).toISOString()).from(new Date().toISOString()),
                    last_update_raw: db.last_update,
                },
            });
        }
        async function dbSend() {
            if (!db.region) return getRegion();
            const dbcount = await getDB('puuids').countDocuments({name, tag}, {collation: {locale: 'en', strength: 2, alternate: 'shifted'}});
            if (dbcount > 1) {
                getDB('puuids').deleteMany({name, tag}, {collation: {locale: 'en', strength: 2, alternate: 'shifted'}});
                return normalfetch();
            }
            if ((req.query.force && req.query.force == 'true') || !db.last_update || moment().unix() - db.last_update > 3600) {
                const matchlist = await makeRequest({
                    url: `https://pd.${db.region}.a.pvp.net/match-history/v1/history/${db.puuid}`,
                    res,
                    region: db.region,
                });
                if (res.sent) return;
                if (!matchlist.data?.History?.length)
                    return res.status(200).send({status: 200, data: {puuid: db.puuid, region: db.region, account_level: db.account_level, name: db.name, tag: db.tag}});
                const matchfetch = existsMatchFile(matchlist.data.History[0].MatchID)
                    ? {data: getMatchFile(matchlist.data.History[0].MatchID)}
                    : await makeRequest({
                          url: `https://pd.${db.region}.a.pvp.net/match-details/v1/matches/${matchlist.data.History[0].MatchID}`,
                          res,
                          region: db.region,
                      });
                if (res.sent) return;
                if (matchfetch.response)
                    return res.status(200).send({status: 200, data: {puuid: db.puuid, region: db.region, account_level: db.account_level, name: db.name, tag: db.tag}});
                if (matchfetch.config && !matchfetch.response && matchfetch.data) await saveMatchFile(matchfetch.data, matchlist.data.History[0].MatchID);
                if (!matchfetch.data) console.log(matchfetch);

                const player = matchfetch.data.players.find(item => item.subject == db.puuid);
                await getDB('puuids').updateOne(
                    {puuid: db.puuid},
                    {
                        $set: {
                            name: player.gameName,
                            tag: player.tagLine,
                            account_level: player != null ? player.accountLevel : null,
                            card: player.playerCard,
                            last_update: moment().unix(),
                            last_match: matchfetch.data.matchInfo.gameStartMillis,
                        },
                    }
                );
                return res.code(200).send({
                    status: 200,
                    data: {
                        puuid: db.puuid,
                        region: db.region,
                        account_level: player != null ? player.accountLevel : null,
                        name: player.gameName,
                        tag: player.tagLine,
                        card: {
                            small: `https://media.valorant-api.com/playercards/${player.playerCard}/smallart.png`,
                            large: `https://media.valorant-api.com/playercards/${player.playerCard}/largeart.png`,
                            wide: `https://media.valorant-api.com/playercards/${player.playerCard}/wideart.png`,
                            id: player.playerCard,
                        },
                        last_update: 'Now',
                        last_update_raw: moment().unix(),
                    },
                });
            }
            return res.code(200).send({
                status: 200,
                data: {
                    puuid: db.puuid,
                    region: db.region,
                    account_level: db.account_level,
                    name: db.name,
                    tag: db.tag,
                    card: {
                        small: `https://media.valorant-api.com/playercards/${db.card}/smallart.png`,
                        large: `https://media.valorant-api.com/playercards/${db.card}/largeart.png`,
                        wide: `https://media.valorant-api.com/playercards/${db.card}/wideart.png`,
                        id: db.card,
                    },
                    last_update: moment(moment.unix(db.last_update).toISOString()).from(new Date().toISOString()),
                    last_update_raw: db.last_update,
                },
            });
        }
        if (await check404({name, tag})) return errorhandler({status: 404, res, errors: [{instance: 'riot'}]});
        const db = await getPuuid({name, tag});
        if (db) return dbSend();
        return normalfetch();
        const tracker = await makeRequest({
            url: `https://api.tracker.gg/api/v2/valorant/standard/matches/riot/${encodeURI(name)}%23${encodeURI(tag)}/`,
            return_error: true,
            httpAgent: true,
            proxy: true,
            res,
        });
        if (res.sent) return;
        if (tracker.response || !tracker?.data?.data?.matches?.length) return normalfetch();
        const matchfetch = await makeRequest({
            res,
            url: `http://api.henrikdev.xyz/valorant/v2/match/${tracker.data.data.matches[0].attributes.id}`,
            proxy: false,
        });
        if (res.sent) return;
        /*const puuids = [];
        for (let i = 0; matchfetch.data.data.players.all_players.length > i; i++) {
            puuids.push(matchfetch.data.data.players.all_players[i].puuid);
        }
        const puuidpvp = await makeRequest({
            res,
            method: 'PUT',
            body: puuids,
            region: 'eu',
            url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
        });
        if (res.sent) return;
        const puuidcheck = puuidpvp.data.find(
            item =>
                item.GameName.toLowerCase().replaceAll('_', '').replaceAll(' ', '').trim().includes(name.replaceAll(' ', '').replaceAll('_', '').toLowerCase()) &&
                item.TagLine.toLowerCase().replaceAll('_', '').replaceAll(' ', '').trim().includes(tag.replaceAll(' ', '').replaceAll('_', '').toLowerCase())
        );*/
        const trackerstats = tracker.data.data.matches[0].segments[0].stats;
        const player = matchfetch.data.data.players.all_players.find(
            item =>
                item.stats.kills == trackerstats.kills.value &&
                item.stats.deaths == trackerstats.deaths.value &&
                item.stats.assists == trackerstats.assists.value &&
                item.stats.score == trackerstats.score.value
        );
        await getDB('puuids').updateOne(
            {puuid: player.puuid},
            {
                $set: {
                    region: matchfetch.data.data.metadata.region,
                    account_level: player != null ? player.level : 'N.A',
                    name: player.name,
                    tag: player.tag,
                    card: player.player_card,
                    last_update: moment().unix(),
                    last_match: matchfetch.data.data.metadata.game_start * 1000,
                },
            },
            {upsert: true}
        );
        return res.code(200).send({
            status: 200,
            data: {
                puuid: player.puuid,
                region: matchfetch.data.data.metadata.region,
                account_level: player != null ? player.level : 'N.A',
                name: player.name,
                tag: player.tag,
                card: {
                    small: `https://media.valorant-api.com/playercards/${player.player_card}/smallart.png`,
                    large: `https://media.valorant-api.com/playercards/${player.player_card}/largeart.png`,
                    wide: `https://media.valorant-api.com/playercards/${player.player_card}/wideart.png`,
                    id: player.player_card,
                },
                last_update: 'Now',
                last_update_raw: moment().unix(),
            },
        });
    });
}

/*async function xmppfetch() {
    const puuid = await axios.get(`http://185.216.179.26:3700/${encodeURI(name)}/${encodeURI(tag)}`).catch(error => {
        return error;
    });
    if (puuid.response || puuid.data.code != null || puuid.data.iq == null)
        return errorhandler({status: puuid.response ? puuid.response.status : puuid.data.code, res: res, type: 'riot'});
    const region = await getRegion(puuid.data.iq.query.item.attr.puuid);
    if (region.error == 'no match') return res.code(500).send({status: 500, message: "No matches available, can't get puuid"});
    const player = region.data.players.find(item => item.subject == puuid.data.iq.query.item.attr.puuid);
    await mongodb
        .db('INGAME-API')
        .collection('puuids')
        .updateOne(
            {puuid: player.subject},
            {
                $set: {
                    region: region.region,
                    name: player.gameName.trim().replaceAll(' ', ''),
                    tag: player.tagLine.trim().replaceAll(' ', ''),
                    account_level: player != null ? player.accountLevel : null,
                    card: player.playerCard,
                    realname: player.gameName,
                    realtag: player.tagLine,
                    last_update: moment().unix(),
                    last_match: region.data.matchInfo.gameStartMillis,
                },
            },
            {upsert: true}
        );
    return res.code(200).send({
        status: 200,
        data: {
            puuid: player.subject,
            region: region.region,
            account_level: player != null ? player.accountLevel : null,
            name: player.gameName,
            tag: player.tagLine,
            card: {
                small: `https://media.valorant-api.com/playercards/${player.playerCard}/smallart.png`,
                large: `https://media.valorant-api.com/playercards/${player.playerCard}/largeart.png`,
                wide: `https://media.valorant-api.com/playercards/${player.playerCard}/wideart.png`,
                id: player.playerCard,
            },
            last_update: 'Now',
        },
    });
}
async function getRegion(puuid) {
    const regions = ['eu', 'na', 'kr', 'ap'];
    const axiosf = [];
    for (const region of regions) {
        axiosf.push(
            axios
                .get(`https://pd.${region}.a.pvp.net/match-history/v1/history/${puuid}`, {
                    headers: getRiotHeaders(region),
                    timeout: 2500,
                    proxy: {host: proxiehost, port: 3128},
                })
                .catch(error => {
                    return error;
                })
        );
    }
    const results = await axios.all(axiosf);
    if (results.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res: res, type: 'riot'});
    let matchid;
    for (const check of results) {
        if (!check.response) {
            const index = results.findIndex(item => item == check);
            matchid = {
                matchid: check.data.History.length ? check.data.History[0].MatchID : 500,
                region: regions[index],
            };
        }
    }
    if (matchid == undefined || matchid.matchid == 500) return {error: 'no match'};
    const matchdata = await axios
        .get(`https://pd.${matchid.region}.a.pvp.net/match-details/v1/matches/${matchid.matchid}`, {
            headers: getRiotHeaders(matchid.region),
            timeout: 2500,
            proxy: {host: proxiehost, port: 3128},
        })
        .catch(error => {
            return error;
        });
    if (matchdata.code == 'ECONNABORTED') return errorhandler({status: 408, res: res, type: 'riot'});
    return {error: null, data: matchdata.data, region: matchid.region};
}
}*/
