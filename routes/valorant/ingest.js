import {generateNumber, axios, proxies, getRiotHeaders} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/valorant/v1/ingest', async (req, res) => {
        const {puuid, region, matchId} = req.body;
        const proxiehost = proxies[generateNumber()];
        if (!puuid || !region) return res.code(400).send({status: 400, message: "Your request body is missing the puuid or region element, can't proceed"});
        if (!/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(puuid))
            return res.code(400).send({status: 400, message: 'The given value is not a valid puuid'});
        if (matchId) {
            const requests = await axios
                .get(`https://pd.${region}.a.pvp.net/match-details/v1/matches/${matchId}`, {headers: getRiotHeaders('eu'), proxy: {host: proxiehost, port: 3128}})
                .catch(error => {
                    return error;
                });
        }
    });
}
