import {getEsportLeagues, getEsportSchedules, getEsportVods, errorhandler} from '../../server.js';

export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/esports/:type', async (req, res) => {
        let schedules = getEsportSchedules();
        const leagues = getEsportLeagues();
        const vods = getEsportVods();
        const possible_filters = {
            region: [...new Set(leagues.map(i => i.region.toLowerCase().replaceAll(' ', '_')))],
            league: [...new Set(leagues.map(i => i.slug.toLowerCase()))],
        };
        if (req.query.region) {
            if (Array.isArray(req.query.region)) {
                if (req.query.region.some(i => !possible_filters.region.includes(i.toLowerCase())))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 105, details: possible_filters.region}]});
                else schedules = schedules.filter(i => req.query.region.some(k => i.league.region.toLowerCase() == k.toLowerCase()));
            } else {
                if (!possible_filters.region.includes(req.query.region.toLowerCase()))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 105, details: possible_filters.region}]});
                else schedules = schedules.filter(i => req.query.region.toLowerCase() == i.league.region.toLowerCase());
            }
        }
        if (req.query.league) {
            if (Array.isArray(req.query.league)) {
                if (req.query.league.some(i => !possible_filters.league.includes(i.toLowerCase())))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 105, details: possible_filters.league}]});
                else schedules = schedules.filter(i => req.query.league.some(k => i.league.slug.toLowerCase() == k.toLowerCase()));
            } else {
                if (!possible_filters.league.includes(req.query.league.toLowerCase()))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 105, details: possible_filters.league}]});
                else schedules = schedules.filter(i => req.query.league.toLowerCase() == i.league.slug.toLowerCase());
            }
        }
        switch (req.params.type) {
            case 'schedule': {
                return res.send({
                    status: 200,
                    data: schedules.map(i => {
                        return {
                            date: i.startTime,
                            state: i.state,
                            type: i.type,
                            vod: vods.find(k => k.match.id == i.match?.id)?.games?.[0]?.vods?.[0]?.parameter
                                ? `https://youtu.be/${vods.find(k => k.match.id == i.match.id)?.games?.[0]?.vods?.[0]?.parameter}`
                                : null,
                            league: {
                                name: i.league.name,
                                identifier: i.league.slug,
                                icon: i.league.image,
                                region: i.league.region,
                            },
                            tournament: {
                                name: i.tournament.split.name,
                                season: i.tournament.season.name,
                            },
                            match: {
                                id: i.match?.id ?? null,
                                teams:
                                    i.match?.teams?.map(k => {
                                        return {
                                            name: k.name,
                                            code: k.code,
                                            icon: k.image,
                                            has_won: k.result?.outcome == 'win' ?? false,
                                            game_wins: k.result?.gameWins ?? 0,
                                            record: {
                                                wins: k.record?.wins ?? 0,
                                                losses: k.record?.losses ?? 0,
                                            },
                                        };
                                    }) ?? [],
                                game_type: {
                                    type: i.match?.strategy?.type ?? null,
                                    count: i.match?.strategy?.count ?? null,
                                },
                            },
                        };
                    }),
                });
            }
        }
    });
}
