import {canvas} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/crosshair/:type', async (req, res) => {
        const n = {
                '0:p': ['general.adsUsePrimary', 0, 1, !0, e => 0 !== e],
                '0:c': ['general.overwriteAllPrimary', 0, 1, !0, e => 0 !== e],
                '0:s': ['general.advancedOptions', 0, 1, !0, e => 0 !== e],
                'P:c': ['primary.color', 0, 8, !0],
                'P:u': ['primary.hexColor.value', 0, 4294967295, !0, e => i(e)],
                'P:h': ['primary.outlines.enabled', 0, 1, !0, e => 0 !== e],
                'P:t': ['primary.outlines.width', 1, 6, !0],
                'P:o': ['primary.outlines.alpha', 0, 1, !1],
                'P:d': ['primary.dot.enabled', 0, 1, !0, e => 0 !== e],
                'P:b': ['primary.hexColor.enabled', 0, 1, !0, e => 0 !== e],
                'P:z': ['primary.dot.width', 1, 6, !0],
                'P:a': ['primary.dot.alpha', 0, 1, !1],
                'P:f': ['general.hideOnFire', 0, 1, !0, e => 0 !== e],
                'P:s': ['general.followSpectating', 0, 1, !0, e => 0 !== e],
                'P:m': ['primary.overwriteFireMul', 0, 1, !0, e => 0 !== e],
                'P:0b': ['primary.inner.enabled', 0, 1, !0, e => 0 !== e],
                'P:0t': ['primary.inner.width', 0, 10, !0],
                'P:0l': ['primary.inner.length', 0, 20, !0],
                'P:0v': ['primary.inner.vertical.length', 0, 20, !0],
                'P:0g': ['primary.inner.vertical.enabled', 0, 1, !0, e => 0 !== e],
                'P:0o': ['primary.inner.offset', 0, 20, !0],
                'P:0a': ['primary.inner.alpha', 0, 1, !1],
                'P:0m': ['primary.inner.moveMul.enabled', 0, 1, !0, e => 0 !== e],
                'P:0f': ['primary.inner.fireMul.enabled', 0, 1, !0, e => 0 !== e],
                'P:0s': ['primary.inner.moveMul.mul', 0, 3, !1],
                'P:0e': ['primary.inner.fireMul.mul', 0, 3, !1],
                'P:1b': ['primary.outer.enabled', 0, 1, !0, e => 0 !== e],
                'P:1t': ['primary.outer.width', 0, 10, !0],
                'P:1l': ['primary.outer.length', 0, 10, !0],
                'P:1v': ['primary.outer.vertical.length', 0, 20, !0],
                'P:1g': ['primary.outer.vertical.enabled', 0, 1, !0, e => 0 !== e],
                'P:1o': ['primary.outer.offset', 0, 40, !0],
                'P:1a': ['primary.outer.alpha', 0, 1, !1],
                'P:1m': ['primary.outer.moveMul.enabled', 0, 1, !0, e => 0 !== e],
                'P:1f': ['primary.outer.fireMul.enabled', 0, 1, !0, e => 0 !== e],
                'P:1s': ['primary.outer.moveMul.mul', 0, 3, !1],
                'P:1e': ['primary.outer.fireMul.mul', 0, 3, !1],
                'A:c': ['ads.color', 0, 8, !0],
                'A:u': ['ads.hexColor.value', 0, 4294967295, !0, e => i(e)],
                'A:h': ['ads.outlines.enabled', 0, 1, !0, e => 0 !== e],
                'A:t': ['ads.outlines.width', 1, 6, !0],
                'A:o': ['ads.outlines.alpha', 0, 1, !1],
                'A:d': ['ads.dot.enabled', 0, 1, !0, e => 0 !== e],
                'A:b': ['ads.hexColor.enabled', 0, 1, !0, e => 0 !== e],
                'A:z': ['ads.dot.width', 1, 6, !0],
                'A:a': ['ads.dot.alpha', 0, 1, !1],
                'A:m': ['ads.overwriteFireMul', 0, 1, !0, e => 0 !== e],
                'A:0b': ['ads.inner.enabled', 0, 1, !0, e => 0 !== e],
                'A:0t': ['ads.inner.width', 0, 10, !0],
                'A:0l': ['ads.inner.length', 0, 20, !0],
                'A:0v': ['ads.inner.vertical.length', 0, 20, !0],
                'A:0g': ['ads.inner.vertical.enabled', 0, 1, !0, e => 0 !== e],
                'A:0o': ['ads.inner.offset', 0, 20, !0],
                'A:0a': ['ads.inner.alpha', 0, 1, !1],
                'A:0m': ['ads.inner.moveMul.enabled', 0, 1, !0, e => 0 !== e],
                'A:0f': ['ads.inner.fireMul.enabled', 0, 1, !0, e => 0 !== e],
                'A:0s': ['ads.inner.moveMul.mul', 0, 3, !1],
                'A:0e': ['ads.inner.fireMul.mul', 0, 3, !1],
                'A:1b': ['ads.outer.enabled', 0, 1, !0, e => 0 !== e],
                'A:1t': ['ads.outer.width', 0, 10, !0],
                'A:1l': ['ads.outer.length', 0, 10, !0],
                'A:1v': ['ads.outer.vertical.length', 0, 20, !0],
                'A:1g': ['ads.outer.vertical.enabled', 0, 1, !0, e => 0 !== e],
                'A:1o': ['ads.outer.offset', 0, 40, !0],
                'A:1a': ['ads.outer.alpha', 0, 1, !1],
                'A:1m': ['ads.outer.moveMul.enabled', 0, 1, !0, e => 0 !== e],
                'A:1f': ['ads.outer.fireMul.enabled', 0, 1, !0, e => 0 !== e],
                'A:1s': ['ads.outer.moveMul.mul', 0, 3, !1],
                'A:1e': ['ads.outer.fireMul.mul', 0, 3, !1],
                'S:b': ['sniper.hexColor.enabled', 0, 1, !0, e => 0 !== e],
                'S:c': ['sniper.color', 0, 8, !0],
                'S:t': ['sniper.hexColor.value', 0, 4294967295, !0, e => i(e)],
                'S:d': ['sniper.dot.enabled', 0, 1, !0, e => 0 !== e],
                'S:s': ['sniper.dot.width', 0, 4, !1],
                'S:o': ['sniper.dot.alpha', 0, 1, !1],
            },
            r = ['P', 'A', 'S'],
            l = {
                general: {
                    advancedOptions: !1,
                    adsUsePrimary: !0,
                    overwriteAllPrimary: !1,
                    hideOnFire: 0,
                    followSpectating: !0,
                },
                primary: {
                    color: 0,
                    useCustomColor: !1,
                    hexColor: {
                        enabled: !1,
                        value: 'FFFFFFFF',
                    },
                    outlines: {
                        enabled: !0,
                        width: 1,
                        alpha: 0.5,
                    },
                    dot: {
                        enabled: !1,
                        width: 2,
                        alpha: 1,
                    },
                    overwriteFireMul: !1,
                    inner: {
                        enabled: !0,
                        width: 2,
                        length: 6,
                        vertical: {
                            enabled: !1,
                            length: 6,
                        },
                        offset: 3,
                        alpha: 0.8,
                        moveMul: {
                            enabled: !1,
                            mul: 1,
                        },
                        fireMul: {
                            enabled: !0,
                            mul: 1,
                        },
                    },
                    outer: {
                        enabled: !0,
                        width: 2,
                        length: 2,
                        vertical: {
                            enabled: !1,
                            length: 2,
                        },
                        offset: 10,
                        alpha: 0.35,
                        moveMul: {
                            enabled: !0,
                            mul: 1,
                        },
                        fireMul: {
                            enabled: !0,
                            mul: 1,
                        },
                    },
                },
                ads: {
                    color: 0,
                    useCustomColor: !1,
                    hexColor: {
                        enabled: !1,
                        value: 'FFFFFFFF',
                    },
                    outlines: {
                        enabled: !0,
                        width: 1,
                        alpha: 0.5,
                    },
                    dot: {
                        enabled: !1,
                        width: 2,
                        alpha: 1,
                    },
                    overwriteFireMul: !1,
                    inner: {
                        enabled: !0,
                        width: 2,
                        length: 6,
                        vertical: {
                            enabled: !1,
                            length: 6,
                        },
                        offset: 3,
                        alpha: 0.8,
                        moveMul: {
                            enabled: !1,
                            mul: 1,
                        },
                        fireMul: {
                            enabled: !0,
                            mul: 1,
                        },
                    },
                    outer: {
                        enabled: !0,
                        width: 2,
                        length: 2,
                        vertical: {
                            enabled: !1,
                            length: 2,
                        },
                        offset: 10,
                        alpha: 0.35,
                        moveMul: {
                            enabled: !0,
                            mul: 1,
                        },
                        fireMul: {
                            enabled: !0,
                            mul: 1,
                        },
                    },
                },
                sniper: {
                    color: 7,
                    useCustomColor: !1,
                    hexColor: {
                        enabled: !1,
                        value: 'FFFFFFFF',
                    },
                    dot: {
                        enabled: !0,
                        width: 1,
                        alpha: 0.75,
                    },
                },
            },
            a = ['#ffffff', '#00ff00', '#7fff00', '#dfff00', '#ffff00', '#00ffff', '#ff00ff', '#ff0000'],
            o = ['White', 'Green', 'Yellow Green', 'Green Yellow', 'Yellow', 'Cyan', 'Pink', 'Red', 'Custom'],
            i = function (e) {
                let t = e.toString(16).toUpperCase();
                return t.length < 8 && (t = '0'.repeat(8 - t.length) + t), t;
            },
            d = function (e, t, n) {
                const r = t.split('.');
                for (var l = e, a = 0; a < r.length - 1; a++) l = l[r[a]];
                l[r[r.length - 1]] = n;
            },
            u = function (e, t) {
                try {
                    const l = t.split('.');
                    for (var n = e, r = 0; r < l.length - 1; r++) n = n[l[r]];
                    return n[l[l.length - 1]];
                } catch (e) {
                    return;
                }
            },
            s = /^[0-9A-F]{8}$/g,
            h = function (e, t) {
                if (e.length > t + 1) {
                    if (8 === e[t + 1].length && e[t + 1].match(s)) return parseInt(e[t + 1], 16);
                    const n = parseFloat(e[t + 1]);
                    if (!isNaN(n)) return n;
                }
                return !1;
            },
            v = function (e, t, n, r) {
                if ('enabled' in t && !t.enabled)
                    return e.enabled
                        ? {
                              enabled: !1,
                          }
                        : {};
                var l = {};
                for (var a in (t.hexColor && (t.hexColor.enabled = 8 === t.color), t)) {
                    if (!n && ('ads' === a || 'sniper' === a)) continue;
                    if (r && 'ads' === a) continue;
                    const o = e[a],
                        i = t[a];
                    if ('object' == typeof i) {
                        const e = v(o, i, r);
                        0 !== Object.keys(e).length && (l[a] = e);
                    } else o !== i && (l[a] = i);
                }
                return l;
            },
            y = function (e, t, n, r, l, a, o, i) {
                (e.globalAlpha = i),
                    e.fillRect(t, n, r, l),
                    o.enabled && 0 !== r && 0 !== l && ((e.globalAlpha = o.alpha), e.strokeRect(t - a.xy, n - a.xy, r + a.wh, l + a.wh));
            },
            A = {
                green: 'Green Box',
                metall: 'Metal',
                blaugelb: 'Blue/Yellow',
                yellow: 'Yellow',
                orange: 'Orange/White',
                blue: 'Blue/White',
                grass: 'Grass',
                sky: 'Sky',
            },
            convertCodeToObject = function (e) {
                const t = e.split(';');
                var a = JSON.parse(JSON.stringify(l));
                if (t.length <= 1) return a;
                var o = '0';
                const i = t.length;
                for (var d = 1; d < i; d += 2) {
                    if (r.includes(t[d])) {
                        (o = t[d]), d--;
                        continue;
                    }
                    const e = h(t, d);
                    if (!1 === e) {
                        console.error('ignoring invalid key: ' + t[d]);
                        continue;
                    }
                    const l = o + ':' + t[d],
                        i = n[l];
                    if (!i) {
                        console.error('ignoring unmapped key: ' + l);
                        continue;
                    }
                    if (i[3] && !Number.isInteger(e)) {
                        console.error('ignoring non-int value: ' + l + '=' + e);
                        continue;
                    }
                    if (e < i[1] || e > i[2]) {
                        console.error('ignoring out of bounds value: ' + l + '=' + e);
                        continue;
                    }
                    const s = i[0].split('.');
                    for (var u = a, c = 0; c < s.length - 1; c++) u = u[s[c]];
                    u[s[s.length - 1]] = i.length >= 5 ? i[4](e) : e;
                }
                return a;
            };

        switch (req.params.type) {
            case 'convert': {
                return res.code(200).send({status: 200, data: convertCodeToObject(req.query['id'])});
            }
            case 'generate': {
                const size = req.query.size ? (req.query.size > 4096 ? 4096 : Number(req.query.size)) : 1024;
                const Canvas = canvas.createCanvas(size, size);
                const ctx = Canvas.getContext('2d');
                const scaleFactor = Canvas.width / 128;
                const translation = -(Math.pow(2, Math.log2(scaleFactor)) - 1) * 64;
                ctx.scale(scaleFactor, scaleFactor);
                ctx.translate(translation, translation);
                let e = convertCodeToObject(req.query['id']);
                const l = e,
                    i = Canvas.width / 2;
                const t = (e = e[l.general.adsUsePrimary ? 'primary' : r]).outlines,
                    c = {
                        xy: 0.5 * t.width,
                        wh: 1 * t.width,
                    };
                8 == e.color ? (ctx.fillStyle = '#' + e.hexColor.value.substr(0, 6)) : (ctx.fillStyle = a[e.color]), (ctx.lineWidth = t.width);
                const s = ['inner', 'dot', 'outer'];
                for (let d in s) {
                    const r = s[d];
                    if ('dot' === r) {
                        if (e.dot.enabled) {
                            const {width: n, alpha: r} = e.dot;
                            ctx.globalAlpha = r;
                            const l = i - Math.ceil(n / 2);
                            ctx.fillRect(l, l, n, n), t.enabled && ((ctx.globalAlpha = t.alpha), ctx.strokeRect(l - c.xy, l - c.xy, n + c.wh, n + c.wh));
                        }
                    } else {
                        const a = e[r];
                        if (a.enabled) {
                            let {width: r, length: d, alpha: s, fireMul: h} = a;
                            let u = a.offset;
                            h.enabled && !e.overwriteFireMul && (u += 4);
                            const m = r % 2;
                            y(ctx, i + u, Math.floor(i - r / 2), d, r, c, t, s),
                                y(ctx, i - u - d - m, Math.floor(i - r / 2), d, r, c, t, s),
                                a.vertical.enabled && (d = a.vertical.length),
                                y(ctx, Math.floor(i - r / 2), i + u, r, d, c, t, s),
                                (n && l.general.hideOnFire) || y(ctx, Math.floor(i - r / 2), i - u - d - m, r, d, c, t, s);
                        }
                    }
                }
                console.log(Canvas);
                res.code(200).type('image/png').send(Canvas.toBuffer());
            }
        }
    });
}
