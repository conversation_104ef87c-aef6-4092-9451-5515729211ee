import {getDB, makeRequest, regions} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/version/:region', async (req, res) => {
        const region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        if (!regions.includes(region)) return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        const version = await getDB('versions').findOne({region});
        delete version.patch_url;
        delete version._id;
        res.send({
            status: 200,
            data: {...version},
        });
    });
}
