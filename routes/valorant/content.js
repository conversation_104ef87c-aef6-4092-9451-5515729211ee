import {fs, locals, errorhandler} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/v1/content', async (req, res) => {
        if (req.query.locale) {
            if (locals.some(item => item.toLowerCase() == req.query.locale.toLowerCase()))
                return res.code(200).send(fs.readFileSync(`./files/valorant/content/content-${req.query.locale.toLowerCase()}.json`));
            return errorhandler({status: 400, res, errors: [{instance: 'own', code: 108, details: locals}]});
        }
        res.code(200).send(fs.readFileSync('./files/valorant/content/content-en-gb.json'));
    });
}
