import {fs, getSkins, getBuddies, getSprays, getPlayerCards, item_types_val, getPlayerTitles, basedata} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/store-featured', async (req, res) => {
        const sdata = JSON.parse(fs.readFileSync('./files/valorant/store/store_featured.json'));
        switch (req.params.version) {
            case 'v1': {
                return res.code(200).send({status: 200, data: {FeaturedBundle: sdata}});
            }
            case 'v2': {
                const itemhandler = items => {
                    return items.map(k => {
                        const dataset =
                            item_types_val[k.Item.ItemTypeID] == 'skin_level'
                                ? getSkins()
                                : item_types_val[k.Item.ItemTypeID] == 'buddy'
                                ? getBuddies()
                                : item_types_val[k.Item.ItemTypeID] == 'spray'
                                ? {levels: getSprays(), base: getSprays()}
                                : item_types_val[k.Item.ItemTypeID] == 'player_card'
                                ? {levels: getPlayerCards(), base: getPlayerCards()}
                                : item_types_val[k.Item.ItemTypeID] == 'player_title'
                                ? {levels: getPlayerTitles(), base: getPlayerTitles()}
                                : null;
                        const uuid = dataset.levels.find(l => l.uuid == k.Item.ItemID).parent
                            ? dataset.levels.find(l => l.uuid == k.Item.ItemID).parent
                            : dataset.levels.find(l => l.uuid == k.Item.ItemID).uuid;
                        const base_data = dataset.base.find(k => k.uuid == uuid);
                        return {
                            uuid,
                            name: base_data.titleText ?? base_data.displayName,
                            image: base_data.displayIcon ?? null,
                            type: item_types_val[k.Item.ItemTypeID],
                            amount: k.Item.Amount,
                            discount_percent: k.DiscountPercent,
                            base_price: k.BasePrice,
                            discounted_price: k.DiscountedPrice,
                            promo_item: k.IsPromoItem,
                        };
                    });
                };
                return res.code(200).send({
                    status: 200,
                    data: sdata.Bundles.map(i => {
                        const items = itemhandler(i.Items);
                        return {
                            bundle_uuid: i.DataAssetID,
                            bundle_price: items.reduce((pval, cval) => pval + cval.discounted_price, 0),
                            whole_sale_only: i.WholesaleOnly,
                            items,
                            seconds_remaining: i.DurationRemainingInSeconds,
                            expires_at: i.timestamp,
                        };
                    }),
                });
            }
            default: {
                return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
            }
        }
    });
}
