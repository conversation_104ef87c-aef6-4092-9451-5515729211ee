import {getCurrentLeaderboard, errorhandler, regions, fs, getCategories, getCurrentSeasonShort} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorant/:version/leaderboard/:region', async (req, res) => {
        const region = req.params.region.toLowerCase();
        if (![...regions, 'br', 'latam'].includes(region))
            return errorhandler({res, status: 400, errors: [{instance: 'own', code: 104, details: [...regions, 'latam', 'br']}]});
        const category = getCategories();
        switch (req.params.version) {
            case 'v1': {
                const errors = [];
                if (!req.query.name && req.query.tag)
                    errors.push({instance: 'own', code: 109, details: 'If you want to filter for name and tag you need to enter both values'});
                if (req.query.name && !req.query.tag)
                    errors.push({instance: 'own', code: 110, details: 'If you want to filter for name and tag you need to enter both values'});
                if (errors.length) return errorhandler({status: 400, res, errors});
                if (req.query.season && !category.includes(req.query.season.toLowerCase()))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 115, details: category}]});
                const lbdata_1 =
                    (req.query.season && req.query.season == getCurrentSeasonShort()) || !req.query.season
                        ? getCurrentLeaderboard(region)
                        : JSON.parse(fs.readFileSync(`./files/valorant/leaderboard/${req.query.season.toLowerCase()}-leaderboard-${region}.json`));
                if (!lbdata_1?.players) return errorhandler({status: 404, res, errors: [{instance: 'riot'}]});
                const lbdata = [...lbdata_1.players];
                lbdata.length = 1000;
                if (res.sent) return;
                if (req.query.name && req.query.tag) {
                    const searched_player = lbdata.filter(
                        item => item.gameName.toLowerCase() == req.query.name.toLowerCase() && item.tagLine.toLowerCase() == req.query.tag.toLowerCase()
                    );
                    if (!searched_player.length) return errorhandler({status: 404, res, errors: [{instance: 'riot', code: 111}]});
                    return res.code(200).send({status: 200, data: searched_player});
                }
                if (req.query.puuid) {
                    const searched_player = lbdata.filter(item => item.puuid == req.query.puuid);
                    if (!searched_player.length) return errorhandler({status: 404, res, errors: [{instance: 'riot', code: 111}]});
                    return res.code(200).send({status: 200, data: searched_player});
                }
                return res.code(200).send(lbdata);
            }
            case 'v2': {
                const errors = [];
                if (!req.query.name && req.query.tag)
                    errors.push({instance: 'own', code: 109, details: 'If you want to filter for name and tag you need to enter both values'});
                if (req.query.name && !req.query.tag)
                    errors.push({instance: 'own', code: 110, details: 'If you want to filter for name and tag you need to enter both values'});
                if (errors.length) return errorhandler({status: 400, res, errors});
                if (req.query.season && !category.includes(req.query.season.toLowerCase()))
                    return errorhandler({status: 400, res, errors: [{instance: 'own', code: 115, details: category}]});
                const lbdata =
                    (req.query.season && req.query.season == getCurrentSeasonShort()) || !req.query.season
                        ? {...getCurrentLeaderboard(region)}
                        : {...JSON.parse(fs.readFileSync(`./files/valorant/leaderboard/${req.query.season.toLowerCase()}-leaderboard-${region}.json`))};
                if (!lbdata?.players) return errorhandler({status: 404, res, errors: [{instance: 'riot'}]});
                if (req.query.name && req.query.tag) {
                    if (req.query.season && !category.includes(req.query.season.toLowerCase()))
                        return errorhandler({status: 400, res, errors: [{instance: 'own', code: 115, details: category}]});
                    const searched_player = lbdata.players.filter(
                        item => item.gameName.toLowerCase() == req.query.name.toLowerCase() && item.tagLine.toLowerCase() == req.query.tag.toLowerCase()
                    );
                    if (!searched_player.length) return errorhandler({status: 404, res, errors: [{instance: 'riot', code: 111}]});
                    return res.code(200).send({status: 200, data: searched_player});
                }
                if (req.query.puuid) {
                    if (req.query.season && !category.includes(req.query.season.toLowerCase()))
                        return errorhandler({status: 400, res, errors: [{instance: 'own', code: 115, details: category}]});
                    const searched_player = lbdata.players.filter(item => item.puuid == req.query.puuid);
                    if (!searched_player.length) return errorhandler({status: 404, res, errors: [{instance: 'riot', code: 111}]});
                    return res.code(200).send({status: 200, data: searched_player});
                }
                res.code(200).send(lbdata);
            }
            default: {
                return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
            }
        }
    });
}
