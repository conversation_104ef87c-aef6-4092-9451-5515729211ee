import {makeRequest, getMultiVersusHeaders, moment, mv_maps, mv_agents} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/match/:id', async (req, res) => {
        const details = await makeRequest({
            url: `https://dokken-api.wbagora.com/matches/${req.params.id}`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        const formatPlayer = data => {
            return data.map(k => {
                if (k.account_id)
                    return {
                        id: k.account_id,
                        username: k.identity.alternate.wb_network[0].username,
                        character: null,
                        damage: 0,
                        ringouts: 0,
                        score: 0,
                        deaths: 0,
                        platform: null,
                        team: null,
                    };
                return {
                    id: k.AccountId,
                    username: k.<PERSON>rna<PERSON>,
                    character: mv_agents[k.CharacterSlug.toLowerCase()],
                    damage: k.DamageDone,
                    ringouts: k.Ringouts,
                    score: k.Score,
                    deaths: k.Deaths,
                    platform: k.Played<PERSON>form,
                    team: k.Team<PERSON>ndex,
                };
            });
        };
        return res.code(200).send({
            status: 200,
            data: {
                metadata: {
                    created_at: moment(details.data.created_at).unix(),
                    completed_at: moment(details.data.completion_time).unix(),
                    state: details.data.state,
                    draw: details.data.draw,
                    map: mv_maps[details.data.server_data.MapName],
                    custom_match: details.data.server_data.IsCustomMatch,
                    cluster: details.data.cluster,
                    mode: details.data.template.slug,
                },
                players: {
                    all: details.data.state == 'open' ? formatPlayer(details.data.players.all) : formatPlayer(details.data.server_data.PlayerData),
                    0:
                        details.data.state == 'open'
                            ? formatPlayer(details.data.players.all).filter(k => k.TeamIndex == 0)
                            : formatPlayer(details.data.server_data.PlayerData.filter(k => k.TeamIndex == 0)),
                    1:
                        details.data.state == 'open'
                            ? formatPlayer(details.data.players.all).filter(k => k.TeamIndex == 1)
                            : formatPlayer(details.data.server_data.PlayerData.filter(k => k.TeamIndex == 1)),
                },
                teams: {
                    0: {
                        won: details.data.state == 'open' ? null : details.data.server_data.TeamScores[0] > details.data.server_data.TeamScores[1],
                    },
                    1: {
                        won: details.data.state == 'open' ? null : details.data.server_data.TeamScores[1] > details.data.server_data.TeamScores[0],
                    },
                },
            },
        });
    });
}
