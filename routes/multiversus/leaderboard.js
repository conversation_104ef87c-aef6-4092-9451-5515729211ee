import {makeRequest, getMultiVersusHeaders, moment, errorhandler} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/leaderboard/:mode', async (req, res) => {
        if (!['1v1', '2v2'].some(i => i == req.params.mode))
            return errorhandler({res, status: 400, errors: [{instance: 'multiversus', code: 201, details: ['1v1', '2v2']}]});
        const ladder = await makeRequest({
            url: `https://dokken-api.wbagora.com/leaderboards/${req.params.mode}/show`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        const results = ladder.data.leaders.map(i => {
            return {
                id: i.member,
                username: i.account.identity.alternate.wb_network[0].username,
                created_at: moment(i.account.created_at).unix(),
                rank: i.rank,
                gamemodes: {
                    '1v1': {
                        wins: i.profile.matches['1v1'] ? i.profile.matches['1v1'].win : 0,
                        win_streak: i.profile.matches['1v1'] ? i.profile.matches['1v1'].win_streak : 0,
                        longest_win_streak: i.profile.matches['1v1'] ? i.profile.matches['1v1'].longest_win_streak : 0,
                        losses: i.profile.matches['1v1'] ? i.profile.matches['1v1'].loss : 0,
                    },
                    '2v2': {
                        wins: i.profile.matches['2v2'] ? i.profile.matches['2v2'].win : 0,
                        win_streak: i.profile.matches['2v2'] ? i.profile.matches['2v2'].win_streak : 0,
                        longest_win_streak: i.profile.matches['2v2'] ? i.profile.matches['2v2'].longest_win_streak : 0,
                        losses: i.profile.matches['2v2'] ? i.profile.matches['2v2'].loss : 0,
                    },
                },
            };
        });
        return res.code(200).send({
            status: 200,
            data: results,
        });
    });
}
