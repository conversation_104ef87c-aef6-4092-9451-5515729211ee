import {makeRequest, getMultiVersusHeaders, moment, errorhandler, mv_agents} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/by-id/:toplevel/:sub/:id', async (req, res) => {
        switch (req.params.toplevel) {
            case 'account': {
                if (req.params.sub != 'details') return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
                const details = (
                    await Promise.allSettled([
                        makeRequest({
                            url: `https://dokken-api.wbagora.com/profiles/${req.params.id}`,
                            proxy: true,
                            res,
                            overwrite_headers: getMultiVersusHeaders(),
                            instance: 'multiverse',
                        }),
                        makeRequest({
                            url: `https://dokken-api.wbagora.com/accounts/${req.params.id}?fields=server_data&fields=presence`,
                            proxy: true,
                            res,
                            overwrite_headers: getMultiVersusHeaders(),
                            instance: 'multiverse',
                        }),
                    ])
                ).map(i => i.value.data);
                return res.code(200).send({
                    status: 200,
                    data: {
                        id: details[1].id,
                        username: details[1].identity.alternate.wb_network[0].username,
                        created_at: moment(details[1].created_at).unix(),
                        level: details[1].server_data.Level,
                        xp: details[1].server_data.CurrentXP,
                        online: details[1].presence == 'online' ? true : false,
                        characters: Object.values(details[1].server_data.Characters).map((k, index) => {
                            return {
                                agent: mv_agents[Object.keys(details[1].server_data.Characters)[index].toLowerCase()],
                                xp: Object.values(k)[0].CurrentXP,
                                level: Object.values(k)[0].Level,
                                wins: details[0].server_data.stat_trackers.character_wins[Object.keys(details[1].server_data.Characters)[index]],
                            };
                        }),
                        stats: {
                            highest_damage_dealt: details[0].server_data.stat_trackers.HighestDamageDealt,
                            assists: details[0].server_data.stat_trackers.TotalAssists,
                            ringouts: {
                                total: details[0].server_data.stat_trackers.TotalRingouts ?? 0,
                                single_ringouts: details[0].server_data.lifetime_ringouts ?? 0,
                                double_ringouts: details[0].server_data.stat_trackers.TotalDoubleRingouts ?? 0,
                            },
                            wins: details[0].server_data.stat_trackers.TotalWins,
                            dodged_attacks: details[0].server_data.stat_trackers.TotalAttacksDodged,
                            damage: details[0].server_data.lifetime_damage,
                            matches: details[0].server_data.matches_played,
                        },
                        gamemodes: {
                            '1v1': {
                                wins: details[0].matches['1v1'] ? details[0].matches['1v1'].win : 0,
                                win_streak: details[0].matches['1v1'] ? details[0].matches['1v1'].win_streak : 0,
                                longest_win_streak: details[0].matches['1v1'] ? details[0].matches['1v1'].longest_win_streak : 0,
                                losses: details[0].matches['1v1'] ? details[0].matches['1v1'].loss : 0,
                            },
                            '2v2': {
                                wins: details[0].matches['2v2'] ? details[0].matches['2v2'].win : 0,
                                win_streak: details[0].matches['2v2'] ? details[0].matches['2v2'].win_streak : 0,
                                longest_win_streak: details[0].matches['2v2'] ? details[0].matches['2v2'].longest_win_streak : 0,
                                losses: details[0].matches['2v2'] ? details[0].matches['2v2'].loss : 0,
                            },
                        },
                    },
                });
            }
            case 'leaderboard-placements': {
                if (!['1v1', '2v2'].some(i => i == req.params.sub))
                    return errorhandler({res, status: 400, errors: [{instance: 'multiversus', code: 201, details: ['1v1', '2v2']}]});
                const details = await makeRequest({
                    url: `https://dokken-api.wbagora.com/leaderboards/${req.params.sub}/score-and-rank/${req.params.id}?account_fields=identity&account_fields=server_data&fields=server_data.${req.params.sub}shuffle.0.topRating&partial_response=1`,
                    proxy: true,
                    res,
                    overwrite_headers: getMultiVersusHeaders(),
                    instance: 'multiverse',
                });
                return res.code(200).send({
                    status: 200,
                    data: {
                        id: req.params.id,
                        username: details.data.account['identity.alternate.wb_network'][0].username,
                        mode: req.params.sub,
                        rank: details.data.rank,
                        score: details.data.score ? details.data.score.toFixed(2) : '0',
                        top_rating_character: {
                            name: mv_agents[details.data.profile[`server_data.${req.params.sub}shuffle.0.topRating.character`]],
                            xp: details.data.account[
                                `server_data.Characters.${details.data.profile[`server_data.${req.params.sub}shuffle.0.topRating.character`]}.Mastery.CurrentXP`
                            ],
                            level: details.data.account[
                                `server_data.Characters.${details.data.profile[`server_data.${req.params.sub}shuffle.0.topRating.character`]}.Mastery.Level`
                            ],
                        },
                    },
                });
            }
        }
    });
}
