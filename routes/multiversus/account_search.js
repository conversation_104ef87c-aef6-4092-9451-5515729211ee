import {makeRequest, getMultiVersusHeaders, moment} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/account/search/:name', async (req, res) => {
        const search = await makeRequest({
            url: `https://dokken-api.wbagora.com/profiles/search_queries/get-by-username/run?limit=10&username=${encodeURI(
                req.params.name
            )}&account_fields=identity&account_fields=presence&account_fields=server_data&account_fields=data&partial_response=1`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        if (!search.data.total) return res.code({status: 200, data: []});
        const results = search.data.results.map(i => {
            return {
                id: i.result.account_id,
                username: i.result.account['identity.alternate.wb_network'][0].username,
                created_at: moment(i.result.created_at).unix(),
                level: i.result.account['server_data.Level'],
                xp: i.result.account['server_data.CurrentXP'],
                online: i.result.account.presence == 'online' ? true : false,
            };
        });
        return res.code(200).send({status: 200, data: results});
    });
}
