import {makeRequest, getMultiVersusHeaders, moment, errorhandler, mv_agents} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/leaderboard-placements/:mode/:name', async (req, res) => {
        if (!['1v1', '2v2'].some(i => i == req.params.mode))
            return errorhandler({res, status: 400, errors: [{instance: 'multiversus', code: 201, details: ['1v1', '2v2']}]});
        const search = await makeRequest({
            url: `https://dokken-api.wbagora.com/profiles/search_queries/get-by-username/run?limit=1&username=${encodeURI(req.params.name)}`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        if (!search.data.total) return errorhandler({res, status: 404, errors: [{instance: 'multiversus'}]});
        const details = await makeRequest({
            url: `https://dokken-api.wbagora.com/leaderboards/${req.params.mode}/score-and-rank/${search.data.results[0].result.account_id}?account_fields=identity&account_fields=server_data&fields=server_data.${req.params.mode}shuffle.0.topRating&partial_response=1`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        return res.code(200).send({
            status: 200,
            data: {
                id: search.data.results[0].result.account_id,
                username: details.data.account['identity.alternate.wb_network'][0].username,
                mode: req.params.mode,
                rank: details.data.rank,
                score: details.data.score ? details.data.score.toFixed(2) : '0',
                top_rating_character: {
                    name: mv_agents[details.data.profile[`server_data.${req.params.mode}shuffle.0.topRating.character`]],
                    xp: details.data.account[
                        `server_data.Characters.${details.data.profile[`server_data.${req.params.mode}shuffle.0.topRating.character`]}.Mastery.CurrentXP`
                    ],
                    level: details.data.account[
                        `server_data.Characters.${details.data.profile[`server_data.${req.params.mode}shuffle.0.topRating.character`]}.Mastery.Level`
                    ],
                },
            },
        });
    });
}
