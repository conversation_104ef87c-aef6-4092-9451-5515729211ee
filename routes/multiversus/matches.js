import {makeRequest, getMultiVersusHeaders, moment, errorhandler, mv_maps, mv_agents} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/matches/:name', async (req, res) => {
        const search = await makeRequest({
            url: `https://dokken-api.wbagora.com/profiles/search_queries/get-by-username/run?limit=1&username=${encodeURI(req.params.name)}`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        if (!search.data.total) return errorhandler({res, status: 404, errors: [{instance: 'multiversus'}]});
        const queries = new URLSearchParams('fields=server_data&templates=2v2&templates=1v1&templates=ffa');
        queries.append('count', req.query.size ?? 10);
        queries.append('page', req.query.page ?? 1);
        const details = await makeRequest({
            url: `https://dokken-api.wbagora.com/matches/all/${search.data.results[0].result.account_id}?${queries}`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        const formatPlayer = data => {
            return data.map(k => {
                if (k.account_id)
                    return {
                        id: k.account_id,
                        username: k.identity.alternate.wb_network[0].username,
                        character: null,
                        damage: 0,
                        ringouts: 0,
                        score: 0,
                        deaths: 0,
                        platform: null,
                        team: null,
                    };
                return {
                    id: k.AccountId,
                    username: k.Username,
                    character: mv_agents[k.CharacterSlug.toLowerCase()],
                    damage: k.DamageDone,
                    ringouts: k.Ringouts,
                    score: k.Score,
                    deaths: k.Deaths,
                    platform: k.PlayedPlatform,
                    team: k.TeamIndex,
                };
            });
        };
        const results = details.data.matches.map(i => {
            return {
                metadata: {
                    created_at: moment(i.created_at).unix(),
                    completed_at: moment(i.completion_time).unix(),
                    state: i.state,
                    draw: i.draw,
                    id: i.id,
                    map: mv_maps[i.server_data.MapName],
                    custom_match: i.server_data.IsCustomMatch,
                    cluster: i.cluster,
                    mode: i.template.slug,
                },
                players: {
                    all: i.state == 'open' ? formatPlayer(i.players.all) : formatPlayer(i.server_data.PlayerData),
                    0:
                        i.state == 'open'
                            ? formatPlayer(i.players.all).filter(k => k.TeamIndex == 0)
                            : formatPlayer(i.server_data.PlayerData.filter(k => k.TeamIndex == 0)),
                    1:
                        i.state == 'open'
                            ? formatPlayer(i.players.all).filter(k => k.TeamIndex == 1)
                            : formatPlayer(i.server_data.PlayerData.filter(k => k.TeamIndex == 1)),
                },
                teams: {
                    0: {
                        won: i.state == 'open' ? null : i.server_data.TeamScores[0] > i.server_data.TeamScores[1],
                    },
                    1: {
                        won: i.state == 'open' ? null : i.server_data.TeamScores[1] > i.server_data.TeamScores[0],
                    },
                },
            };
        });
        return res.code(200).send({
            status: 200,
            data: results,
        });
    });
}
