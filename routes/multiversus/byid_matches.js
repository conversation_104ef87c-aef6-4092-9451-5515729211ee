import {makeRequest, getMultiVersusHeaders, moment, mv_maps, mv_agents} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/by-id/matches/:id', async (req, res) => {
        const queries = new URLSearchParams('fields=server_data&templates=2v2&templates=1v1&templates=ffa');
        queries.append('count', req.query.size ?? 10);
        queries.append('page', req.query.page ?? 1);
        const details = await makeRequest({
            url: `https://dokken-api.wbagora.com/matches/all/${req.params.id}?${queries}`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        if (res.sent) return;
        const formatPlayer = data => {
            return data.map(k => {
                return {
                    id: k.AccountId,
                    username: k.<PERSON>,
                    character: mv_agents[k.CharacterSlug.toLowerCase()],
                    damage: k.Damage<PERSON>one,
                    ringouts: k.Ringouts,
                    score: k.Score,
                    deaths: k.Deaths,
                    platform: k.PlayedPlatform,
                    team: k.TeamIndex,
                };
            });
        };
        const results = details.data.matches.map(i => {
            return {
                metadata: {
                    created_at: moment(i.created_at).unix(),
                    completed_at: moment(i.completion_time).unix(),
                    state: i.state,
                    draw: i.draw,
                    id: i.id,
                    map: mv_maps[i.server_data.MapName],
                    custom_match: i.server_data.IsCustomMatch,
                    cluster: i.cluster,
                    mode: i.template.slug,
                },
                players: {
                    all: formatPlayer(i.server_data.PlayerData),
                    0: formatPlayer(i.server_data.PlayerData.filter(k => k.TeamIndex == 0)),
                    1: formatPlayer(i.server_data.PlayerData.filter(k => k.TeamIndex == 1)),
                },
                teams: {
                    0: {
                        won: i.server_data.TeamScores[0] > i.server_data.TeamScores[1],
                    },
                    1: {
                        won: i.server_data.TeamScores[1] > i.server_data.TeamScores[0],
                    },
                },
            };
        });
        return res.code(200).send({
            status: 200,
            data: results,
        });
    });
}
