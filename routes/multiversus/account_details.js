import {makeRequest, getMultiVersusHeaders, moment, errorhandler, mv_agents} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/multiversus/v1/account/details/:name', async (req, res) => {
        const search = await makeRequest({
            url: `https://dokken-api.wbagora.com/profiles/search_queries/get-by-username/run?limit=1&username=${encodeURI(req.params.name)}`,
            proxy: true,
            res,
            overwrite_headers: getMultiVersusHeaders(),
            instance: 'multiverse',
        });
        if (!search.data.total) return errorhandler({res, status: 404, errors: [{instance: 'multiversus'}]});
        const details = (
            await Promise.allSettled([
                makeRequest({
                    url: `https://dokken-api.wbagora.com/profiles/${search.data.results[0].result.account_id}`,
                    proxy: true,
                    res,
                    overwrite_headers: getMultiVersusHeaders(),
                    instance: 'multiverse',
                }),
                makeRequest({
                    url: `https://dokken-api.wbagora.com/accounts/${search.data.results[0].result.account_id}?fields=server_data&fields=presence`,
                    proxy: true,
                    res,
                    overwrite_headers: getMultiVersusHeaders(),
                    instance: 'multiverse',
                }),
            ])
        ).map(i => i.value.data);
        return res.code(200).send({
            status: 200,
            data: {
                id: search.data.results[0].result.account_id,
                username: details[1].identity.alternate.wb_network[0].username,
                created_at: moment(search.data.results[0].result.created_at).unix(),
                level: details[1].server_data.Level,
                xp: details[1].server_data.CurrentXP,
                online: details[1].presence == 'online' ? true : false,
                characters: Object.values(details[1].server_data.Characters).map((k, index) => {
                    return {
                        agent: mv_agents[Object.keys(details[1].server_data.Characters)[index].toLowerCase()],
                        xp: Object.values(k)[0].CurrentXP,
                        level: Object.values(k)[0].Level,
                        wins: details[0].server_data.stat_trackers.character_wins[Object.keys(details[1].server_data.Characters)[index]],
                    };
                }),
                stats: {
                    highest_damage_dealt: details[0].server_data.stat_trackers.HighestDamageDealt,
                    assists: details[0].server_data.stat_trackers.TotalAssists,
                    ringouts: {
                        total: details[0].server_data.stat_trackers.TotalRingouts ?? 0,
                        single_ringouts: details[0].server_data.lifetime_ringouts ?? 0,
                        double_ringouts: details[0].server_data.stat_trackers.TotalDoubleRingouts ?? 0,
                    },
                    wins: details[0].server_data.stat_trackers.TotalWins,
                    dodged_attacks: details[0].server_data.stat_trackers.TotalAttacksDodged,
                    damage: details[0].server_data.lifetime_damage,
                    matches: details[0].server_data.matches_played,
                },
                gamemodes: {
                    '1v1': {
                        wins: details[0].matches['1v1'] ? details[0].matches['1v1'].win : 0,
                        win_streak: details[0].matches['1v1'] ? details[0].matches['1v1'].win_streak : 0,
                        longest_win_streak: details[0].matches['1v1'] ? details[0].matches['1v1'].longest_win_streak : 0,
                        losses: details[0].matches['1v1'] ? details[0].matches['1v1'].loss : 0,
                    },
                    '2v2': {
                        wins: details[0].matches['2v2'] ? details[0].matches['2v2'].win : 0,
                        win_streak: details[0].matches['2v2'] ? details[0].matches['2v2'].win_streak : 0,
                        longest_win_streak: details[0].matches['2v2'] ? details[0].matches['2v2'].longest_win_streak : 0,
                        losses: details[0].matches['2v2'] ? details[0].matches['2v2'].loss : 0,
                    },
                },
            },
        });
    });
}
