use std::collections::HashMap;
use std::sync::Arc;
use axum::body::Body;
use axum::extract::{ Path, Query, Request, State };
use axum::response::Response;
use mongodb::bson::doc;
use serde_json::json;
use crate::{ AppState, get_db };
use crate::structs::database::APITokens;
use crate::structs::errors::{ error_handler, ErrorCodes };
use crate::structs::helper::{ get_c_season, get_season_by_id };
use crate::structs::jobs::{ load_website_wrapper, update_player_leaderboard_wrapper, update_premier_leaderboard_wrapper };
use crate::structs::paths::{ AdminV1Jobs };

pub async fn jobs(Query(query): Query<HashMap<String, String>>, Path(path): Path<AdminV1Jobs>, State(app_state): State<Arc<AppState>>, req: Request) -> Response {
	let req_headers = req.headers().clone();
	let token = req_headers.get("authorization").clone();
	let api_key = if token.is_some() {
		Some(token.unwrap().to_str().unwrap().to_string())
	} else if query.get("api_key").is_some() {
		Some(query.get("api_key").unwrap().to_string())
	} else {
		None
	};
	if api_key.is_none() {
		return error_handler(vec![ErrorCodes::Base401]);
	}
	let token = api_key.unwrap();

	let client = app_state.client.clone();

	let token_fetch = get_db::<APITokens>(&client, "tokens", Some("API")).find_one(doc! { "token": token }).await;
	if token_fetch.is_err() {
		return error_handler(vec![ErrorCodes::Base401]);
	}
	let token_fetch = token_fetch.unwrap();
	if token_fetch.is_none() {
		return error_handler(vec![ErrorCodes::Base401]);
	}
	let token_fetch = token_fetch.unwrap();
	if !token_fetch.admin {
		return error_handler(vec![ErrorCodes::Base403]);
	}
	if path.type_.is_empty() {
		return error_handler(vec![ErrorCodes::Base400]);
	}
	if path.type_ == "update_premier_leaderboard" {
		tokio::spawn(update_premier_leaderboard_wrapper(client));
		return Response::builder()
			.status(200)
			.header("Content-Type", "application/json")
			.body(Body::from(serde_json::to_string(&json!({"status": "success"})).unwrap()))
			.unwrap();
	}
	if path.type_ == "update_website" {
		tokio::spawn(load_website_wrapper(client));
		return Response::builder()
			.status(200)
			.header("Content-Type", "application/json")
			.body(Body::from(serde_json::to_string(&json!({"status": "success"})).unwrap()))
			.unwrap();
	}
	if path.type_ == "update_player_leaderboard" {
		let c_season = get_c_season().await;
		let season = if let Some(season) = query.get("season") { season.to_string() } else { c_season.to_string() };
		let check_season = get_season_by_id(&season).await;
		if check_season.is_none() {
			return error_handler(vec![ErrorCodes::InvalidSeason]);
		}
		tokio::spawn(update_player_leaderboard_wrapper(client, season.clone()));
		return Response::builder()
			.status(200)
			.header("Content-Type", "application/json")
			.body(Body::from(serde_json::to_string(&json!({"status": "success"})).unwrap()))
			.unwrap();
	}
	Response::builder()
		.status(200)
		.header("Content-Type", "application/json")
		.body(Body::from(serde_json::to_string(&json!({"status": "unknown_job"})).unwrap()))
		.unwrap()
}
