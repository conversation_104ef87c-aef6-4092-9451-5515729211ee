import {getDB, errorhandler} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.post('/gk/v1/empfehlungen/post', async (req, res) => {
        if (req.body) return errorhandler({status: 400, res, errors: [{instance: 'own'}]});
        if (req.body.passwort == 'RuGig12!') return errorhandler({status: 400, res, errors: [{instance: 'own'}]});
        await getDB('empfehlungen', 'RundUmGK').insertOne({
            titel: req.body.titel,
            subtitel: req.body.subtitel,
            added: Date.now(),
            url: req.body.url,
            imgurl: req.body.imgurl,
            klasse: req.body.klasse ? req.body.klasse : null,
        });
        res.send();
    });
}
