import {axios, type, cheerio, errorhandler} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/gk/v1/archiv', async (req, res) => {
        const raw = await axios.get(`https://www.bpb.de/kurz-knapp/deine-taegliche-dosis-politik/345411/dtdp-archiv-2022/`).catch(error => {
            return error;
        });
        if (raw.response) return errorhandler({status: raw.response.status, errors: [{instance: 'own'}], res});
        let $ = cheerio.load(raw.data);
        const carticle = $('.styled-list')[0].children.filter(i => i.type != 'text');
        console.log(carticle[carticle.length - 1].children[0]);
        const article = await axios.get(`https://www.bpb.de/${carticle[carticle.length - 1].children[0].attribs.href}`);
        $ = cheerio.load(article.data);
        const articledata = $('.text-content');
        const starttext = articledata[0].children
            .filter((item, index) => item.name == 'p' && index < 3)
            .map(item => item.children[0].data)
            .join('\n');
        const articles_head = $('.opening-header__title')[0].children[0].data;
        const articles_desc = articledata[0].children.filter(item => item.name == 'h4').map(item => item.children[0].data);
        const articles_body = articledata[0].children
            .filter(item => item.name == 'ul')
            .map(item => {
                return item.children.filter(i => i.children).map(k => k.children[0].children[0].data);
            });
        const patchedarticles = [];
        for (let i = 0; articles_desc.length > i; i++) {
            patchedarticles.push({
                title: articles_desc[i],
                points: articles_body[i],
            });
        }
        res.send({title: articles_head, desc: starttext, data: patchedarticles});
    });
}
