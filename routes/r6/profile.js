import {axios, errorhandler} from '../../server.js';
const platformarray = ['pc', 'xbox', 'ps4'];
export default async function (fastify, opts, done) {
    fastify.get('/r6/v1/profile/:platform/:name', async (req, res) => {
        const name = req.params.name;
        const platform = req.params.platform.toLowerCase();
        if (!platformarray.includes(platform)) return res.code(400).send({status: '400', message: 'This platform is not available', available_platforms: platformarray});
        const fetchuser = await axios.get(`https://r6stats.com/api/player-search/${encodeURI(name)}/${platform}`).catch(error => {
            return error;
        });
        if (fetchuser.status != 200) return errorhandler({status: fetchuser.response.status, errors: [{instance: 'r6'}], res});
        const filteruser = fetchuser.data.data.filter(item => item.username == name);
        if (!filteruser.length) return errorhandler({status: 404, errors: [{instance: 'r6'}], res});
        const req1 = axios.get(`https://r6stats.com/api/stats/${filteruser[0].ubisoft_id}`);
        const req2 = axios.get(`https://r6stats.com/api/stats/${filteruser[0].ubisoft_id}/seasonal`);
        const fetch_data = await axios.all([req1, req2]).catch(error => {
            return error;
        });
        if (fetch_data[0].response && fetch_data[1].response) return errorhandler({status: fetch_data[1].response.status, errors: [{instance: 'r6'}], res});
        const h_k_ratio = (fetch_data[0].data.data.stats[0].general.headshots / fetch_data[0].data.data.stats[0].general.kills) * 100;
        res.code(200).send({
            status: 200,
            data: {
                metadata: {
                    user: fetch_data[0].data.data.username,
                    avatar_url: fetch_data[0].data.data.avatar_url_256,
                    platform: fetch_data[0].data.data.platform,
                    ubisoft_id: fetch_data[0].data.data.ubisoft_id,
                    level: fetch_data[0].data.data.progression.level,
                    last_updated: fetch_data[0].data.data.last_updated,
                },
                stats_general: {
                    rank: {
                        mmr: fetch_data[1].data.data.seasons[0].regions.emea[0].mmr,
                        max_mmr: fetch_data[1].data.data.seasons[0].regions.emea[0].max_mmr,
                        last_mmr_change: fetch_data[1].data.data.seasons[0].regions.emea[0].last_match_mmr_change,
                    },
                    assists: fetch_data[0].data.data.stats[0].general.assists,
                    barricades_deployed: fetch_data[0].data.data.stats[0].general.barricades_deployed,
                    blind_kills: fetch_data[0].data.data.stats[0].general.blind_kills,
                    bullet_hits: fetch_data[0].data.data.stats[0].general.bullet_hits,
                    deaths: fetch_data[0].data.data.stats[0].general.deaths,
                    dead_but_not_out: fetch_data[0].data.data.stats[0].general.dbno,
                    gadgets_destroyed: fetch_data[0].data.data.stats[0].general.gadgets_destroyed,
                    played_games: fetch_data[0].data.data.stats[0].general.games_played,
                    headshots: fetch_data[0].data.data.stats[0].general.headshots,
                    headshot_kill_ratio: h_k_ratio.toFixed(2) + '%',
                    kd: fetch_data[0].data.data.stats[0].general.kd,
                    kills: fetch_data[0].data.data.stats[0].general.kills,
                    losses: fetch_data[0].data.data.stats[0].general.losses,
                    melee_kills: fetch_data[0].data.data.stats[0].general.melee_kills,
                    penetration_kills: fetch_data[0].data.data.stats[0].general.penetration_kills,
                    playtime_in_seconds: fetch_data[0].data.data.stats[0].general.playtime,
                    reinforcements: fetch_data[0].data.data.stats[0].general.reinforcements_deployed,
                    revives: fetch_data[0].data.data.stats[0].general.revives,
                    suicides: fetch_data[0].data.data.stats[0].general.suicides,
                    wins: fetch_data[0].data.data.stats[0].general.wins,
                    win_lose: fetch_data[0].data.data.stats[0].general.wl,
                },
                stats_by_queue: {
                    casual: {
                        deaths: fetch_data[0].data.data.stats[0].queue.casual.deaths,
                        played_games: fetch_data[0].data.data.stats[0].queue.casual.games_played,
                        kd: fetch_data[0].data.data.stats[0].queue.casual.kd,
                        kills: fetch_data[0].data.data.stats[0].queue.casual.kills,
                        losses: fetch_data[0].data.data.stats[0].queue.casual.losses,
                        playtime_in_seconds: fetch_data[0].data.data.stats[0].queue.casual.playtime,
                        wins: fetch_data[0].data.data.stats[0].queue.casual.wins,
                        win_lose: fetch_data[0].data.data.stats[0].queue.casual.wl,
                    },
                    ranked: {
                        deaths: fetch_data[0].data.data.stats[0].queue.ranked.deaths,
                        played_games: fetch_data[0].data.data.stats[0].queue.ranked.games_played,
                        kd: fetch_data[0].data.data.stats[0].queue.ranked.kd,
                        kills: fetch_data[0].data.data.stats[0].queue.ranked.kills,
                        losses: fetch_data[0].data.data.stats[0].queue.ranked.losses,
                        playtime_in_seconds: fetch_data[0].data.data.stats[0].queue.ranked.playtime,
                        wins: fetch_data[0].data.data.stats[0].queue.ranked.wins,
                        win_lose: fetch_data[0].data.data.stats[0].queue.ranked.wl,
                    },
                    others: {
                        deaths: fetch_data[0].data.data.stats[0].queue.other.deaths,
                        played_games: fetch_data[0].data.data.stats[0].queue.other.games_played,
                        kd: fetch_data[0].data.data.stats[0].queue.other.kd,
                        kills: fetch_data[0].data.data.stats[0].queue.other.kills,
                        losses: fetch_data[0].data.data.stats[0].queue.other.losses,
                        playtime_in_seconds: fetch_data[0].data.data.stats[0].queue.other.playtime,
                        wins: fetch_data[0].data.data.stats[0].queue.other.wins,
                        win_lose: fetch_data[0].data.data.stats[0].queue.other.wl,
                    },
                },
            },
        });
    });
}
