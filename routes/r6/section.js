import {axios, errorhandler} from '../../server.js';
const platformarray = ['pc', 'xbox', 'ps4'];
const sectionarray = ['operator', 'season'];
export default async function (fastify, opts, done) {
    fastify.get('/r6/v1/profile/:platform/:name/:section', async (req, res) => {
        const name = req.params.name;
        const platform = req.params.platform.toLowerCase();
        const section = req.params.section;
        if (!sectionarray.includes(section)) return res.code(400).send({status: '400', message: 'This platform is not available', available_sections: sectionarray});
        if (!platformarray.includes(platform)) return res.code(400).send({status: '400', message: 'This platform is not available', available_platforms: platformarray});
        const fetchuser = await axios.get(`https://r6stats.com/api/player-search/${name}/${platform}`).catch(error => {
            return error;
        });
        if (fetchuser.status != 200) return errorhandler({status: fetchuser.response.status, errors: [{instance: 'r6'}], res});
        const filteruser = fetchuser.data.data.filter(item => item.username == name);
        if (!filteruser.length) return errorhandler({status: 404, errors: [{instance: 'r6'}], res});
        const req1 = axios.get(`https://r6stats.com/api/stats/${filteruser[0].ubisoft_id}`);
        const req2 = axios.get(`https://r6stats.com/api/stats/${filteruser[0].ubisoft_id}/seasonal`);
        const fetch_data = await axios.all([req1, req2]).catch(error => {
            return error;
        });
        if (!fetch_data[0].status && !fetch_data[1].status) return errorhandler({status: fetch_data[1].response.status, errors: [{instance: 'r6'}], res});
        if (section == 'season') {
            const ranksavailable = {
                0: 'Unranked',
                1: 'Copper 5',
                2: 'Copper 4',
                3: 'Copper 3',
                4: 'Copper 2',
                5: 'Copper 1',
                6: 'Bronze 5',
                7: 'Bronze 4',
                8: 'Bronze 3',
                9: 'Bronze 2',
                10: 'Bronze 1',
                11: 'Silver 5',
                12: 'Silver 4',
                13: 'Silver 3',
                14: 'Silver 2',
                15: 'Silver 1',
                16: 'Gold 3',
                17: 'Gold 2',
                18: 'Gold 1',
                19: 'Platinum 3',
                20: 'Platinum 2',
                21: 'Platinum 1',
                22: 'Diamond',
                23: 'Champions',
            };
            const regions = {
                emea: 'Europe',
                ncsa: 'America',
                apac: 'Asia',
            };
            const seasons = [];
            for (let i = 0; fetch_data[1].data.data.seasons.length > i; i++) {
                function getSeason() {
                    const regions = [];
                    regions.push(fetch_data[1].data.data.seasons[i].regions.ncsa[0]);
                    regions.push(fetch_data[1].data.data.seasons[i].regions.emea[0]);
                    regions.push(fetch_data[1].data.data.seasons[i].regions.apac[0]);
                    const filtered = regions.filter(item => item.deaths != 0);
                    return filtered.length != 0 ? filtered[0].region : 'Not played';
                }
                const region = fetch_data[1].data.data.seasons[i].id > 17 ? 'Global' : getSeason();
                if (region == 'Global') {
                    seasons.push({
                        deaths: fetch_data[1].data.data.seasons[i].regions.emea[0].deaths,
                        kills: fetch_data[1].data.data.seasons[i].regions.emea[0].kills,
                        losses: fetch_data[1].data.data.seasons[i].regions.emea[0].losses,
                        mmr: {
                            mmr: fetch_data[1].data.data.seasons[i].regions.emea[0].mmr,
                            rank: ranksavailable[fetch_data[1].data.data.seasons[i].regions.emea[0].rank],
                            max_mmr: fetch_data[1].data.data.seasons[i].regions.emea[0].max_mmr,
                            max_rank: ranksavailable[fetch_data[1].data.data.seasons[i].regions.emea[0].max_rank],
                            last_mmr_change: fetch_data[1].data.data.seasons[i].regions.emea[0].last_match_mmr_change,
                        },
                        region: 'Global',
                        season_name: fetch_data[1].data.data.seasons[i].name,
                        wins: fetch_data[1].data.data.seasons[i].regions.emea[0].wins,
                    });
                } else if (region != 'Not played') {
                    const json = {
                        deaths: fetch_data[1].data.data.seasons[i].regions[region][0].deaths,
                        kills: fetch_data[1].data.data.seasons[i].regions[region][0].kills,
                        losses: fetch_data[1].data.data.seasons[i].regions[region][0].losses,
                        mmr: {
                            mmr: fetch_data[1].data.data.seasons[i].regions[region][0].mmr,
                            rank: ranksavailable[fetch_data[1].data.data.seasons[i].regions[region][0].rank],
                            max_mmr: fetch_data[1].data.data.seasons[i].regions[region][0].max_mmr,
                            max_rank: ranksavailable[fetch_data[1].data.data.seasons[i].regions[region][0].max_rank],
                            last_mmr_change: fetch_data[1].data.data.seasons[i].regions[region][0].last_match_mmr_change,
                        },
                        region: regions[region],
                        season_name: fetch_data[1].data.data.seasons[i].name,
                        wins: fetch_data[1].data.data.seasons[i].regions[region][0].wins,
                    };
                    seasons.push(json);
                }
            }
            return res.code(200).send({status: 200, data: seasons});
        }
        if (section == 'operator') {
            const rawsorted = fetch_data[0].data.data.operators.sort((item1, item2) => item2.kills - item1.kills);
            const rawarray = [];
            for (let i = 0; rawsorted.length > i; i++) {
                const h_k_ratio1 = (rawsorted[i].headshots / rawsorted[i].kills) * 100;
                const json1 = {
                    dead_but_not_out: rawsorted[i].dbnos,
                    deaths: rawsorted[i].deaths,
                    headshots: rawsorted[i].headshots,
                    headshot_kill_ratio: h_k_ratio1.toFixed(2) + '%',
                    kd: rawsorted[i].kd,
                    kills: rawsorted[i].kills,
                    losses: rawsorted[i].losses,
                    melee_kills: rawsorted[i].melee_kills,
                    operator: {
                        name: rawsorted[i].operator.name,
                        role: rawsorted[i].operator.role,
                        badge_url: rawsorted[i].operator.images.badge,
                    },
                    playtime_in_seconds: rawsorted[i].playtime,
                    wins: rawsorted[i].wins,
                    win_lose: rawsorted[i].wl,
                };
                rawarray.push(json1);
            }
            res.code(200).send({status: 200, data: rawarray});
        }
    });
}
