use axum::body::Body;
use axum::extract::{Path, Request};
use axum::response::Response;
use serde_json::json;
use reqwest::{Client as HTTPClient};
use crate::{error_handler, ErrorCodes};
use crate::structs::errors::get_base_error;
use crate::structs::paths::{McDachQuery};

pub async fn valo_dach_whitelist_add(Path(params): Path<McDachQuery>, req: Request) -> Response {
    let auth = req.headers().get("Authorization");
    if auth.is_none() {
        return error_handler(vec![get_base_error(403)]);
    }
    if auth.unwrap() != "HDEV-93f86e37-984e-4adf-9d92-30a3aa6c28e4" {
        return error_handler(vec![get_base_error(403)]);
    }
    if params.name.is_none() {
        return error_handler(vec![ErrorCodes::Base400]);
    } else {
        let name = params.name.clone().unwrap();
        let url = if !cfg!(debug_assertions) {
            format!("http://localhost:5000/whitelist?name={}", name)
        } else {
            format!("http://***************:5000/whitelist?name={}", name)
        };
        let http_client: HTTPClient = HTTPClient::new();
        let response = http_client.post(url).send().await;
        if response.is_err() {
            return error_handler(vec![ErrorCodes::InternalError]);
        }
        let response = response.unwrap();
        let response_string = response.text().await.unwrap();
        Response::builder()
            .status(200)
            .header("Content-Type", "application/json")
            .body(Body::from(serde_json::to_string(&json!({"message": response_string})).unwrap())).unwrap()
    }
}

pub async fn valo_dach_whitelist_remove(Path(params): Path<McDachQuery>, req: Request) -> Response {
    let auth = req.headers().get("Authorization");
    if auth.is_none() {
        return error_handler(vec![get_base_error(403)]);
    }
    if auth.unwrap() != "HDEV-93f86e37-984e-4adf-9d92-30a3aa6c28e4" {
        return error_handler(vec![get_base_error(403)]);
    }
    if params.name.is_none() {
        return error_handler(vec![ErrorCodes::Base400]);
    } else {
        let name = params.name.clone().unwrap();
        let url = if !cfg!(debug_assertions) {
            format!("http://localhost:5000/whitelist?name={}", name)
        } else {
            format!("http://***************:5000/whitelist?name={}", name)
        };
        let http_client: HTTPClient = HTTPClient::new();
        let response = http_client.delete(url).send().await;
        if response.is_err() {
            return error_handler(vec![ErrorCodes::InternalError]);
        }
        let response = response.unwrap();
        let response_string = response.text().await.unwrap();
        Response::builder()
            .status(200)
            .header("Content-Type", "application/json")
            .body(Body::from(serde_json::to_string(&json!({"message": response_string})).unwrap())).unwrap()
    }
}

pub async fn valo_dach_whitelist_get(req: Request) -> Response {
    let auth = req.headers().get("Authorization");
    if auth.is_none() {
        return error_handler(vec![get_base_error(403)]);
    }
    if auth.unwrap() != "HDEV-93f86e37-984e-4adf-9d92-30a3aa6c28e4" {
        return error_handler(vec![get_base_error(403)]);
    }
    let url = if !cfg!(debug_assertions) {
        "http://localhost:5000/whitelist".to_string()
    } else {
        "http://***************:5000/whitelist".to_string()
    };
    let http_client: HTTPClient = HTTPClient::new();
    let response = http_client.get(url).send().await;
    if response.is_err() {
        return error_handler(vec![ErrorCodes::InternalError]);
    }
    let response = response.unwrap();
    let response_string = response.text().await.unwrap();
    Response::builder()
        .status(200)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&json!({"message": response_string})).unwrap())).unwrap()
}