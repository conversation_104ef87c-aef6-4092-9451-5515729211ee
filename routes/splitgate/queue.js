import {axios, errorhandler, mongodb} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/splitgate/v1/queue-time', async (req, res) => {
        return errorhandler({status: 410, type: 'util', res: res});
        res.header('X-Handled-Server', 'API-Server 1');
        const parse = {
            Mins: 'MINUTES',
            minutes: 'MINUTES',
            min: 'MINUTES',
        };
        const fetch = await mongodb.db('API').collection('splitgate-queue').findOne();
        if (fetch == undefined) {
            const discordfetch = await axios
                .get('https://discord.com/api/v8/guilds/327997395650740225/channels', {
                    'X-Debug-Options': 'bugReporterEnabled',
                    headers: {
                        'Alt-Used': 'discord.com',
                        Cookie: '__dcfduid=2b9b97adcb841c1f96d62817c61780c2; OptanonConsent=isIABGlobal=false&datestamp=Sun+Aug+15+2021+17%3A46%3A08+GMT%2B0200+(Mitteleurop%C3%A4ische+Sommerzeit)&version=6.17.0&hosts=&landingPath=https%3A%2F%2Fdiscord.com%2F&groups=C0001%3A1%2CC0002%3A0%2CC0003%3A0; _fbp=fb.1.1626464188817.932109026; __sdcfduid=1557be90f83121eb8dbbb3532d43f00788705c959cca6e44993a62b294bcfac354fe9ef75a5522ab814baed89e270ec1; locale=de',
                        Authorization: 'NzA1NzM4NTgyODk5Mjk0MjE5.YRk3TA.EMsMlXx6Z8ZmWLPTHDBD9vvcDuo',
                        Host: 'discord.com',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
                        'X-Super-Properties':
                            'eyJvcyI6IldpbmRvd3MiLCJicm93c2VyIjoiRmlyZWZveCIsImRldmljZSI6IiIsInN5c3RlbV9sb2NhbGUiOiJkZSIsImJyb3dzZXJfdXNlcl9hZ2VudCI6Ik1vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQ7IHJ2OjkwLjApIEdlY2tvLzIwMTAwMTAxIEZpcmVmb3gvOTAuMCIsImJyb3dzZXJfdmVyc2lvbiI6IjkwLjAiLCJvc192ZXJzaW9uIjoiMTAiLCJyZWZlcnJlciI6IiIsInJlZmVycmluZ19kb21haW4iOiIiLCJyZWZlcnJlcl9jdXJyZW50IjoiIiwicmVmZXJyaW5nX2RvbWFpbl9jdXJyZW50IjoiIiwicmVsZWFzZV9jaGFubmVsIjoic3RhYmxlIiwiY2xpZW50X2J1aWxkX251bWJlciI6OTM1NTQsImNsaWVudF9ldmVudF9zb3VyY2UiOm51bGx9',
                        Connection: 'keep-alive',
                    },
                })
                .catch(error => {
                    return error;
                });
            if (discordfetch.response) return errorhandler({status: discordfetch.response.status, res: res, type: 'own'});
            const channeldata = discordfetch.data.find(item => item.id == '870829581890887751').name.split('');
            var index;
            var type_index;
            for (i of channeldata) {
                if (!isNaN(Number(i)) && i != '-' && i != ' ') {
                    const lindex = channeldata.indexOf(String(i));
                    index = Number(i);
                    type_index = channeldata.splice(lindex + String(i).length, channeldata.length - 1).join('');
                }
            }
            await mongodb
                .db('API')
                .collection('splitgate-queue')
                .insertOne({
                    createdAt: new Date(),
                    length: index,
                    type: parse[type_index] != undefined ? parse[type_index] : null,
                    status: parse[type_index] != undefined ? 'Online' : 'Offline',
                    casual: discordfetch.data.find(item => item.id == '870829860518526996').name.startsWith('🔴') ? 'Offline' : 'Online',
                    ranked: discordfetch.data.find(item => item.id == '870830109366550560').name.startsWith('🔴') ? 'Offline' : 'Online',
                    custom: discordfetch.data.find(item => item.id == '877823025314148362').name.startsWith('🔴') ? 'Offline' : 'Online',
                });
            return res.code(200).send({
                status: 200,
                data: {
                    length: index,
                    type: parse[type_index] != undefined ? parse[type_index] : null,
                    status: parse[type_index] != undefined ? 'Online' : 'Offline',
                    casual: discordfetch.data.find(item => item.id == '870829860518526996').name.startsWith('🔴') ? 'Offline' : 'Online',
                    ranked: discordfetch.data.find(item => item.id == '870830109366550560').name.startsWith('🔴') ? 'Offline' : 'Online',
                    custom: discordfetch.data.find(item => item.id == '877823025314148362').name.startsWith('🔴') ? 'Offline' : 'Online',
                },
            });
        }
        return res.code(200).send({
            status: 200,
            data: {length: Number(fetch.length), type: fetch.type, status: fetch.status, casual: fetch.casual, ranked: fetch.ranked, custom: fetch.custom},
        });
    });
}
