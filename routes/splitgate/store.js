import {axios, errorhandler} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/splitgate/v1/store', async (req, res) => {
        return errorhandler({status: 410, type: 'util', res: res});
        const rarity = {
            Legendary: 'https://cdn.henrikdev.xyz/splitgate/images/legendary-icon.png',
            Epic: 'https://cdn.henrikdev.xyz/splitgate/images/epic-icon.png',
            Rare: 'https://cdn.henrikdev.xyz/splitgate/images/rare-icon.png',
            Common: 'https://cdn.henrikdev.xyz/splitgate/images/common-icon.png',
        };
        const fetch = await axios
            .get('https://api.splitgate.com/game-client/get-store', {
                headers: {
                    Authorization:
                        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjp7ImlkIjoiNjExMDM3YzlkZTI2MDUxODliYjY0MzExIiwicGxhdGZvcm1JZCI6Ijc2NTYxMTk4MTkzODU3NzEwIiwicGxhdGZvcm0iOiJTVEVBTSIsInBsYXRmb3JtVGl0bGVJZCI6IjY3NzYyMCIsInBsYXRmb3JtRW52aXJvbm1lbnQiOiJVbmtub3duIiwicGxhdGZvcm1PcyI6IldJTkRPV1MifSwiaWF0IjoxNjI5MjgxNjUzLCJleHAiOjE2MzA0OTEyNTMsImF1ZCI6InNwbGl0Z2F0ZS5jb20iLCJpc3MiOiJzcGxpdGdhdGUuY29tIn0.Wrui38jbsKIrtMs1rA33bXLJoBUgAsBN5_SwkOKwAt0',
                },
            })
            .catch(error => {
                return error;
            });
        if (fetch.response) return errorhandler({status: fetch.response.status, res: res, type: 'own'});
        const v = fetch.data.batchedEvents.StoreUpdate.categories.find(item => item.title.en == 'Value Bundle');
        const f = fetch.data.batchedEvents.StoreUpdate.categories.find(item => item.title.en == 'Featured Items');
        const d = fetch.data.batchedEvents.StoreUpdate.categories.find(item => item.title.en == 'Daily Items');
        var vp = [];
        if (v != undefined) {
            for (bundles of v.sections) {
                for (bundle of bundles.items) {
                    var items = [];
                    for (item of bundle.items) {
                        var ov = await axios.get(`https://cdn.henrikdev.xyz/splitgate/${item.customization.customizationType.toLowerCase()}`).catch(error => {
                            return error;
                        });
                        ov = ov.response
                            ? null
                            : ov.data.data.find(item2 =>
                                  item.customization.customizationType.toLowerCase() != 'emote'
                                      ? item2.type == item.customization.customizationValue.split('-')[0]
                                      : item2.image.includes('Emote')
                              );
                        ov =
                            item.customization.customizationType.toLowerCase() != 'emote'
                                ? ov.skins.find(item2 => item2.image.includes(`${item.customization.customizationValue}_T`))
                                : ov;
                        items.push({
                            type: item.customization.customizationType,
                            image: `https://cdn.henrikdev.xyz/splitgate/images/${item.customization.customizationType}_${item.customization.customizationValue}_T.png`,
                            displayName: ov.displayName,
                            availability: ov.availability,
                            released: ov.released,
                            rarity: ov.rarity,
                        });
                    }
                    vp.push({
                        id: bundle.id,
                        title: bundle.title.en,
                        cost: bundle.currencyCost,
                        rarity: {type: bundle.rarity, image: rarity[bundle.rarity]},
                        bundle_image: bundle.imageUrl,
                        items: items,
                    });
                }
            }
        }
        var fp = [];
        if (f != undefined) {
            for (bundles of f.sections) {
                for (item of bundles.items) {
                    var ov = await axios.get(`https://cdn.henrikdev.xyz/splitgate/${item.customization.customizationType.toLowerCase()}`).catch(error => {
                        return error;
                    });
                    ov = ov.response
                        ? null
                        : ov.data.data.find(item2 =>
                              item.customization.customizationType.toLowerCase() != 'emote'
                                  ? item2.type == item.customization.customizationValue.split('-')[0]
                                  : item2.image.includes('Emote')
                          );
                    ov =
                        item.customization.customizationType.toLowerCase() != 'emote'
                            ? ov.skins.find(item2 => item2.image.includes(`${item.customization.customizationValue}_T`))
                            : ov;
                    fp.push({
                        type: item.customization.customizationType,
                        image: `https://cdn.henrikdev.xyz/splitgate/images/${item.customization.customizationType}_${item.customization.customizationValue}_T.png`,
                        displayName: ov.displayName,
                        availability: ov.availability,
                        released: ov.released,
                        rarity: ov.rarity,
                        cost: item.currencyCost,
                        starttime_ms: bundles.startTimeMs,
                        expirationtime_ms: bundles.expirationTimeMs,
                    });
                }
            }
        }
        var dp = [];
        if (d != undefined) {
            for (bundles of d.sections) {
                for (item of bundles.items) {
                    var ov = await axios.get(`https://cdn.henrikdev.xyz/splitgate/${item.customization.customizationType.toLowerCase()}`).catch(error => {
                        return error;
                    });
                    ov = ov.response
                        ? null
                        : ov.data.data.find(item2 =>
                              item.customization.customizationType.toLowerCase() != 'emote'
                                  ? item2.type == item.customization.customizationValue.split('-')[0]
                                  : item2.image.includes('Emote')
                          );
                    ov =
                        item.customization.customizationType.toLowerCase() != 'emote'
                            ? ov.skins.find(item2 => item2.image.includes(`${item.customization.customizationValue}_T`))
                            : ov;
                    console.log(ov);
                    dp.push({
                        type: item.customization.customizationType,
                        image: `https://cdn.henrikdev.xyz/splitgate/images/${item.customization.customizationType}_${item.customization.customizationValue}_T.png`,
                        displayName: ov.displayName,
                        availability: ov.availability,
                        released: ov.released,
                        rarity: ov.rarity,
                        cost: item.currencyCost,
                        starttime_ms: bundles.startTimeMs,
                        expirationtime_ms: bundles.expirationTimeMs,
                    });
                }
            }
        }
        res.code(200).send({
            status: 200,
            data: {
                value_bundles: {
                    translations: v != undefined ? v.title : null,
                    items: vp,
                },
                featured_items: {
                    translations: f != undefined ? f.title : null,
                    items: fp,
                },
                daily_items: {
                    translations: d != undefined ? d.title : null,
                    items: dp,
                },
            },
        });
    });
}
