import {makeRequest} from '../../server.js';
export default async function (fastify, opts, done) {
    fastify.get('/valorantlabs/v1/matches/:region/:puuid', async (req, res) => {
        const puuid = req.params.puuid;
        let region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        const matches = await makeRequest({
            url: `https://pd.${region}.a.pvp.net/match-history/v1/history/${puuid}?startIndex=0&endIndex=20`,
            region,
            res,
            instance: 'riot',
        });
        const history_array = [];
        for (const match of matches.data.History) {
            history_array.push({matchId: match.MatchID, gameStartTimeMillis: match.GameStartTime});
        }
        return res.send({history: history_array});
    });
}
