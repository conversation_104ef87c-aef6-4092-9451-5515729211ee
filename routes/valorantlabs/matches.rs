use std::sync::Arc;
use axum::body::Body;
use axum::extract::{Path, State};
use axum::response::Response;
use serde_json::json;
use crate::AppState;
use crate::structs::errors::error_handler;
use crate::structs::helper::fetch_matches_by_puuid;
use crate::structs::paths::ValorantLabsMatchesPath;

pub async fn get_matches(
    Path(path): Path<ValorantLabsMatchesPath>,
    State(app_state): State<Arc<AppState>>,
) -> Response {
    let region = if ["br", "latam"]
        .iter()
        .any(|x| x == &path.affinity.to_lowercase())
    {
        String::from("na")
    } else {
        path.affinity.to_lowercase()
    };
    let conn = app_state.redis.clone();
    //let conn = redis.get().await.expect("Failed to get Redis connection from pool");
    let history = fetch_matches_by_puuid(conn, &path.puuid, &region, "", "PC").await;
    if history.is_err() {
        return error_handler(vec![history.unwrap_err()]);
    }
    let history = history.unwrap().data.matches.iter().map(|i| json!({"matchId": i.match_id.to_string(), "gameStartTimeMillis": i.match_start_time.timestamp_millis()})).collect::<Vec<_>>();
    Response::builder()
        .status(200)
        .header("Content-Type", "application/json")
        .body(Body::from(serde_json::to_string(&json!({"history": history})).unwrap())).unwrap()
}