import {errorhandler, moment, saveMatchFile, makeRequest, existsMatchFile, getMatchFile} from '../../server.js';
import {ValorantMatchv2} from '../../methods/matchid.js';
export default async function (fastify, opts, done) {
    /*fastify.get('/valorantlabs/v1/match/:region/:id', async (req, res) => {
        const gameid = req.params.id;
        let region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        const version = 'v2';
        moment.locale('en');
        async function fetchmatch(matchid) {
            const requests = await makeRequest({
                url: `https://pd.${region}.a.pvp.net/match-details/v1/matches/${matchid}`,
                res,
                region,
                return_error: true,
            });
            if (requests.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (requests.response) return {data: 404};
            requests.data.region = region;
            return {region: region, data: requests.data};
        }

        function generatePlayerRoundObject(data) {
            //data.players / data.puuid
            const playerdata = data.player.find(item => item.subject == data.puuid);
            return {
                puuid: playerdata.subject,
                display_name: `${playerdata.gameName}#${playerdata.tagLine}`,
                team: playerdata.teamId,
            };
        }
        function generateLocation(data) {
            const callback = [];
            for (let i = 0; data.locations.length > i; i++) {
                const cplayer = data.player.find(item => item.subject == data.locations[i].subject);
                callback.push({
                    player_puuid: cplayer.subject,
                    player_display_name: `${cplayer.gameName}#${cplayer.tagLine}`,
                    player_team: cplayer.teamId,
                    location: {
                        x: data.locations[i].location.x,
                        y: data.locations[i].location.y,
                    },
                    view_radians: data.locations[i].viewRadians,
                });
            }
            return callback;
        }
        function generateKills(data) {
            if (!data.kills) return;
            const weapons = getWeapons();
            if (!data.kills) console.log(match.data);
            for (let k = 0; data.kills.length > k; k++) {
                const victim = data.kills[k].victim != '' ? generatePlayerRoundObject({puuid: data.kills[k].victim, player: data.player}) : null;
                const killer = data.kills[k].killer != '' ? generatePlayerRoundObject({puuid: data.kills[k].killer, player: data.player}) : null;
                const assistants = [];
                for (let p = 0; data.kills[k].assistants.length > p; p++) {
                    const assistant = generatePlayerRoundObject({puuid: data.kills[k].assistants[p], player: data.player});
                    assistants.push({
                        assistant_puuid: assistant.puuid,
                        assistant_display_name: assistant.display_name,
                        assistant_team: assistant.team,
                    });
                }
                const locations = generateLocation({player: data.player, locations: data.kills[k].playerLocations});
                kills.push({
                    kill_time_in_round: data.kills[k].roundTime,
                    kill_time_in_match: data.kills[k].gameTime,
                    round: data.kills[k].round,
                    killer_puuid: killer ? killer.puuid : null,
                    killer_display_name: killer ? killer.display_name : null,
                    killer_team: killer ? killer.team : null,
                    victim_puuid: victim ? victim.puuid : null,
                    victim_display_name: victim ? victim.display_name : null,
                    victim_team: victim ? victim.team : null,
                    victim_death_location: {
                        x: data.kills[k].victimLocation.x,
                        y: data.kills[k].victimLocation.y,
                    },
                    damage_weapon_id: data.kills[k].finishingDamage.damageItem,
                    damage_weapon_name:
                        data.kills[k].finishingDamage.damageType == 'Weapon'
                            ? weapons.find(item => item.uuid == data.kills[k].finishingDamage.damageItem.toLowerCase())?.displayName
                            : null,
                    damage_weapon_assets: {
                        display_icon:
                            data.kills[k].finishingDamage.damageType == 'Weapon'
                                ? weapons.find(item => item.uuid == data.kills[k].finishingDamage.damageItem.toLowerCase())?.displayIcon
                                : null,
                        killfeed_icon:
                            data.kills[k].finishingDamage.damageType == 'Weapon'
                                ? weapons.find(item => item.uuid == data.kills[k].finishingDamage.damageItem.toLowerCase())?.killStreamIcon
                                : null,
                    },
                    secondary_fire_mode: data.kills[k].finishingDamage.isSecondaryFireMode,
                    player_locations_on_kill: locations,
                    assistants: assistants,
                });
            }
        }
        function generatePlayerStats(data) {
            const stats = [];
            const weapons = getWeapons();
            const gear = getGear();
            for (let i = 0; data.stats.length > i; i++) {
                let damage = 0;
                let bodyshots = 0;
                let headshots = 0;
                let legshots = 0;
                const damage_stats = [];
                const kills_stats = [];
                for (let k = 0; data.stats[i].damage.length > k; k++) {
                    const cplayer = generatePlayerRoundObject({puuid: data.stats[i].damage[k].receiver, player: data.player});
                    damage_stats.push({
                        receiver_puuid: data.stats[i].damage[k].receiver,
                        receiver_display_name: cplayer.display_name,
                        receiver_team: cplayer.team,
                        bodyshots: data.stats[i].damage[k].bodyshots,
                        damage: data.stats[i].damage[k].damage,
                        headshots: data.stats[i].damage[k].headshots,
                        legshots: data.stats[i].damage[k].legshots,
                    });
                    bodyshots += data.stats[i].damage[k].bodyshots;
                    headshots += data.stats[i].damage[k].headshots;
                    legshots += data.stats[i].damage[k].legshots;
                    if (data.stats[i].damage[k].damage != 999) damage += data.stats[i].damage[k].damage;
                    if (data.stats[i].damage[k].damage != 999) player_round_stats[cplayer.puuid].damage_received += data.stats[i].damage[k].damage;
                }
                for (let k = 0; data.stats[i].kills.length > k; k++) {
                    const victim = generatePlayerRoundObject({puuid: data.stats[i].kills[k].victim, player: data.player});
                    const killer = generatePlayerRoundObject({puuid: data.stats[i].kills[k].killer, player: data.player});
                    const assistants = [];
                    for (let p = 0; data.stats[i].kills[k].assistants.length > p; p++) {
                        const assistant = generatePlayerRoundObject({puuid: data.stats[i].kills[k].assistants[p], player: data.player});
                        assistants.push({
                            assistant_puuid: assistant.puuid,
                            assistant_display_name: assistant.display_name,
                            assistant_team: assistant.team,
                        });
                    }
                    const locations = generateLocation({player: data.player, locations: data.stats[i].kills[k].playerLocations});
                    kills_stats.push({
                        kill_time_in_round: data.stats[i].kills[k].roundTime,
                        kill_time_in_match: data.stats[i].kills[k].gameTime,
                        killer_puuid: killer.puuid,
                        killer_display_name: killer.display_name,
                        killer_team: killer.team,
                        victim_puuid: victim.puuid,
                        victim_display_name: victim.display_name,
                        victim_team: victim.team,
                        victim_death_location: {
                            x: data.stats[i].kills[k].victimLocation.x,
                            y: data.stats[i].kills[k].victimLocation.y,
                        },
                        damage_weapon_id: data.stats[i].kills[k].finishingDamage.damageItem,
                        damage_weapon_name:
                            data.stats[i].kills[k].finishingDamage.damageType == 'Weapon'
                                ? weapons.find(item => item.uuid == data.stats[i].kills[k].finishingDamage.damageItem.toLowerCase())?.displayName
                                : null,
                        damage_weapon_assets: {
                            display_icon:
                                data.stats[i].kills[k].finishingDamage.damageType == 'Weapon'
                                    ? weapons.find(item => item.uuid == data.stats[i].kills[k].finishingDamage.damageItem.toLowerCase())?.displayIcon
                                    : null,
                            killfeed_icon:
                                data.stats[i].kills[k].finishingDamage.damageType == 'Weapon'
                                    ? weapons.find(item => item.uuid == data.stats[i].kills[k].finishingDamage.damageItem.toLowerCase())?.killStreamIcon
                                    : null,
                        },
                        secondary_fire_mode: data.stats[i].kills[k].finishingDamage.isSecondaryFireMode,
                        player_locations_on_kill: locations,
                        assistants: assistants,
                    });
                }
                const cplayer = generatePlayerRoundObject({puuid: data.stats[i].subject, player: data.player});
                stats.push({
                    ability_casts: {
                        c_casts: data.stats[i].ability.grenadeEffects,
                        q_casts: data.stats[i].ability.ability1Effects,
                        e_cast: data.stats[i].ability.ability2Effects,
                        x_cast: data.stats[i].ability.ultimateEffects,
                    },
                    player_puuid: cplayer.puuid,
                    player_display_name: cplayer.display_name,
                    player_team: cplayer.team,
                    damage_events: damage_stats,
                    damage: damage,
                    bodyshots: bodyshots,
                    headshots: headshots,
                    legshots: legshots,
                    kill_events: kills_stats,
                    kills: kills_stats.length,
                    score: data.stats[i].score,
                    economy: {
                        loadout_value: data.stats[i].economy.loadoutValue,
                        weapon: {
                            id: data.stats[i].economy.weapon.length ? data.stats[i].economy.weapon : null,
                            name: data.stats[i].economy.weapon.length ? weapons.find(item => item.uuid == data.stats[i].economy.weapon.toLowerCase()).displayName : null,
                            assets: {
                                display_icon: data.stats[i].economy.weapon.length
                                    ? weapons.find(item => item.uuid == data.stats[i].economy.weapon.toLowerCase()).displayIcon
                                    : null,
                                killfeed_icon: data.stats[i].economy.weapon.length
                                    ? weapons.find(item => item.uuid == data.stats[i].economy.weapon.toLowerCase()).killStreamIcon
                                    : null,
                            },
                        },
                        armor: {
                            id: data.stats[i].economy.armor.length ? data.stats[i].economy.armor : null,
                            name: data.stats[i].economy.armor.length ? gear.find(item => item.uuid == data.stats[i].economy.armor.toLowerCase()).displayName : null,
                            assets: {
                                display_icon: data.stats[i].economy.armor.length
                                    ? gear.find(item => item.uuid == data.stats[i].economy.armor.toLowerCase()).displayIcon
                                    : null,
                            },
                        },
                        remaining: data.stats[i].economy.remaining,
                        spent: data.stats[i].economy.spent,
                    },
                    was_afk: data.stats[i].wasAfk,
                    was_penalized: data.stats[i].wasPenalized,
                    stayed_in_spawn: data.stats[i].stayedInSpawn,
                });
                player_round_stats[cplayer.puuid].damage_made += damage;
                player_round_stats[cplayer.puuid].bodyshots += bodyshots;
                player_round_stats[cplayer.puuid].headshots += headshots;
                player_round_stats[cplayer.puuid].legshots += legshots;
                player_round_stats[cplayer.puuid].economy.spent += data.stats[i].economy.spent;
                player_round_stats[cplayer.puuid].economy.value += data.stats[i].economy.loadoutValue;
            }
            return stats;
        }
        async function createPlayerObject(data) {
            const check = await getDB('puuids').findOne({puuid: data.player.subject});
            if (check == undefined || check.last_match == undefined || data.metadata.gameStartMillis > check.last_match)
                getDB('puuids').updateOne(
                    {puuid: data.player.subject},
                    {
                        $set: {
                            account_level: data.player.accountLevel,
                            name: data.player.gameName,
                            tag: data.player.tagLine,
                            card: data.player.playerCard,
                            last_update: moment().unix(),
                            last_match: data.metadata.gameStartMillis,
                            region: match.region,
                        },
                    },
                    {upsert: true}
                );
            players.push({
                puuid: data.player.subject,
                name: data.player.gameName,
                tag: data.player.tagLine,
                team: data.player.teamId,
                level: data.player.accountLevel,
                character: getAgents().find(item => item.uuid == data.player.characterId).displayName,
                currenttier: data.player.competitiveTier,
                currenttier_patched: tiers[data.player.competitiveTier],
                player_card: data.player.playerCard,
                player_title: data.player.playerTitle,
                party_id: data.player.partyId,
                session_playtime: {
                    minutes: data.player.sessionPlaytimeMinutes,
                    seconds: data.player.sessionPlaytimeMinutes * 60,
                    milliseconds: data.player.sessionPlaytimeMinutes * 60 * 1000,
                },
                behavior: {
                    afk_rounds: data.player.behaviorFactors.afkRounds,
                    friendly_fire: {
                        incoming: data.player.behaviorFactors.friendlyFireIncoming != null ? data.player.behaviorFactors.friendlyFireIncoming : null,
                        outgoing: data.player.behaviorFactors.friendlyFireOutgoing != null ? data.player.behaviorFactors.friendlyFireOutgoing : null,
                    },
                    rounds_in_spawn: data.player.behaviorFactors.stayedInSpawnRounds != null ? data.player.behaviorFactors.stayedInSpawnRounds : null,
                },
                platform: {
                    type: data.player.platformInfo.platformType,
                    os: {
                        name: data.player.platformInfo.platformOS,
                        version: data.player.platformInfo.platformOSVersion,
                    },
                },
                ability_casts: {
                    c_cast:
                        (data.metadata.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            data.metadata.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            data.player.stats.abilityCasts == undefined) == true
                            ? null
                            : data.player.stats.abilityCasts.grenadeCasts,
                    q_cast:
                        (data.metadata.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            data.metadata.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            data.player.stats.abilityCasts == undefined) == true
                            ? null
                            : data.player.stats.abilityCasts.ability1Casts,
                    e_cast:
                        (data.metadata.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            data.metadata.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            data.player.stats.abilityCasts == undefined) == true
                            ? null
                            : data.player.stats.abilityCasts.ability2Casts,
                    x_cast:
                        (data.metadata.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            data.metadata.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            data.player.stats.abilityCasts == undefined) == true
                            ? null
                            : data.player.stats.abilityCasts.ultimateCasts,
                },
                assets: {
                    card: {
                        small: `https://media.valorant-api.com/playercards/${data.player.playerCard}/smallart.png`,
                        large: `https://media.valorant-api.com/playercards/${data.player.playerCard}/largeart.png`,
                        wide: `https://media.valorant-api.com/playercards/${data.player.playerCard}/wideart.png`,
                    },
                    agent: {
                        small: `https://media.valorant-api.com/agents/${data.player.characterId}/displayicon.png`,
                        bust: `https://media.valorant-api.com/agents/${data.player.characterId}/fullportrait.png`,
                        full: `https://media.valorant-api.com/agents/${data.player.characterId}/fullportrait.png`,
                        killfeed: `https://media.valorant-api.com/agents/${data.player.characterId}/killfeedportrait.png`,
                    },
                },
                stats: {
                    score: data.player.stats.score,
                    kills: data.player.stats.kills,
                    deaths: data.player.stats.deaths,
                    assists: data.player.stats.assists,
                    bodyshots: !isNaN(player_round_stats[data.player.subject].bodyshots) ? player_round_stats[data.player.subject].bodyshots : 0,
                    headshots: !isNaN(player_round_stats[data.player.subject].headshots) ? player_round_stats[data.player.subject].headshots : 0,
                    legshots: !isNaN(player_round_stats[data.player.subject].legshots) ? player_round_stats[data.player.subject].legshots : 0,
                },
                economy: {
                    spent: {
                        overall: player_round_stats[data.player.subject].economy.spent,
                        average: Number((player_round_stats[data.player.subject].economy.spent / data.rounds).toFixed(0)),
                    },
                    loadout_value: {
                        overall: player_round_stats[data.player.subject].economy.value,
                        average: Number((player_round_stats[data.player.subject].economy.value / data.rounds).toFixed(0)),
                    },
                },
                damage_made: player_round_stats[data.player.subject].damage_made,
                damage_received: player_round_stats[data.player.subject].damage_received,
            });
            return;
        }
        async function createRoundObject(data) {
            rounds.push({
                winning_team: data.round.winningTeam,
                end_type: data.round.roundResult,
                bomb_planted: data.round.bombPlanter ? true : false,
                bomb_defused: data.round.bombDefuser ? true : false,
                plant_events: {
                    plant_location: data.round.bombPlanter ? {x: data.round.plantLocation.x, y: data.round.plantLocation.y} : null,
                    planted_by: data.round.bombPlanter ? generatePlayerRoundObject({puuid: data.round.bombPlanter, player: data.players}) : null,
                    plant_site: data.round.bombPlanter ? data.round.plantSite : null,
                    plant_time_in_round: data.round.bombPlanter ? data.round.plantRoundTime : null,
                    player_locations_on_plant: data.round.bombPlanter ? generateLocation({player: data.players, locations: data.round.plantPlayerLocations}) : null,
                },
                defuse_events: {
                    defuse_location: data.round.bombDefuser ? {x: data.round.defuseLocation.x, y: data.round.defuseLocation.y} : null,
                    defused_by: data.round.bombDefuser ? generatePlayerRoundObject({puuid: data.round.bombDefuser, player: data.players}) : null,
                    defuse_time_in_round: data.round.bombDefuser ? data.round.defuseRoundTime : null,
                    player_locations_on_defuse: data.round.bombDefuser ? generateLocation({player: data.players, locations: data.round.defusePlayerLocations}) : null,
                },
                player_stats: generatePlayerStats({player: data.players, stats: data.round.playerStats}),
            });
            return;
        }

        let match = {};
        const players = [];
        const rounds = [];
        const player_round_stats = {damage: []};
        const kills = [];
        switch (version) {
            case 'v2': {
                if (existsMatchFile(gameid)) {
                    match.data ||= getMatchFile(gameid);
                } else {
                    match = await fetchmatch(gameid);
                    if (res.sent) return;
                    if (typeof match.data == 'number') return res.code(404).send({status: 404, message: 'Unknown Match ID'});
                    await saveMatchFile(match.data, match.data.matchInfo.matchId);
                }
                const playing_players = match.data.players.filter(item => item.teamId != 'Neutral');
                playing_players.forEach(item => (item.mode = match.data.matchInfo.gameMode));
                playing_players.forEach(i => {
                    player_round_stats[i.subject] = {
                        economy: {spent: 0, value: 0},
                        damage_received: 0,
                        damage_made: 0,
                        bodyshots: 0,
                        headshots: 0,
                        legshots: 0,
                    };
                });
                const prms_player = [];
                const prms_round = [];
                generateKills({kills: match.data.kills, player: playing_players});
                for (let i = 0; match.data.roundResults.length > i; i++) {
                    prms_round.push(createRoundObject({round: match.data.roundResults[i], players: playing_players}));
                }
                for (let i = 0; playing_players.length > i; i++) {
                    prms_player.push(
                        createPlayerObject({players: playing_players, player: playing_players[i], metadata: match.data.matchInfo, rounds: match.data.roundResults.length})
                    );
                }
                await Promise.all(prms_player);
                await Promise.all(prms_round);
                const gamepods = getGamepods();
                const maps = getMaps();
                const rteam = match.data.teams.find(item => item.teamId == 'Red');
                const bteam = match.data.teams.find(item => item.teamId == 'Blue');
                await getDB('history').findOneAndUpdate(
                    {match_id: match.data.matchInfo.matchId},
                    {
                        $setOnInsert: {
                            meta: {
                                map: maps.find(item => item.mapUrl == match.data.matchInfo.mapId).uuid,
                                v: match.data.matchInfo.gameVersion,
                                mode: match.data.matchInfo.queueID,
                                g_s: match.data.matchInfo.gameStartMillis,
                                s: match.data.matchInfo.seasonId,
                                r: match.data.region ?? null,
                                c: gamepods[match.data.matchInfo.gamePodId] ?? null,
                            },
                            stats: players.map(i => {
                                return {
                                    id: i.puuid,
                                    team: i.team,
                                    l: i.level,
                                    c: getAgents().find(k => k.displayName == i.character).uuid ?? null,
                                    tier: i.currenttier,
                                    s: i.stats.score,
                                    k: i.stats.kills,
                                    d: i.stats.deaths,
                                    a: i.stats.assists,
                                    b: i.stats.bodyshots,
                                    h: i.stats.headshots,
                                    leg: i.stats.legshots,
                                    dr: i.damage_received,
                                    dm: i.damage_made,
                                };
                            }),
                            teams: {
                                r: rteam?.numPoints ?? null,
                                b: bteam?.numPoints ?? null,
                            },
                        },
                    },
                    {upsert: true}
                );
                res.code(200).send({
                    status: 200,
                    data: {
                        metadata: {
                            map: maps.find(item => item.mapUrl == match.data.matchInfo.mapId).displayName,
                            game_version: match.data.matchInfo.gameVersion,
                            game_length: Number((match.data.matchInfo.gameLengthMillis / 1000).toFixed(0)),
                            game_start: Number((match.data.matchInfo.gameStartMillis / 1000).toFixed(0)),
                            game_start_patched: moment(match.data.matchInfo.gameStartMillis).format('LLLL'),
                            rounds_played: match.data.teams[0].roundsPlayed,
                            mode: gamemodes.find(item => item.id == match.data.matchInfo.queueID).name,
                            queue: gamemodes.find(item => match.data.matchInfo.gameMode.includes(item.asset)).name,
                            season_id: match.data.matchInfo.seasonId,
                            platform: match.data.matchInfo.platformType,
                            matchid: match.data.matchInfo.matchId,
                            region: match.data.region ? match.data.region : null,
                            cluster: gamepods[match.data.matchInfo.gamePodId] != undefined ? gamepods[match.data.matchInfo.gamePodId] : 'Unknown',
                        },
                        players: {
                            all_players: players,
                            red: players.filter(item => item.team == 'Red'),
                            blue: players.filter(item => item.team == 'Blue'),
                        },
                        teams: {
                            red: {
                                has_won: rteam ? rteam.won : null,
                                rounds_won: rteam ? rteam.numPoints : null,
                                rounds_lost: bteam ? bteam.numPoints : null,
                            },
                            blue: {
                                has_won: bteam ? bteam.won : null,
                                rounds_won: bteam ? bteam.numPoints : null,
                                rounds_lost: rteam ? rteam.numPoints : null,
                            },
                        },
                        rounds: rounds,
                        kills: kills,
                    },
                });
                return;
            }
            default: {
                return errorhandler({status: 501, res: res, type: 'own'});
            }
        }
    });*/
    fastify.get('/valorantlabs/v1/match/:region/:id', async (req, res) => {
        const gameid = req.params.id;
        let region = ['br', 'latam'].some(i => i == req.params.region.toLowerCase()) ? 'na' : req.params.region.toLowerCase();
        const version = 'v2';
        moment.locale('en');
        async function fetchmatch(matchid) {
            const requests = await makeRequest({
                url: `https://pd.${region}.a.pvp.net/match-details/v1/matches/${matchid}`,
                res,
                region,
                return_error: true,
            });
            if (requests.code == 'ECONNABORTED') return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
            if (requests.response) return {data: 404};
            requests.data.region = region;
            return {region: region, data: requests.data};
        }
        switch (version) {
            case 'v2': {
                let match_data = {};
                if (existsMatchFile(gameid)) {
                    match_data.data ||= getMatchFile(gameid);
                } else {
                    match_data = await fetchmatch(gameid);
                    if (res.sent) return;
                    if (typeof match_data.data == 'number') return res.code(404).send({status: 404, message: 'Unknown Match ID'});
                    await saveMatchFile(match_data.data, match_data.data.matchInfo.matchId);
                }
                const match = new ValorantMatchv2(match_data.data);
                await match._init();
                await match.lifetime();
                return res.code(200).send({status: 200, data: match.toJSON()});
            }
            default: {
                return errorhandler({status: 501, res, errors: [{instance: 'own'}]});
            }
        }
    });
}
