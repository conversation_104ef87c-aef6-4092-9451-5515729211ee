use crate::structs::database::B2Match;
use crate::{ get_db, get_db_hdd, S3_CLIENT };
use futures::stream::StreamExt;
use mongodb::bson::{ doc, from_document, Document };
use mongodb::{ Client, Collection };
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::{ Mutex, Semaphore };
use valorant_api::response_types::matchdetails_v1::MatchDetailsV1;

#[derive(Debug, <PERSON>lone)]
pub struct MigrationStats {
	pub total_processed: u64,
	pub successful_migrations: u64,
	pub failed_migrations: u64,
	pub skipped_existing: u64,
	pub start_time: Instant,
}

impl MigrationStats {
	pub fn new() -> Self {
		Self {
			total_processed: 0,
			successful_migrations: 0,
			failed_migrations: 0,
			skipped_existing: 0,
			start_time: Instant::now(),
		}
	}

	pub fn print_progress(&self) {
		let elapsed = self.start_time.elapsed();
		let rate = if elapsed.as_secs() > 0 { (self.total_processed as f64) / (elapsed.as_secs() as f64) } else { 0.0 };

		println!(
			"[MIGRATION PROGRESS] Processed: {}, Success: {}, Failed: {}, Skipped: {}, Rate: {:.2}/sec, Elapsed: {}s",
			self.total_processed,
			self.successful_migrations,
			self.failed_migrations,
			self.skipped_existing,
			rate,
			elapsed.as_secs()
		);
	}
}

/// Check if a match has already been migrated by looking for metadata
async fn check_metadata_exists(metadata_collection: &Collection<B2Match>, match_id: &str) -> Result<bool, mongodb::error::Error> {
	let count = metadata_collection.count_documents(doc! { "m_id": match_id }).await?;
	Ok(count > 0)
}

/// Create B2Match metadata from MatchDetailsV1
async fn create_b2_match_from_match_details(match_data: &MatchDetailsV1) -> B2Match {
	use crate::structs::database::{ B2MatchMeta, B2MatchPlayer, B2MatchTeam };
	use crate::structs::parser::parse_match_v4;

	let match_info = &match_data.match_info;

	// Parse the match data to get structured data (similar to existing save_match_b2 function)
	let v4_parse = parse_match_v4(match_data.clone(), "migration").await;

	// Extract metadata
	let meta = B2MatchMeta {
		map_id: match_info.map_id.clone(),
		pod_id: match_info.game_pod_id.clone(),
		v: match_info.game_version.clone(),
		l_ms: match_info.game_length_millis.unwrap_or(0),
		s_ms: match_info.game_start_millis,
		q: match_info.queue_id.map(|q| q.to_string()).unwrap_or_else(|| "custom".to_string()),
		g: match_info.game_mode.clone(),
		s_id: match_info.season_id.to_string(),
		p: match_info.platform_type.clone(),
		p_s: if let Some(premier) = &match_info.premier_match_info {
			if let Some(id) = premier.premier_season_id { Some(id.to_string()) } else { None }
		} else {
			None
		},
		p_e: if let Some(premier) = &match_info.premier_match_info {
			if let Some(id) = premier.premier_event_id { Some(id.to_string()) } else { None }
		} else {
			None
		},
		p_t: None,
		p_m: None,
	};

	// Extract players from parsed data
	let players: Vec<B2MatchPlayer> = v4_parse.players
		.into_iter()
		.map(|x| B2MatchPlayer {
			id: x.puuid,
			t: x.team_id,
			c: x.agent.id,
			p: x.party_id,
			s: x.stats.score,
			k: x.stats.kills,
			d: x.stats.deaths,
			a: x.stats.assists,
			head: x.stats.headshots,
			body: x.stats.bodyshots,
			leg: x.stats.legshots,
			a_g: x.ability_casts.grenade.unwrap_or(0),
			a_1: x.ability_casts.ability1.unwrap_or(0),
			a_2: x.ability_casts.ability2.unwrap_or(0),
			a_u: x.ability_casts.ultimate.unwrap_or(0),
			tier: x.tier.id,
			l: x.account_level,
			d_d: x.stats.damage.dealt,
			d_r: x.stats.damage.received,
		})
		.collect();

	// Extract teams from parsed data
	let teams: Vec<B2MatchTeam> = v4_parse.teams
		.into_iter()
		.map(|x| B2MatchTeam {
			id: x.team_id,
			r_id: if let Some(premier_roster) = x.premier_roster {
				Some(premier_roster.id)
			} else {
				None
			},
			p: x.rounds.won,
		})
		.collect();

	B2Match {
		m_id: match_info.match_id.to_string(),
		meta,
		players,
		teams,
		b2_error: false,
	}
}

/// Upload match data to S3 with compression
async fn upload_to_s3(match_id: &str, match_data: &MatchDetailsV1) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
	let s3_client = S3_CLIENT.load_full();
	let bucket_name = "henrikdev"; // Using the same bucket as in existing code

	let match_data_bytes = serde_json::to_vec(match_data)?;
	let compressed_data = zstd::stream::encode_all(&match_data_bytes[..], 15)?;

	let file_path = format!("matches/{}.json", match_id);

	s3_client.put_object(bucket_name, &file_path, bytes::Bytes::from(compressed_data)).await?;

	Ok(())
}

/// Migrate a single match from MongoDB to S3 + metadata
async fn migrate_single_match(
	match_doc: Document,
	metadata_collection: &Collection<B2Match>,
	stats: Arc<Mutex<MigrationStats>>
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
	// Parse the match document
	let match_data = from_document::<MatchDetailsV1>(match_doc);
	if match_data.is_err() {
		let e = match_data.err().unwrap();
		eprintln!("[ERROR] Failed to parse match document: {}", e);
		return Err(Box::new(e));
	}
	let match_data = match_data.unwrap();
	let match_id = match_data.match_info.match_id.to_string();

	// Update processed count
	{
		let mut stats_guard = stats.lock().await;
		stats_guard.total_processed += 1;
	}

	// Check if already migrated
	if check_metadata_exists(metadata_collection, &match_id).await? {
		let mut stats_guard = stats.lock().await;
		stats_guard.skipped_existing += 1;
		if stats_guard.total_processed % 100 == 0 {
			println!("[SKIP] Match {} already migrated ({})", match_id, stats_guard.total_processed);
		}
		return Ok(());
	}

	// Upload to S3
	match upload_to_s3(&match_id, &match_data).await {
		Ok(_) => {
			// println!("[S3 UPLOAD] Successfully uploaded match {}", match_id);
		}
		Err(e) => {
			eprintln!("[S3 ERROR] Failed to upload match {}: {}", match_id, e);
			let mut stats_guard = stats.lock().await;
			stats_guard.failed_migrations += 1;
			return Err(e);
		}
	}

	// Create metadata entry
	let b2_match = create_b2_match_from_match_details(&match_data).await;

	match metadata_collection.insert_one(&b2_match).await {
		Ok(_) => {
			let mut stats_guard = stats.lock().await;
			stats_guard.successful_migrations += 1;
			//println!("[SUCCESS] Migrated match {} ({})", match_id, stats_guard.total_processed);

			// Print progress every 50 matches
			if stats_guard.total_processed % 50 == 0 {
				stats_guard.print_progress();
			}
		}
		Err(e) => {
			eprintln!("[METADATA ERROR] Failed to create metadata for match {}: {}", match_id, e);
			let mut stats_guard = stats.lock().await;
			stats_guard.failed_migrations += 1;
			return Err(Box::new(e));
		}
	}

	Ok(())
}

/// Main migration function
pub async fn run_mongo_to_s3_migration(
	client: &Client,
	ssd_client: &Client,
	batch_size: Option<i64>,
	max_concurrent: Option<usize>
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
	let batch_size = batch_size.unwrap_or(1000);
	let max_concurrent = max_concurrent.unwrap_or(10);

	println!("[MIGRATION START] Starting MongoDB to S3 migration");
	println!("[MIGRATION CONFIG] Batch size: {}, Max concurrent: {}", batch_size, max_concurrent);

	// Get collections
	let matches_collection = get_db_hdd::<Document>(ssd_client, "matches", None);
	let metadata_collection = get_db::<B2Match>(client, "match_metadata_b2", None);

	// Initialize stats
	let stats = Arc::new(Mutex::new(MigrationStats::new()));

	// Create semaphore for concurrency control
	let semaphore = Arc::new(Semaphore::new(max_concurrent));

	// Get total count for progress tracking
	let total_matches = matches_collection.estimated_document_count().await;
	println!("[MIGRATION INFO] Total matches to process: {:?}", total_matches);

	// Create cursor for batch processing
	let mut cursor = matches_collection.find(doc! {}).batch_size(batch_size as u32).await?;

	let mut tasks = Vec::new();

	// Process matches in batches
	while let Some(match_doc) = cursor.next().await {
		let match_doc = match_doc?;

		let permit = semaphore.clone().acquire_owned().await?;
		let metadata_collection_clone = metadata_collection.clone();
		let stats_clone = stats.clone();

		let task = tokio::spawn(async move {
			let _permit = permit; // Permit am Leben halten

			let match_id = match match_doc.get_document("matchInfo") {
				Ok(doc) => doc.get_str("matchId").unwrap_or("").to_string(),
				Err(_) => "".to_string(),
			};

			let mut attempts = 0;
			let max_attempts = 3;
			loop {
				attempts += 1;
				match migrate_single_match(match_doc.clone(), &metadata_collection_clone, stats_clone.clone()).await {
					Ok(_) => {
						break;
					}
					Err(_) if attempts < max_attempts => {
						// Optional: kurze Pause zwischen den Versuchen
						tokio::time::sleep(std::time::Duration::from_millis(200)).await;
					}
					Err(e) => {
						eprintln!("[ERROR] Migration failed: {} | Match ID: {}", e, match_id);
						break;
					}
				}
			}
			// Sehr kleine Verzögerung zwischen Matches
			tokio::time::sleep(std::time::Duration::from_millis(2)).await;
		});

		tasks.push(task);

		// Limit the number of pending tasks to prevent memory issues
		if tasks.len() >= max_concurrent * 2 {
			// Wait for some tasks to complete
			let (completed, _index, remaining) = futures::future::select_all(tasks).await;
			let _ = completed; // Ignore the result
			tasks = remaining;
		}
	}

	// Wait for all remaining tasks to complete
	futures::future::join_all(tasks).await;

	// Print final statistics
	let final_stats = stats.lock().await;
	println!("[MIGRATION COMPLETE] Final statistics:");
	final_stats.print_progress();

	Ok(())
}
