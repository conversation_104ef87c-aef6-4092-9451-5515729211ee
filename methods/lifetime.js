import {errorhandler, getQueues, getMaps, redis, makeRequest, getDB, base_url, getActIDs, getAgents} from '../server.js';
import {DatabaseValorantMMRHistory, ValorantMMRHistory} from './mmr-history.js';

export const lifetime_matches = async ({puuid, res, queries, region} = {}) => {
    const maps = getMaps();
    let search_query = new URLSearchParams();
    const queues = getQueues();
    if (queries.mode) {
        if (Array.isArray(queries.mode)) return errorhandler({status: 400, res, errors: [{instance: 'own', code: 116, details: 'mode'}]});
        if (!queues.some(i => i.api == queries.mode.toLowerCase()))
            return errorhandler({status: 400, res, errors: [{instance: 'own', code: 106, details: queues.map(i => i.api)}]});
        search_query.append('queue', queues.find(i => i.api == queries.mode.toLowerCase()).id);
    }
    if (queries.map) {
        if (Array.isArray(queries.map)) return errorhandler({status: 400, res, errors: [{instance: 'own', code: 116, details: 'map'}]});
        if (!maps.some(i => i.displayName.toLowerCase() == queries.map.toLowerCase()))
            return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 107, details: maps.map(i => i.displayName)}]});
    }
    if (!queries.size && queries.page) return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 117, details: 'size'}]});
    if (queries.size && queries.page && (isNaN(queries.size) || isNaN(queries.page)))
        return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 118, details: null}]});
    if (queries.page && Number(queries.page) < 1) return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 119, details: null}]});
    if (queries.size && Number(queries.size) < 1) return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 120, details: null}]});
    const redis_data = await redis.get(`mhistory;${region};${puuid};${queries.mode?.toLowerCase() ?? null}`);
    const redis_data_names = await redis.get(`names;${puuid}`);
    const request = (
        await Promise.allSettled([
            JSON.parse(redis_data) ??
                makeRequest({
                    url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${puuid}/competitiveupdates?${search_query.toString()}`,
                    region,
                    return_error: true,
                    res,
                }),
            JSON.parse(redis_data_names) ??
                makeRequest({
                    url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                    method: 'PUT',
                    body: [puuid],
                    return_error: true,
                    region: 'eu',
                    res,
                }),
        ])
    ).map(i => i.value);
    const historyreq =
        JSON.parse(redis_data) ??
        (await makeRequest({
            url: `https://pd.${region}.a.pvp.net/mmr/v1/players/${puuid}/competitiveupdates?${search_query.toString()}`,
            region,
            res,
        }));
    if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res, errors: [{instance: 'riot'}]});
    if (request[0].response || request[0].code) return errorhandler({status: request[0].response?.status ?? 500, res, errors: [{instance: 'riot'}]});
    if (!redis_data && historyreq.data)
        redis.set(`mhistory;${region};${puuid};${queries.mode?.toLowerCase() ?? null}`, JSON.stringify({data: request[0].data}), {EX: 300});
    if (!redis_data_names && request[1].data) redis.set(`names;${puuid}`, JSON.stringify({data: request[1].data}), {EX: 300});
    const unfetched_matches = [];
    if (!redis_data)
        for (let i = 0; request[0].data.Matches.length > i; i++) {
            const lookup = await getDB('history').countDocuments({match_id: request[0].data.Matches[i].MatchID});
            if (!lookup)
                unfetched_matches.push(
                    makeRequest({
                        url: `${base_url}/valorantlabs/v1/match/${region}/${request[0].data.Matches[i].MatchID}`,
                        return_error: true,
                        proxy: false,
                    })
                );
        }
    if (unfetched_matches.length) (await Promise.allSettled(unfetched_matches)).map(i => i.value);
    let query = {'stats.id': puuid};
    if (queries.mode) query['meta.mode'] = queues.find(k => k.api == queries.mode.toLowerCase()).name.toLowerCase();
    if (queries.map) query['meta.map'] = maps.find(k => k.displayName.toLowerCase() == queries.map.toLowerCase()).uuid;
    let matches_db = await getDB('history')
        .find(query)
        .sort('meta.g_s', -1)
        .skip(queries.page && queries.size ? Number(queries.page) * Number(queries?.size) - Number(queries?.size) : 0)
        .limit(Number(queries?.size) ?? 0)
        .toArray();
    let riotid = {
        name: request[1].data?.[0]?.GameName ?? null,
        tag: request[1].data?.[0]?.TagLine ?? null,
    };
    const actids = Object.entries(getActIDs());
    const agents = getAgents();
    const total = await getDB('history').countDocuments(query);
    const before = Number(queries.page) * Number(queries?.size) - Number(queries?.size);
    return res.code(200).send({
        status: 200,
        ...riotid,
        results: {
            total,
            returned: matches_db.length,
            before: queries.page && queries.size ? before : 0,
            after: queries.page && queries.size ? total - (matches_db.length + before) : 0,
        },
        data: matches_db.map(i => {
            const stats = i.stats.find(k => k.id == puuid);
            return {
                meta: {
                    id: i.match_id,
                    map: {
                        id: i.meta.map,
                        name: maps.find(k => k.uuid == i.meta.map).displayName ?? null,
                    },
                    version: i.meta.v,
                    mode: queues.find(k => k.id == i.meta.mode).name,
                    started_at: new Date(i.meta.g_s).toISOString(),
                    season: {
                        id: i.meta.s,
                        short: actids.find(k => k[1] == i.meta.s)[0] ?? null,
                    },
                    region: i.meta.r,
                    cluster: i.meta.c,
                },
                stats: {
                    puuid: stats.id,
                    team: stats.team,
                    level: stats.l,
                    character: {
                        id: stats.c,
                        name: agents.find(k => k.uuid == stats.c).displayName ?? null,
                    },
                    tier: stats.tier,
                    score: stats.s,
                    kills: stats.k,
                    deaths: stats.d,
                    assists: stats.a,
                    shots: {
                        head: stats.h,
                        body: stats.b,
                        leg: stats.leg,
                    },
                    damage: {
                        made: stats.dm,
                        received: stats.dr,
                    },
                },
                teams: {
                    red: i.teams.r,
                    blue: i.teams.b,
                },
            };
        }),
    });
};
export const lifetime_mmr_history = async ({puuid, res, queries, region} = {}) => {
    const maps = getMaps();
    let search_query = new URLSearchParams();
    if (!queries.size && queries.page) return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 117, details: 'size'}]});
    if (queries.size && queries.page && (isNaN(queries.size) || isNaN(queries.page)))
        return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 118, details: null}]});
    if (queries.page && Number(queries.page) < 1) return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 119, details: null}]});
    if (queries.size && Number(queries.size) < 1) return errorhandler({status: 400, res, errors: [{instance: 'riot', code: 120, details: null}]});
    const mmr_history = new ValorantMMRHistory(puuid, region, res);
    await mmr_history.fetchData();
    await mmr_history._parse();
    await mmr_history.lifetime();
    let query = {p_id: puuid};
    let mmr_db = await getDB('mmr_history')
        .find(query)
        .sort('d', -1)
        .skip(queries.page && queries.size ? Number(queries.page) * Number(queries?.size) - Number(queries?.size) : 0)
        .limit(Number(queries?.size) ?? 0)
        .toArray();
    const total = await getDB('mmr_history').countDocuments(query);
    const before = Number(queries.page) * Number(queries?.size) - Number(queries?.size);
    return res.code(200).send({
        status: 200,
        ...mmr_history.riotid,
        results: {
            total,
            returned: mmr_db.length,
            before: queries.page && queries.size ? before : 0,
            after: queries.page && queries.size ? total - (mmr_db.length + before) : 0,
        },
        data: mmr_db.map(i => new DatabaseValorantMMRHistory(i).toJSON()),
    });
};
