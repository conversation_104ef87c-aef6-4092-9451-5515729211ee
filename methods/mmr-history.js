import {getMaps, base_url, makeRequest, errorhandler, redis, tiers, moment, calculateElo, getPuuid, getDB, getActIDs} from '../server.js';

export class ValorantMMRHistory {
    constructor(user, region, res) {
        this.res = res;
        this.user = user;
        this.region = region;
        this.maps = getMaps();
        this.tiers = tiers;
        this.API_COMP = null;
        this.API_NAME = null;
    }
    async _init() {
        if (typeof this.user == 'object') {
            if (this.user.name && this.user.tag) await this.fetchByName();
        }
        if (typeof this.team == 'undefined') {
            this.unknown = true;
        }
        if (this.res.sent) return;
        await this.fetchData();
        if (this.res.sent) return;
        await this._parse();
        if (this.res.sent) return;
        if (!this.redis_data) await this.lifetime();
        if (this.res.sent) return;
        this.respond();
    }
    async fetchByName() {
        let db = await getPuuid({name: this.user.name, tag: this.user.tag});
        if (!db) {
            const puuid = await makeRequest({
                url: `${base_url}/valorant/v1/account/${encodeURI(this.user.name)}/${encodeURI(this.user.tag)}`,
                proxy: false,
                return_error: true,
                timeout: 7500,
                res: this.res,
            });
            if (puuid.code == 'ECONNABORTED') return errorhandler({status: 408, res: this.res, errors: [{instance: 'riot'}]});
            if (puuid.response) return errorhandler({status: puuid.response.status, res: this.res, errors: [{instance: 'riot'}]});
            this.user = puuid.data.data.puuid;
        } else this.user = db.puuid;
    }
    async fetchData() {
        this.redis_data = await redis.get(`mmr;v1;${this.region};${this.user}`);
        this.redis_data_names = await redis.get(`names;${this.user}`);
        const request = (
            await Promise.allSettled([
                JSON.parse(this.redis_data) ??
                    makeRequest({
                        url: `https://pd.${this.region}.a.pvp.net/mmr/v1/players/${this.user}/competitiveupdates?queue=competitive&startIndex=0&endIndex=20`,
                        res: this.res,
                        region: this.region,
                        return_error: true,
                    }),
                JSON.parse(this.redis_data_names) ??
                    makeRequest({
                        url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                        method: 'PUT',
                        body: [this.user],
                        return_error: true,
                        region: 'eu',
                        res: this.res,
                    }),
            ])
        ).map(i => i.value);
        if (request.some(item => item.code == 'ECONNABORTED')) return errorhandler({status: 408, res: this.res, errors: [{instance: 'riot'}]});
        if (request[0].response) return errorhandler({status: request[0].response.status, res: this.res, errors: [{instance: 'riot'}]});
        if (!request[0].data?.Matches?.length) return this.res.code(200).send({status: 200, name: null, tag: null, data: []});
        if (!this.redis_data && request[0].data) redis.set(`mmr;v1;${this.region};${this.user}`, JSON.stringify({data: request[0].data}), {EX: 300});
        if (!this.redis_data_names && request[1].data) redis.set(`names;${this.user}`, JSON.stringify({data: request[1].data}), {EX: 300});
        this.API_COMP = request[0].data;
        this.API_NAME = request[1].data;
    }
    async _parse() {
        if (!this.API_COMP) console.log(this.user);
        this.comp = this.API_COMP.Matches.filter(i => i.TierAfterUpdate != 0 && i.SeasonID != '').sort((a, b) => b.MatchStartTime - a.MatchStartTime);
        if (!this.comp.length) return this.res.code(200).send({status: 200, name: null, tag: null, data: []});
        this.history = this.comp.map(i => this._parseEntry(i));
        this.riotid = {
            name: this.API_NAME?.[0]?.GameName ?? null,
            tag: this.API_NAME?.[0]?.TagLine ?? null,
        };
        if (this.riotid.name && this.riotid.tag) getDB('puuids').updateOne({puuid: this.user}, {$set: {name: this.riotid.name, tag: this.riotid.tag}});
    }
    _parseEntry(entry) {
        const map = this.maps.find(k => k.mapUrl == entry.MapID);
        return {
            currenttier: entry.TierAfterUpdate,
            currenttierpatched: this.tiers[entry.TierAfterUpdate],
            images: {
                small: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${entry.TierAfterUpdate}/smallicon.png`,
                large: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${entry.TierAfterUpdate}/largeicon.png`,
                triangle_down: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${entry.TierAfterUpdate}/ranktriangledownicon.png`,
                triangle_up: `https://media.valorant-api.com/competitivetiers/03621f52-342b-cf4e-4f86-9350a49c6d04/${entry.TierAfterUpdate}/ranktriangleupicon.png`,
            },
            match_id: entry.MatchID,
            map: {
                name: map.displayName,
                id: map.uuid,
            },
            season_id: entry.SeasonID,
            ranking_in_tier: entry.RankedRatingAfterUpdate,
            mmr_change_to_last_game: entry.RankedRatingEarned,
            elo: calculateElo({tier: entry.TierAfterUpdate, progress: entry.RankedRatingAfterUpdate}),
            date: moment(entry.MatchStartTime).format('LLLL'),
            date_raw: Math.round(entry.MatchStartTime / 1000),
        };
    }
    async lifetime() {
        for (const match of this.comp) {
            const db = await getDB('mmr_history').findOne({m_id: match.MatchID, p_id: this.user});
            if (!db) {
                const parsed = this._parseEntry(match);
                await getDB('mmr_history').insertOne({
                    m_id: match.MatchID,
                    p_id: this.user,
                    t: parsed.currenttier,
                    map_id: parsed.map.id,
                    s_id: parsed.season_id,
                    r: parsed.ranking_in_tier,
                    m_c: parsed.mmr_change_to_last_game,
                    d: parsed.date_raw,
                });
            }
        }
    }
    respond() {
        if (!this.res.sent) return this.res.code(200).send({status: 200, name: this.riotid.name, tag: this.riotid.tag, data: this.history});
    }
}

export class DatabaseValorantMMRHistory {
    constructor(entry) {
        this.entry = entry;
        this.maps = getMaps();
        this.actids = Object.entries(getActIDs());
        this.tiers = tiers;
        this._parse();
    }
    _parse() {
        const map = this.maps.find(k => k.uuid == this.entry.map_id);
        this.match_id = this.entry.m_id;
        this.tier = {
            id: this.entry.t,
            name: this.tiers[this.entry.t],
        };
        this.map = {
            name: map.displayName,
            id: map.uuid,
        };
        this.season = {
            id: this.entry.s_id,
            short: this.actids.find(k => k[1] == this.entry.s_id)[0] ?? null,
        };
        this.ranking_in_tier = this.entry.r;
        this.last_mmr_change = this.entry.m_c;
        this.elo = calculateElo({tier: this.entry.t, progress: this.entry.r});
        this.date = new Date(this.entry.d * 1000).toISOString();
    }
    toJSON() {
        return {
            match_id: this.match_id,
            tier: this.tier,
            map: this.map,
            season: this.season,
            ranking_in_tier: this.ranking_in_tier,
            last_mmr_change: this.last_mmr_change,
            elo: this.elo,
            date: this.date,
        };
    }
}
