import {getGamepods, getMaps, getWeapons, moment, getDB, tiers, getGear, getAgents, extractHexColorCode, getGamemodes, getQueues} from '../server.js';

export class ValorantMatchv2 {
    constructor(raw) {
        this.raw = raw;
        this.maps = getMaps();
        this.weapons = getWeapons();
        this.gamepods = getGamepods();
        this.gear = getGear();
        this.agents = getAgents();
        this.gamemodes = getGamemodes();
        this.queues = getQueues();
        this.kills = [];
        this.players = [];
        this.rounds = [];
        this.coaches = [];
        this.observers = [];
        this.tiers = tiers;
    }
    async _init() {
        this.generateMeta();
        await this.createPlayerObject();
        this.createCoaches();
        this.createRoundObject();
        this.generateTeams();
        this.generateKills();
    }
    generateMeta() {
        if (!this.queues.find(i => i.id == this.raw.matchInfo.queueID)) console.log(this.queues, this.raw.matchInfo.queueID);
        this.metadata = {
            map: this.maps.find(i => i.mapUrl == this.raw.matchInfo.mapId).displayName,
            game_version: this.raw.matchInfo.gameVersion,
            game_length: Math.round(this.raw.matchInfo.gameLengthMillis / 1000),
            game_start: Math.round(this.raw.matchInfo.gameStartMillis / 1000),
            game_start_patched: moment(this.raw.matchInfo.gameStartMillis).format('LLLL'),
            rounds_played: this.raw.teams[0].roundsPlayed,
            mode: this.queues.find(i => i.id == this.raw.matchInfo.queueID).name,
            mode_id: this.raw.matchInfo.queueID,
            queue: this.gamemodes.find(i => this.raw.matchInfo.gameMode.includes(i.asset)).name,
            season_id: this.raw.matchInfo.seasonId,
            platform: this.raw.matchInfo.platformType,
            matchid: this.raw.matchInfo.matchId,
            platform: this.raw.matchInfo.platformType,
            premier_info: {
                tournament_id: this.raw.matchInfo.premierMatchInfo?.tournamentId ?? null,
                matchup_id: this.raw.matchInfo.premierMatchInfo?.matchupId ?? null,
            },
            region: this.raw.region ?? null,
            cluster: this.gamepods[this.raw.matchInfo.gamePodId] ?? 'Unknown',
        };
    }
    generatePlayerRoundObject(puuid) {
        const playerdata = this.raw.players.find(i => i.subject == puuid);
        return {
            puuid: playerdata.subject,
            display_name: `${playerdata.gameName}#${playerdata.tagLine}`,
            team: playerdata.teamId,
        };
    }
    generateLocation(locations) {
        return locations.map(i => {
            const cplayer = this.raw.players.find(k => k.subject == i.subject);
            return {
                player_puuid: cplayer.subject,
                player_display_name: `${cplayer.gameName}#${cplayer.tagLine}`,
                player_team: cplayer.teamId,
                location: {
                    x: i.location.x,
                    y: i.location.y,
                },
                view_radians: i.viewRadians,
            };
        });
    }
    generateKills() {
        if (!this.raw.kills) return [];
        for (const kill of this.raw.kills) {
            const victim = kill.victim != '' ? this.generatePlayerRoundObject(kill.victim) : null;
            const killer = kill.killer != '' ? this.generatePlayerRoundObject(kill.killer) : null;
            const assistants = [];
            for (let p = 0; kill.assistants.length > p; p++) {
                const assistant = this.generatePlayerRoundObject(kill.assistants[p]);
                assistants.push({
                    assistant_puuid: assistant.puuid,
                    assistant_display_name: assistant.display_name,
                    assistant_team: assistant.team,
                });
            }
            const locations = this.generateLocation(kill.playerLocations);
            this.kills.push({
                kill_time_in_round: kill.roundTime,
                kill_time_in_match: kill.gameTime,
                round: kill.round,
                killer_puuid: killer?.puuid ?? null,
                killer_display_name: killer?.display_name ?? null,
                killer_team: killer?.team ?? null,
                victim_puuid: victim?.puuid ?? null,
                victim_display_name: victim?.display_name ?? null,
                victim_team: victim?.team ?? null,
                victim_death_location: {
                    x: kill.victimLocation.x,
                    y: kill.victimLocation.y,
                },
                damage_weapon_id: kill.finishingDamage.damageItem,
                damage_weapon_name:
                    kill.finishingDamage.damageType == 'Weapon' ? this.weapons.find(i => i.uuid == kill.finishingDamage.damageItem.toLowerCase())?.displayName : null,
                damage_weapon_assets: {
                    display_icon:
                        kill.finishingDamage.damageType == 'Weapon' ? this.weapons.find(i => i.uuid == kill.finishingDamage.damageItem.toLowerCase())?.displayIcon : null,
                    killfeed_icon:
                        kill.finishingDamage.damageType == 'Weapon'
                            ? this.weapons.find(item => item.uuid == kill.finishingDamage.damageItem.toLowerCase())?.killStreamIcon
                            : null,
                },
                secondary_fire_mode: kill.finishingDamage.isSecondaryFireMode,
                player_locations_on_kill: locations,
                assistants,
            });
        }
    }
    generatePlayerStats(stats) {
        stats = stats.map(i => {
            let damage = 0;
            let bodyshots = 0;
            let headshots = 0;
            let legshots = 0;
            const damage_stats = [];
            const kills_stats = [];
            const cplayer = this.generatePlayerRoundObject(i.subject);
            const player_index = this.players.findIndex(i => i.puuid == cplayer.puuid);
            for (const rdamage of i.damage) {
                const player_index_receiver = this.players.findIndex(i => i.puuid == rdamage.receiver);
                const cplayer = this.generatePlayerRoundObject(rdamage.receiver);
                damage_stats.push({
                    receiver_puuid: rdamage.receiver,
                    receiver_display_name: cplayer.display_name,
                    receiver_team: cplayer.team,
                    bodyshots: rdamage.bodyshots,
                    damage: rdamage.damage,
                    headshots: rdamage.headshots,
                    legshots: rdamage.legshots,
                });
                bodyshots += rdamage.bodyshots;
                headshots += rdamage.headshots;
                legshots += rdamage.legshots;
                if (rdamage.damage != 999) damage += rdamage.damage;
                if (rdamage.damage != 999) this.players[player_index_receiver].damage_received += rdamage.damage;
            }
            for (const kill of i.kills) {
                const victim = this.generatePlayerRoundObject(kill.victim);
                const killer = this.generatePlayerRoundObject(kill.killer);
                const assistants = [];
                for (let p = 0; kill.assistants.length > p; p++) {
                    const assistant = this.generatePlayerRoundObject(kill.assistants[p]);
                    assistants.push({
                        assistant_puuid: assistant.puuid,
                        assistant_display_name: assistant.display_name,
                        assistant_team: assistant.team,
                    });
                }
                const locations = this.generateLocation(kill.playerLocations);
                kills_stats.push({
                    kill_time_in_round: kill.roundTime,
                    kill_time_in_match: kill.gameTime,
                    killer_puuid: killer.puuid,
                    killer_display_name: killer.display_name,
                    killer_team: killer.team,
                    victim_puuid: victim.puuid,
                    victim_display_name: victim.display_name,
                    victim_team: victim.team,
                    victim_death_location: {
                        x: kill.victimLocation.x,
                        y: kill.victimLocation.y,
                    },
                    damage_weapon_id: kill.finishingDamage.damageItem,
                    damage_weapon_name:
                        kill.finishingDamage.damageType == 'Weapon'
                            ? this.weapons.find(item => item.uuid == kill.finishingDamage.damageItem.toLowerCase())?.displayName
                            : null,
                    damage_weapon_assets: {
                        display_icon:
                            kill.finishingDamage.damageType == 'Weapon'
                                ? this.weapons.find(item => item.uuid == kill.finishingDamage.damageItem.toLowerCase())?.displayIcon
                                : null,
                        killfeed_icon:
                            kill.finishingDamage.damageType == 'Weapon'
                                ? this.weapons.find(item => item.uuid == kill.finishingDamage.damageItem.toLowerCase())?.killStreamIcon
                                : null,
                    },
                    secondary_fire_mode: kill.finishingDamage.isSecondaryFireMode,
                    player_locations_on_kill: locations,
                    assistants,
                });
            }
            this.players[player_index].damage_made += damage;
            this.players[player_index].stats.bodyshots += bodyshots;
            this.players[player_index].stats.headshots += headshots;
            this.players[player_index].stats.legshots += legshots;
            this.players[player_index].economy.spent.overall += i.economy.spent;
            this.players[player_index].economy.loadout_value.overall += i.economy.loadoutValue;

            return {
                ability_casts: {
                    c_casts: i.ability.grenadeEffects,
                    q_casts: i.ability.ability1Effects,
                    e_cast: i.ability.ability2Effects,
                    x_cast: i.ability.ultimateEffects,
                },
                player_puuid: cplayer.puuid,
                player_display_name: cplayer.display_name,
                player_team: cplayer.team,
                damage_events: damage_stats,
                damage: damage,
                bodyshots: bodyshots,
                headshots: headshots,
                legshots: legshots,
                kill_events: kills_stats,
                kills: kills_stats.length,
                score: i.score,
                economy: {
                    loadout_value: i.economy.loadoutValue,
                    weapon: {
                        id: i.economy.weapon.length ? i.economy.weapon.toLowerCase() : null,
                        name: i.economy.weapon.length ? this.weapons.find(k => k.uuid == i.economy.weapon.toLowerCase()).displayName : null,
                        assets: {
                            display_icon: i.economy.weapon.length ? this.weapons.find(k => k.uuid == i.economy.weapon.toLowerCase()).displayIcon : null,
                            killfeed_icon: i.economy.weapon.length ? this.weapons.find(k => k.uuid == i.economy.weapon.toLowerCase()).killStreamIcon : null,
                        },
                    },
                    armor: {
                        id: i.economy.armor.length ? i.economy.armor.toLowerCase() : null,
                        name: i.economy.armor.length ? this.gear.find(k => k.uuid == i.economy.armor.toLowerCase()).displayName : null,
                        assets: {
                            display_icon: i.economy.armor.length ? this.gear.find(k => k.uuid == i.economy.armor.toLowerCase()).displayIcon : null,
                        },
                    },
                    remaining: i.economy.remaining,
                    spent: i.economy.spent,
                },
                was_afk: i.wasAfk,
                was_penalized: i.wasPenalized,
                stayed_in_spawn: i.stayedInSpawn,
            };
        });
        return stats;
    }
    async createPlayerObject() {
        const non_observers = this.raw.players.filter(i => i.teamId != 'Neutral');
        const observers = this.raw.players.filter(i => i.teamId == 'Neutral');
        for (const player of non_observers) {
            const check = await getDB('puuids').findOne({puuid: player.subject});
            if (!check || !check.last_match || this.raw.matchInfo.gameStartMillis > check.last_match)
                getDB('puuids').updateOne(
                    {puuid: player.subject},
                    {
                        $set: {
                            account_level: player.accountLevel,
                            name: player.gameName,
                            tag: player.tagLine,
                            card: player.playerCard,
                            last_update: moment().unix(),
                            last_match: this.raw.matchInfo.gameStartMillis,
                            region: this.raw.region ?? null,
                        },
                    },
                    {upsert: true}
                );
            if (!this.agents.find(i => i.uuid == player.characterId)) console.log(player.characterId);
            this.players.push({
                puuid: player.subject,
                name: player.gameName,
                tag: player.tagLine,
                team: player.teamId,
                level: player.accountLevel,
                character: this.agents.find(i => i.uuid == player.characterId).displayName,
                currenttier: player.competitiveTier,
                currenttier_patched: tiers[player.competitiveTier],
                player_card: player.playerCard,
                player_title: player.playerTitle,
                party_id: player.partyId,
                session_playtime: {
                    minutes: player.sessionPlaytimeMinutes,
                    seconds: player.sessionPlaytimeMinutes * 60,
                    milliseconds: player.sessionPlaytimeMinutes * 60 * 1000,
                },
                behavior: {
                    afk_rounds: player.behaviorFactors.afkRounds,
                    friendly_fire: {
                        incoming: player.behaviorFactors.friendlyFireIncoming ?? null,
                        outgoing: player.behaviorFactors.friendlyFireOutgoing ?? null,
                    },
                    rounds_in_spawn: player.behaviorFactors.stayedInSpawnRounds ?? null,
                },
                platform: {
                    type: player.platformInfo.platformType,
                    os: {
                        name: player.platformInfo.platformOS,
                        version: player.platformInfo.platformOSVersion,
                    },
                },
                ability_casts: {
                    c_cast:
                        (this.raw.matchInfo.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            this.raw.matchInfo.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            player.stats.abilityCasts == undefined) == true
                            ? null
                            : player.stats.abilityCasts.grenadeCasts,
                    q_cast:
                        (this.raw.matchInfo.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            this.raw.matchInfo.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            player.stats.abilityCasts == undefined) == true
                            ? null
                            : player.stats.abilityCasts.ability1Casts,
                    e_cast:
                        (this.raw.matchInfo.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            this.raw.matchInfo.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            player.stats.abilityCasts == undefined) == true
                            ? null
                            : player.stats.abilityCasts.ability2Casts,
                    x_cast:
                        (this.raw.matchInfo.gameMode == '/Game/GameModes/Deathmatch/DeathmatchGameMode.DeathmatchGameMode_C' ||
                            this.raw.matchInfo.gameMode == '/Game/GameModes/GunGame/GunGameTeamsGameMode.GunGameTeamsGameMode_C' ||
                            player.stats.abilityCasts == undefined) == true
                            ? null
                            : player.stats.abilityCasts.ultimateCasts,
                },
                assets: {
                    card: {
                        small: `https://media.valorant-api.com/playercards/${player.playerCard}/smallart.png`,
                        large: `https://media.valorant-api.com/playercards/${player.playerCard}/largeart.png`,
                        wide: `https://media.valorant-api.com/playercards/${player.playerCard}/wideart.png`,
                    },
                    agent: {
                        small: `https://media.valorant-api.com/agents/${player.characterId}/displayicon.png`,
                        bust: `https://media.valorant-api.com/agents/${player.characterId}/fullportrait.png`,
                        full: `https://media.valorant-api.com/agents/${player.characterId}/fullportrait.png`,
                        killfeed: `https://media.valorant-api.com/agents/${player.characterId}/killfeedportrait.png`,
                    },
                },
                stats: {
                    score: player.stats.score,
                    kills: player.stats.kills,
                    deaths: player.stats.deaths,
                    assists: player.stats.assists,
                    bodyshots: 0,
                    headshots: 0,
                    legshots: 0,
                },
                economy: {
                    spent: {
                        overall: 0,
                        average: 0,
                    },
                    loadout_value: {
                        overall: 0,
                        average: 0,
                    },
                },
                damage_made: 0,
                damage_received: 0,
            });
        }
        for (const observer of observers) {
            this.observers.push({
                puuid: observer.subject,
                name: observer.gameName,
                tag: observer.tagLine,
                platform: {
                    type: observer.platformInfo.platformType,
                    os: {
                        name: observer.platformInfo.platformOS,
                        version: observer.platformInfo.platformOSVersion,
                    },
                },
                session_playtime: {
                    minutes: observer.sessionPlaytimeMinutes,
                    seconds: observer.sessionPlaytimeMinutes * 60,
                    milliseconds: observer.sessionPlaytimeMinutes * 60 * 1000,
                },
                team: observer.teamId,
                level: observer.accountLevel,
                player_card: observer.playerCard,
                player_title: observer.playerTitle,
                party_id: observer.partyId,
            });
        }
        return;
    }
    createCoaches() {
        for (const coach of this.raw.coaches ?? []) {
            this.coaches.push({puuid: coach.subject, team: coach.teamId});
        }
    }
    createRoundObject() {
        for (const round of this.raw.roundResults) {
            this.rounds.push({
                winning_team: round.winningTeam,
                end_type: round.roundResult,
                bomb_planted: round.bombPlanter ? true : false,
                bomb_defused: round.bombDefuser ? true : false,
                plant_events: {
                    plant_location: round.bombPlanter ? {x: round.plantLocation.x, y: round.plantLocation.y} : null,
                    planted_by: round.bombPlanter ? this.generatePlayerRoundObject(round.bombPlanter) : null,
                    plant_site: round.bombPlanter ? round.plantSite : null,
                    plant_time_in_round: round.bombPlanter ? round.plantRoundTime : null,
                    player_locations_on_plant: round.bombPlanter ? this.generateLocation(round.plantPlayerLocations) : null,
                },
                defuse_events: {
                    defuse_location: round.bombDefuser ? {x: round.defuseLocation.x, y: round.defuseLocation.y} : null,
                    defused_by: round.bombDefuser ? this.generatePlayerRoundObject(round.bombDefuser) : null,
                    defuse_time_in_round: round.bombDefuser ? round.defuseRoundTime : null,
                    player_locations_on_defuse: round.bombDefuser ? this.generateLocation(round.defusePlayerLocations) : null,
                },
                player_stats: this.generatePlayerStats(round.playerStats),
            });
        }
        for (const [i, player] of this.players.entries()) {
            this.players[i].economy.spent.average = Math.round(this.players[i].economy.spent.overall / this.metadata.rounds_played);
            this.players[i].economy.loadout_value.average = Math.round(this.players[i].economy.loadout_value.overall / this.metadata.rounds_played);
        }
    }
    generateTeams() {
        const rteam = this.raw.teams.find(item => item.teamId == 'Red');
        const bteam = this.raw.teams.find(item => item.teamId == 'Blue');
        this.teams = {
            red: {
                has_won: rteam ? rteam.won : null,
                rounds_won: rteam ? rteam.numPoints : null,
                rounds_lost: bteam ? bteam.numPoints : null,
                roster: rteam?.rosterInfo ? this.generateRoster(rteam.rosterInfo) : null,
            },
            blue: {
                has_won: bteam ? bteam.won : null,
                rounds_won: bteam ? bteam.numPoints : null,
                rounds_lost: rteam ? rteam.numPoints : null,
                roster: bteam?.rosterInfo ? this.generateRoster(bteam.rosterInfo) : null,
            },
        };
    }
    generateRoster(info) {
        const primary_color = extractHexColorCode(info.PrimaryColor);
        const secondary_color = extractHexColorCode(info.SecondaryColor);
        const tertiary_color = extractHexColorCode(info.TertiaryColor);
        return {
            id: info.RosterID,
            members: info.Members.map(k => k.Subject),
            name: info.Name.trim(),
            tag: info.Tag.trim(),
            customization: {
                icon: info.Icon,
                image: `https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/${info.Icon}?primary=${primary_color.replace(
                    '#',
                    ''
                )}&secondary=${secondary_color.replace('#', '')}&tertiary=${tertiary_color.replace('#', '')}`,
                primary_color,
                secondary_color,
                tertiary_color,
            },
        };
    }
    async lifetime() {
        const payload = {
            meta: {
                map: this.maps.find(i => i.mapUrl == this.raw.matchInfo.mapId).uuid,
                v: this.raw.matchInfo.gameVersion,
                mode: this.raw.matchInfo.queueID,
                g_s: this.raw.matchInfo.gameStartMillis,
                s: this.raw.matchInfo.seasonId,
                r: this.raw.region ?? null,
                c: this.gamepods[this.raw.matchInfo.gamePodId] ?? null,
            },
            stats: this.players.map(i => {
                return {
                    id: i.puuid,
                    team: i.team,
                    l: i.level,
                    c: this.agents.find(k => k.displayName == i.character).uuid ?? null,
                    tier: i.currenttier,
                    s: i.stats.score,
                    k: i.stats.kills,
                    d: i.stats.deaths,
                    a: i.stats.assists,
                    b: i.stats.bodyshots,
                    h: i.stats.headshots,
                    leg: i.stats.legshots,
                    dr: i.damage_received,
                    dm: i.damage_made,
                };
            }),
            teams: {
                r: this.teams.red.rounds_won ?? null,
                b: this.teams.blue.rounds_won ?? null,
            },
        };
        await getDB('history').findOneAndUpdate(
            {match_id: this.metadata.matchid},
            {
                $setOnInsert: payload,
            },
            {upsert: true}
        );
    }
    toJSON() {
        return {
            metadata: this.metadata,
            players: {
                all_players: this.players,
                red: this.players.filter(i => i.team == 'Red'),
                blue: this.players.filter(i => i.team == 'Blue'),
            },
            observers: this.observers,
            coaches: this.coaches,
            teams: this.teams,
            rounds: this.rounds,
            kills: this.kills,
        };
    }
}
