import {makeRequest, errorhandler, base_url, getDB, redis, getPremierConferences, getGamepods, getMaps, getPremierSeason} from '../server.js';

export class BasePremierTeam {
    constructor(team) {
        this.team = team;
    }

    async _init() {
        if (typeof this.team == 'object') {
            if (this.team.name && this.team.tag) await this.fetchByName();
        }
        if (typeof this.team == 'string') await this.fetchByID();
        if (typeof this.team == 'undefined') {
            this.unknown = true;
            return;
        }
        if (this.unknown) return;
        this._parse();
    }

    _parse() {
        this.parse(this.team);
    }

    parse(team) {
        this.id = team.id;
        this.name = team.name;
        this.tag = team.tag;
        this.conference = team.conference;
        this.division = team.div;
        this.affinity = team.affinity;
        this.region = team.region;
        this.losses = team.losses;
        this.wins = team.wins;
        this.score = team.score;
        this.ranking = team.ranking;
        this.member = team.member ?? [];
        this.matches = {
            league: team.league_games ?? [],
            tournament: team.tournament_games ?? [],
        };
        this.customization = {
            icon: team.customization.icon,
            image: `https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/${team.customization.icon}?primary=${team.customization.primary_color.replace(
                '#',
                ''
            )}&secondary=${team.customization.secondary_color.replace('#', '')}&tertiary=${team.customization.tertiary_color.replace('#', '')}`,
            primary: team.customization.primary_color,
            secondary: team.customization.secondary_color,
            tertiary: team.customization.tertiary_color,
        };
        delete this.team;
    }

    async fetchByID() {
        const fetch = await getDB('premier_teams').findOne({id: this.team, season: getPremierSeason()});
        if (fetch) {
            this.team = fetch;
            this.unknown = false;
        } else this.unknown = true;
        return;
    }

    async fetchByName() {
        const fetch = await getDB('premier_teams').findOne(
            {name: this.team.name, tag: this.team.tag, season: getPremierSeason()},
            {collation: {locale: 'en', strength: 2, alternate: 'shifted'}}
        );
        if (fetch) {
            this.team = fetch;
            this.unknown = false;
        } else this.unknown = true;
        return;
    }

    toJSON() {
        return {
            id: this.id,
            name: this.name,
            tag: this.tag,
            conference: this.conference,
            division: this.division,
            affinity: this.affinity,
            region: this.region,
            losses: this.losses,
            wins: this.wins,
            score: this.score,
            ranking: this.ranking,
            customization: this.customization,
        };
    }
}

export class PremierTeam extends BasePremierTeam {
    constructor(team, res) {
        super(team);
        this.res = res;
    }

    async _superInit() {
        await super._init();
        if (this.unknown) return this.respond();
    }

    async _patch() {
        await this._superInit();
        if (this.unknown) return this.respond();
        await this._fetch();
        if (!this.API_ROSTER) return errorhandler({status: 500, res: this.res, errors: [{instance: 'riot'}]});
        if (!this.redis_premier_team_history && this.API_HISTORY) {
            await this._checkGames();
            await this._updateTeamDB();
        }
    }

    async _fetch() {
        this.redis_premier_team = await redis.get(`premier;team;${this.id}`);
        this.redis_premier_team_history = await redis.get(`premier;team;${this.id};history`);
        const request = (
            await Promise.allSettled([
                JSON.parse(this.redis_premier_team) ??
                    makeRequest({
                        url: `https://pd.${this.region}.a.pvp.net/premier/v1/rosters/${this.id}`,
                        region: this.region,
                        return_error: true,
                        res: this.res,
                    }),
                JSON.parse(this.redis_premier_team_history) ??
                    makeRequest({
                        url: `https://pd.${this.region}.a.pvp.net/premier/v1/rosters/${this.id}/matchhistory`,
                        region: this.region,
                        return_error: true,
                        res: this.res,
                    }),
            ])
        ).map(i => i.value);
        if (this.res.sent) return;
        if (request[0].code == 'ECONNABORTED')
            return errorhandler({
                status: 408,
                res: this.res,
                errors: [{instance: 'riot'}],
            });
        if (request[0].response)
            return errorhandler({
                status: request[0].response.status,
                res: this.res,
                errors: [{instance: 'riot'}],
            });
        this.API_ROSTER = request[0].data;
        this.API_HISTORY = request[1].data;
        if (!this.redis_premier_team && this.API_ROSTER) await redis.set(`premier;team;${this.id}`, JSON.stringify({data: this.API_ROSTER}), {EX: 300});
        if (!this.redis_premier_team_history && this.API_HISTORY) await redis.set(`premier;team;${this.id};history`, JSON.stringify({data: this.API_HISTORY}), {EX: 300});
        this.customization = {
            icon: this.API_ROSTER.customizationData.icon,
            primary: this.extractHexColorCode(this.API_ROSTER.customizationData.primaryColor),
            secondary: this.extractHexColorCode(this.API_ROSTER.customizationData.secondaryColor),
            tertiary: this.extractHexColorCode(this.API_ROSTER.customizationData.tertiaryColor),
        };
    }

    async _checkGames() {
        const unknown_games_league = this.API_HISTORY.leagueMatchHistory.filter(i => !this.matches.league.some(k => k.id == i.matchId));
        const unknown_games_tournament = this.API_HISTORY.tournamentMatchHistory.filter(i => !this.matches.tournament.some(k => k.tournament_id == i.tournamentId));
        const unfetched_matches = [];
        for (let i = 0; unknown_games_league.length > i; i++) {
            unfetched_matches.push(
                makeRequest({
                    url: `${base_url}/valorantlabs/v1/match/${this.region}/${unknown_games_league[i].matchId}`,
                    return_error: true,
                    proxy: false,
                })
            );
            this.matches.league.push({
                id: unknown_games_league[i].matchId,
                points_before: unknown_games_league[i].leaguePointsBefore,
                points_after: unknown_games_league[i].leaguePointsAfter,
                started_at: new Date(unknown_games_league[i].startTime).toISOString(),
            });
        }
        for (let i = 0; unknown_games_tournament.length > i; i++) {
            const tournament_matches = Object.keys(unknown_games_tournament[i].matchEntries);
            for (let k = 0; tournament_matches.length > k; k++) {
                unfetched_matches.push(
                    makeRequest({
                        url: `${base_url}/valorantlabs/v1/match/${this.region}/${tournament_matches[k]}`,
                        return_error: true,
                        proxy: false,
                    })
                );
            }
            this.matches.tournament.push({
                tournament_id: unknown_games_tournament[i].tournamentId,
                placement: unknown_games_tournament[i].finalPlacement,
                placement_league_bonus: unknown_games_tournament[i].finalPlacementLeaguePointsBonus,
                points_before: unknown_games_tournament[i].leaguePointsBefore,
                points_after: unknown_games_tournament[i].leaguePointsAfter,
                matches: tournament_matches,
            });
        }
        if (unfetched_matches.length) {
            const fetch = (await Promise.allSettled(unfetched_matches))
                .filter(i => i.value.data && !i.value.code && !i.value.response)
                .map(i => i.value.data.data)
                .sort((a, b) => b.metadata.game_start - a.metadata.game_start)[0];
            const member = fetch.teams.red.roster.id == this.id ? fetch.teams.red.roster.members : fetch.teams.blue.roster.members;
            if (member.filter(i => !this.member.some(k => k.puuid == i)).length)
                this.member = (
                    await makeRequest({
                        url: `https://pd.eu.a.pvp.net/name-service/v2/players`,
                        method: 'PUT',
                        body: member,
                        region: 'eu',
                        return_error: true,
                        res: this.res,
                    })
                )?.data
                    ?.filter(i => i.Subject.length)
                    ?.map(i => {
                        return {puuid: i.Subject, name: i.GameName ?? null, tag: i.TagLine ?? null};
                    });
        }
    }

    async _fetchHistory() {
        this.redis_premier_team_history = await redis.get(`premier;team;${this.id};history`);
        this.API_HISTORY = (
            JSON.parse(this.redis_premier_team_history) ??
            (await makeRequest({
                url: `https://pd.${this.region}.a.pvp.net/premier/v1/rosters/${this.id}/matchhistory`,
                region: this.region,
                return_error: true,
                res: this.res,
            }))
        ).data;
    }

    async _updateTeamDB() {
        await getDB('premier_teams').updateOne(
            {id: this.id, season: getPremierSeason()},
            {$set: {member: this.member, league_games: this.matches.league, tournament_games: this.matches.tournament}},
            {upsert: true}
        );
    }

    extractHexColorCode(str) {
        const rgbaValues = str.match(/R=(.*?),G=(.*?),B=(.*?),A=(.*?)\)/);
        const r = Math.round(parseFloat(rgbaValues[1]) * 255)
            .toString(16)
            .padStart(2, '0');
        const g = Math.round(parseFloat(rgbaValues[2]) * 255)
            .toString(16)
            .padStart(2, '0');
        const b = Math.round(parseFloat(rgbaValues[3]) * 255)
            .toString(16)
            .padStart(2, '0');
        return `#${r}${g}${b}`;
    }

    toJSON() {
        console.log(this.API_ROSTER);
        return {
            id: this.id,
            name: this.name,
            tag: this.tag,
            enrolled: this.API_ROSTER.isEnrolled,
            stats: {
                wins: this.API_ROSTER.wins,
                matches: this.API_ROSTER.gamesPlayed,
                losses: this.API_ROSTER.gamesPlayed - this.API_ROSTER.wins,
            },
            placement: {
                points: this.API_ROSTER.leaguePoints,
                conference: this.conference,
                division: this.division,
                place: this.ranking,
            },
            customization: {
                icon: this.customization.icon,
                image: `https://cdn.henrikdev.xyz/valorant/v1/premier/team-icon/${this.API_ROSTER.customizationData.icon}?primary=${this.customization.primary.replace(
                    '#',
                    ''
                )}&secondary=${this.customization.secondary.replace('#', '')}&tertiary=${this.customization.tertiary.replace('#', '')}`,
                primary: this.customization.primary,
                secondary: this.customization.secondary,
                tertiary: this.customization.tertiary,
            },
            member: this.member,
        };
    }

    respond() {
        if (this.res.sent) return;
        if (this.unknown) return errorhandler({status: 404, res: this.res, errors: [{instance: 'riot'}]});
        return this.res.code(200).send({
            status: 200,
            data: this.toJSON(),
        });
    }

    respondHistory() {
        return this.res.code(200).send({
            status: 200,
            data: {
                league_matches: this.matches.league,
                tournament_matches: this.matches.tournament,
            },
        });
    }
}

export class Conference {
    constructor(raw) {
        if (typeof raw == 'object') this.raw = raw;
        if (typeof raw == 'string') {
            const conferences = getPremierConferences();
            this.raw = conferences.find(i => i.key == raw.toUpperCase());
            if (!this.raw) throw Error('[PREMIER CONFERENCES] Unknown Conference');
        }
        this._init();
    }

    async _init() {
        const gamepods = getGamepods();
        this.id = this.raw.id;
        this.affinity = this.raw.affinity;
        this.pods = this.raw.gamePods.map(i => {
            return {pod: i, name: gamepods[i]};
        });
        this.region = this.raw.region;
        this.timezone = this.raw.timezone;
        this.name = this.raw.key;
    }

    toJSON() {
        return {
            id: this.id,
            affinity: this.affinity,
            pods: this.pods,
            region: this.region,
            timezone: this.timezone,
            name: this.name,
            icon: `https://cdn.henrikdev.xyz/valorant/v1/premier/conference/${this.id}`,
        };
    }
}

export class PremierEvent {
    constructor(raw) {
        this.raw = raw;
        this._init();
    }

    async _init() {
        const maps = getMaps();
        this.id = this.raw.ID;
        this.type = this.raw.Type;
        this.starts_at = this.raw.StartDateTime;
        this.ends_at = this.raw.EndDateTime;
        this.conference_schedules = Object.values(this.raw.SchedulePerConference).map(i => {
            return {
                conference: i.Conference,
                starts_at: i.StartDateTime,
                ends_at: i.EndDateTime,
            };
        });
        this.map_selection = {
            type: this.raw.MapSelectionStrategy,
            maps: this.raw.MapPoolMapIDs.map(i => {
                const map = maps.find(k => k.uuid == i);
                return {
                    name: map.displayName,
                    id: map.uuid,
                };
            }),
        };
        this.points_required_to_participate = this.raw.PointsRequiredToParticipate;
    }

    toJSON() {
        return {
            id: this.id,
            type: this.type,
            starts_at: this.starts_at,
            ends_at: this.ends_at,
            conference_schedules: this.conference_schedules,
            map_selection: this.map_selection,
            points_required_to_participate: this.points_required_to_participate,
        };
    }
}

export class PremierSeason {
    constructor(raw) {
        this.raw = raw;
        this._init();
    }

    async _init() {
        this.id = this.raw.id;
        this.championship_event_id = this.raw.ChampionshipEventID;
        this.championship_points_required = this.raw.ChampionshipPointRequirement;
        this.starts_at = this.raw.StartTime;
        this.ends_at = this.raw.EndTime;
        this.enrollment_starts_at = this.raw.EnrollmentPhaseStartDateTime;
        this.enrollment_ends_at = this.raw.EnrollmentPhaseEndDateTime;
        this.events = this.raw.Events.map(i => {
            return new PremierEvent(i).toJSON();
        });
        this.scheduled_events = (this.raw.ScheduledEvents ?? []).map(i => {
            return {
                event_id: i.EventID,
                conference: i.Conference,
                starts_at: i.StartDateTime,
                ends_at: i.EndDateTime,
            };
        });
    }

    toJSON() {
        return {
            id: this.id,
            championship_event_id: this.championship_event_id,
            championship_points_required: this.championship_points_required,
            starts_at: this.starts_at,
            ends_at: this.ends_at,
            enrollment_starts_at: this.enrollment_starts_at,
            enrollment_ends_at: this.enrollment_ends_at,
            events: this.events,
            scheduled_events: this.scheduled_events,
        };
    }
}
