use crate::structs::database::{ <PERSON>2<PERSON><PERSON>, B2MatchMeta, B2MatchPlayer, B2MatchTeam };
use crate::structs::enums::RiotPlatforms;
use crate::structs::http_clients::{ fetch_ipv6, FetchIPv6Options, b2_redis_fetch, B2RedisFetchOptions };
use crate::structs::parser::{ parse_match_v4 };
use crate::{ build_riot_headers, get_db, get_db_hdd, B2_AUTH, S3_CLIENT };
use futures::TryStreamExt;
use mongodb::bson::{ doc, from_document, to_document, Document };
use mongodb::{ bson, Client };
use redis::aio::MultiplexedConnection;
use serde::{ Deserialize, Serialize };
use serde_json::{ json, Value };
use std::sync::atomic::{ AtomicIsize, AtomicUsize };
use uuid::Uuid;
use valorant_api::response_types::matchdetails_v1::{ MatchDetailsV1 };

#[derive(Debug)]
pub struct RateLimiting {
	pub redis_cache_ttl: AtomicIsize,
	pub background_requests: AtomicUsize,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct RateLimitingWrapper<T> {
	pub background_requests: usize,
	pub data: T,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct GetMatchResult {
	pub id: String,
	pub error: bool,
	pub affinity: String,
	pub mode: String,
	pub status: u16,
	pub data: Option<MatchDetailsV1>,
	pub is_from_db: bool,
}

#[derive(Deserialize, Serialize, Debug)]
pub struct GetMatchFromDBBulkResult {
	pub error: bool,
	pub data: Vec<MatchDetailsV1>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CustomQueues {
	pub id: String,
	pub name: String,
	pub api: String,
}

pub async fn get_match_mongo(client: &Client, id: String, affinity: String) -> GetMatchResult {
	let matches_db = get_db_hdd::<Document>(client, "matches", None);
	let match_fetch = matches_db.find_one(doc! { "matchInfo.matchId": &id }).await;
	if match_fetch.is_ok() {
		let match_data = match_fetch.unwrap();
		if let Some(extracted) = match_data {
			let extracted = from_document::<MatchDetailsV1>(extracted).unwrap();
			return GetMatchResult {
				id: String::from(id),
				error: false,
				is_from_db: true,
				mode: String::from("mongo"),
				affinity: String::from(affinity),
				status: 200,
				data: Some(extracted),
			};
		}
	}
	let riot_headers = build_riot_headers(&RiotPlatforms::PC).await;
	let region = if ["br", "latam"].iter().any(|x| x == &affinity.to_lowercase()) { String::from("na") } else { affinity.to_lowercase() };
	let fetch_file = fetch_ipv6::<MatchDetailsV1>(FetchIPv6Options {
		url: format!("https://pd.{}.a.pvp.net/match-details/v1/matches/{}", region, id),
		headers: Some(riot_headers),
		..FetchIPv6Options::default()
	}).await;
	return if fetch_file.is_ok() {
		let data = fetch_file.unwrap();
		let mut val = json!(data.data);
		transform_uuids(&mut val);
		let _ = get_db_hdd::<Document>(client, "matches", None)
			.update_one(doc! { "matchInfo.matchId": &id }, doc! { "$setOnInsert": to_document(&val).unwrap() })
			.upsert(true).await
			.expect("Error inserting document");
		GetMatchResult {
			id: String::from(id),
			error: false,
			mode: String::from("http"),
			affinity: String::from(affinity),
			status: data.status,
			data: Some(data.data),
			is_from_db: false,
		}
	} else if fetch_file.is_err() && fetch_file.unwrap_err().status == 404 {
		GetMatchResult {
			id: String::from(id),
			error: true,
			mode: String::from("http"),
			affinity: String::from(affinity),
			status: 404,
			data: None,
			is_from_db: false,
		}
	} else {
		GetMatchResult {
			id: String::from(id),
			error: true,
			mode: String::from("http"),
			affinity: String::from(affinity),
			status: 500,
			data: None,
			is_from_db: false,
		}
	};
}
pub async fn get_matches_mongo_only_bulk(client: &Client, query: Document, size: i64, skip: u64, sort: Document) -> GetMatchFromDBBulkResult {
	let matches_db = get_db_hdd::<Document>(client, "matches", None);
	let match_fetch = matches_db.find(query).limit(size).sort(sort).skip(skip).await;
	if let Ok(m_f) = match_fetch {
		let collected: Vec<Document> = m_f.try_collect().await.unwrap_or_else(|e| {
			eprintln!("Error collecting documents from MongoDB: {:?}", e);
			vec![]
		});
		let parse_to_matches = collected
			.iter()
			.map(|x| from_document::<MatchDetailsV1>(x.clone()).unwrap())
			.collect::<Vec<MatchDetailsV1>>();
		GetMatchFromDBBulkResult {
			error: false,
			data: parse_to_matches,
		}
	} else {
		GetMatchFromDBBulkResult {
			error: true,
			data: vec![],
		}
	}
}
pub async fn save_match_mongo(client: &Client, id: &String, affinity: &String) -> GetMatchResult {
	let matches_db = get_db_hdd::<Document>(client, "matches", None);
	let match_fetch = matches_db.count_documents(doc! { "matchInfo.matchId": &id }).await;
	if let Ok(c) = match_fetch {
		if c > 0 {
			return GetMatchResult {
				id: String::from(id),
				error: false,
				is_from_db: true,
				mode: String::from("mongo"),
				affinity: String::from(affinity),
				status: 200,
				data: None,
			};
		}
	}
	let riot_headers = build_riot_headers(&RiotPlatforms::PC).await;
	let region = if ["br", "latam"].iter().any(|x| x == &affinity.to_lowercase()) { String::from("na") } else { affinity.to_lowercase() };
	let fetch_file = fetch_ipv6::<MatchDetailsV1>(FetchIPv6Options {
		url: format!("https://pd.{}.a.pvp.net/match-details/v1/matches/{}", region, id),
		headers: Some(riot_headers),
		..FetchIPv6Options::default()
	}).await;
	if fetch_file.is_ok() {
		let data = fetch_file.unwrap();
		let m_data = to_document(&data.data).unwrap();
		println!("[INSERT] Inserting match {} into the database", id);
		let _ = get_db_hdd::<Document>(client, "matches", None)
			.update_one(doc! { "matchInfo.matchId": &id }, doc! { "$setOnInsert": m_data })
			.upsert(true).await
			.expect("Error inserting document");
		GetMatchResult {
			id: String::from(id),
			error: false,
			mode: String::from("http"),
			affinity: String::from(affinity),
			status: data.status,
			data: None,
			is_from_db: false,
		}
	} else if fetch_file.is_err() && fetch_file.unwrap_err().status == 404 {
		GetMatchResult {
			id: String::from(id),
			error: true,
			mode: String::from("http"),
			affinity: String::from(affinity),
			status: 404,
			data: None,
			is_from_db: false,
		}
	} else {
		GetMatchResult {
			id: String::from(id),
			error: true,
			mode: String::from("http"),
			affinity: String::from(affinity),
			status: 500,
			data: None,
			is_from_db: false,
		}
	}
}

pub async fn get_match_b2(client: &Client, redis_client: MultiplexedConnection, id: String, affinity: String) -> GetMatchResult {
	let match_metadata_db = get_db::<B2Match>(client, "match_metadata_b2", None);
	let metadata_fetch = match_metadata_db.find_one(doc! { "m_id": &id, "b2_error": false }).await;

	if let Ok(Some(_)) = metadata_fetch {
		return fetch_from_b2(id.clone(), affinity.clone(), redis_client, None).await;
	}

	let res = save_match_b2(client, redis_client, &id, &affinity, None).await;
	res
}

pub async fn fetch_from_b2(id: String, affinity: String, redis_client: MultiplexedConnection, ttl: Option<i32>) -> GetMatchResult {
	let auth_token = {
		let b2_auth = B2_AUTH.load_full();
		b2_auth.authorization_token.clone()
	};

	let download_url = format!("https://cdn-b2.henrikdev.xyz/matches/{}.json?Authorization={}", id, auth_token);
	let cache_key = format!("b2:match:{}", id);

	let options = B2RedisFetchOptions {
		url: download_url,
		method: "GET".to_string(),
		data: serde_json::Value::Null,
		store: cache_key,
		redis_client: Some(redis_client),
		use_b2_auth: false, // Auth is in URL
		custom_ttl: ttl,
		pass_data: None,
	};

	match b2_redis_fetch(options).await {
		Ok(response) => {
			GetMatchResult {
				id: id.clone(),
				error: false,
				is_from_db: response.is_from_redis,
				mode: String::from("b2_cached"),
				affinity,
				status: response.status,
				data: Some(response.data),
			}
		}
		Err(e) => {
			GetMatchResult {
				id: id.clone(),
				error: true,
				mode: String::from("b2_cached"),
				affinity,
				status: e.status,
				data: None,
				is_from_db: false,
			}
		}
	}
}

pub async fn get_matches_b2_only_bulk(
	client: &Client,
	query: Document,
	size: i64,
	skip: u64,
	sort: Document,
	redis_client: MultiplexedConnection,
	ttl: Option<i32>
) -> GetMatchFromDBBulkResult {
	let match_metadata_db = get_db::<B2Match>(client, "match_metadata_b2", None);
	let metadata_fetch = match_metadata_db.find(query).limit(size).sort(sort).skip(skip).await;

	if let Ok(cursor) = metadata_fetch {
		let metadata_docs: Vec<B2Match> = cursor.try_collect().await.unwrap_or_else(|e| {
			eprintln!("Error collecting match metadata documents: {:?}", e);
			vec![]
		});

		if metadata_docs.is_empty() {
			return GetMatchFromDBBulkResult {
				error: false,
				data: vec![],
			};
		}

		let auth_token = {
			let b2_auth = B2_AUTH.load_full();
			b2_auth.authorization_token.clone()
		};

		let mut match_details = Vec::with_capacity(metadata_docs.len());

		for metadata in metadata_docs {
			if metadata.b2_error {
				// refetch from b2
				save_match_b2(client, redis_client.clone(), &metadata.m_id, &metadata.meta.p, None).await;
			}
			let download_url = format!("https://cdn-b2.henrikdev.xyz/matches/{}.json?Authorization={}", metadata.m_id, auth_token);
			let cache_key = format!("b2:match:{}", metadata.m_id);

			let options = B2RedisFetchOptions {
				url: download_url,
				method: "GET".to_string(),
				data: serde_json::Value::Null,
				store: cache_key,
				redis_client: Some(redis_client.clone()),
				use_b2_auth: false, // Auth is in URL
				custom_ttl: ttl,
				pass_data: None,
			};

			if let Ok(response) = b2_redis_fetch::<MatchDetailsV1>(options).await {
				match_details.push(response.data);
			}
		}

		GetMatchFromDBBulkResult {
			error: false,
			data: match_details,
		}
	} else {
		GetMatchFromDBBulkResult {
			error: true,
			data: vec![],
		}
	}
}

pub async fn save_match_b2(client: &Client, redis_client: MultiplexedConnection, id: &String, affinity: &String, match_data: Option<MatchDetailsV1>) -> GetMatchResult {
	let match_metadata_db = get_db::<B2Match>(client, "match_metadata_b2", None);
	if let Ok(Some(_)) = match_metadata_db.find_one(doc! { "m_id": id }).await {
		let f_from_b2 = fetch_from_b2(id.clone(), affinity.clone(), redis_client, None).await;
		return f_from_b2;
	}

	let mut status = 200;
	let match_data = if let Some(data) = match_data {
		data
	} else {
		let riot_headers = build_riot_headers(&RiotPlatforms::PC).await;
		let region = if ["br", "latam"].iter().any(|x| x == &affinity.to_lowercase()) { String::from("na") } else { affinity.to_lowercase() };
		let fetch_file = fetch_ipv6::<MatchDetailsV1>(FetchIPv6Options {
			url: format!("https://pd.{}.a.pvp.net/match-details/v1/matches/{}", region, id),
			headers: Some(riot_headers),
			..FetchIPv6Options::default()
		}).await;
		if let Ok(data) = fetch_file {
			status = data.status;
			data.data
		} else if let Err(e) = fetch_file {
			return GetMatchResult {
				id: String::from(id),
				error: true,
				mode: String::from("http"),
				affinity: String::from(affinity),
				status: e.status,
				data: None,
				is_from_db: false,
			};
		} else {
			return GetMatchResult {
				id: String::from(id),
				error: true,
				mode: String::from("http"),
				affinity: String::from(affinity),
				status: 500,
				data: None,
				is_from_db: false,
			};
		}
	};

	let match_data_bytes = serde_json::to_vec(&match_data).unwrap();
	let compressed_data = zstd::stream::encode_all(&match_data_bytes[..], 15).unwrap();

	let s3_client = S3_CLIENT.load_full();
	let b2_data = B2_AUTH.load_full();
	//let bucket_name = &b2_data.bucket_id;
	let bucket_name = "henrikdev";

	let file_path = format!("matches/{}.json", id);

	let mut attempts = 0;
	let max_attempts = 3;
	let s3_req = loop {
		attempts += 1;
		match s3_client.put_object(bucket_name, &file_path, bytes::Bytes::from(compressed_data.clone())).await {
			Ok(v) => {
				break Ok(v);
			}
			Err(e) if attempts < max_attempts => {
				// Optional: kurze Pause zwischen den Versuchen
				tokio::time::sleep(std::time::Duration::from_millis(200)).await;
			}
			Err(e) => {
				eprintln!("[ERROR] Migration failed: {} | Match ID: {}", e, id);
				break Err(e);
			}
		}
	};

	if cfg!(debug_assertions) {
		println!("[B2] Uploading match {:?} to S3", s3_req);
	}

	let match_info = match_data.match_info.clone();
	let copy = match_data.clone();
	let v4_parse = parse_match_v4(copy, affinity).await;
	let metadata = B2Match {
		m_id: id.clone(),
		b2_error: s3_req.is_err(),
		meta: B2MatchMeta {
			map_id: match_info.map_id,
			pod_id: match_info.game_pod_id,
			v: match_info.game_version,
			l_ms: match_info.game_length_millis.unwrap_or(0),
			s_ms: match_info.game_start_millis,
			q: if let Some(queue) = match_info.queue_id {
				queue.to_string()
			} else {
				"custom".to_string()
			},
			g: match_info.game_mode,
			s_id: match_info.season_id.to_string(),
			p: match_info.platform_type,
			p_s: if let Some(premier) = &match_info.premier_match_info {
				if let Some(id) = premier.premier_season_id { Some(id.to_string()) } else { None }
			} else {
				None
			},
			p_e: if let Some(premier) = &match_info.premier_match_info {
				if let Some(id) = premier.premier_event_id { Some(id.to_string()) } else { None }
			} else {
				None
			},
			p_t: None,
			p_m: None,
		},
		players: v4_parse.players
			.into_iter()
			.map(|x| B2MatchPlayer {
				id: x.puuid,
				t: x.team_id,
				c: x.agent.id,
				p: x.party_id,
				s: x.stats.score,
				k: x.stats.kills,
				d: x.stats.deaths,
				a: x.stats.assists,
				head: x.stats.headshots,
				body: x.stats.bodyshots,
				leg: x.stats.legshots,
				a_g: x.ability_casts.grenade.unwrap_or(0),
				a_1: x.ability_casts.ability1.unwrap_or(0),
				a_2: x.ability_casts.ability2.unwrap_or(0),
				a_u: x.ability_casts.ultimate.unwrap_or(0),
				tier: x.tier.id,
				l: x.account_level,
				d_d: x.stats.damage.dealt,
				d_r: x.stats.damage.received,
			})
			.collect::<Vec<B2MatchPlayer>>(),
		teams: v4_parse.teams
			.into_iter()
			.map(|x| B2MatchTeam {
				id: x.team_id,
				r_id: if let Some(premier_roster) = x.premier_roster {
					Some(premier_roster.id)
				} else {
					None
				},
				p: x.rounds.won,
			})
			.collect::<Vec<B2MatchTeam>>(),
	};
	let _ = match_metadata_db.update_one(doc! { "m_id": id }, doc! { "$set": bson::to_document(&metadata).unwrap() }).upsert(true).await;
	if cfg!(debug_assertions) {
		println!("[INSERT] Inserting match {} into the database", id);
	}

	GetMatchResult {
		id: String::from(id),
		error: false,
		mode: String::from("http"),
		affinity: String::from(affinity),
		status,
		data: Some(match_data),
		is_from_db: false,
	}
}

pub fn transform_uuids(value: &mut Value) {
	match value {
		Value::Object(map) => {
			for (k, v) in map.iter_mut() {
				if k == "uuid" {
					if let Value::String(s) = v {
						if let Ok(uuid) = Uuid::parse_str(s) {
							*v = Value::String(uuid.to_string());
						}
					}
				} else {
					transform_uuids(v);
				}
			}
		}
		Value::Array(arr) => {
			for v in arr.iter_mut() {
				transform_uuids(v);
			}
		}
		_ => {}
	}
}

pub fn reverse_transform_uuids(value: &mut Value) {
	match value {
		Value::Object(map) => {
			for (k, v) in map.iter_mut() {
				if k == "uuid" {
					if let Value::String(s) = v {
						if let Ok(uuid) = Uuid::parse_str(s) {
							// Replace the string with the UUID
							*v = Value::String(uuid.to_string());
						}
					}
				} else {
					reverse_transform_uuids(v);
				}
			}
		}
		Value::Array(arr) => {
			for v in arr.iter_mut() {
				reverse_transform_uuids(v);
			}
		}
		_ => {}
	}
}
