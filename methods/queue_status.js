import {getQueues, tiers, getMaps} from '../server.js';

export class ValorantQueueStatus {
    constructor(raw) {
        this.raw = raw;
        this.queues = [];
        this.queues_map = getQueues();
        this.maps = getMaps();
        this.tiers = tiers;
        this._parse();
    }
    _parse() {
        this.queues = this.raw.Queues.map(i => {
            return {
                mode: this.queues_map.find(k => k.id == i.QueueID.split('-')[0]).name,
                mode_id: i.QueueID,
                enabled: i.Enabled,
                team_size: i.TeamSize,
                number_of_teams: i.NumTeams,
                party_size: {
                    max: i.MaxPartySize,
                    min: i.MinPartySize,
                    invalid: i.InvalidPartySizes,
                    full_party_bypass: i.AllowFullPartyBypassSkillRestrictions,
                },
                high_skill: {
                    max_party_size: i.MaxPartySizeHighSkill,
                    min_tier: i.HighSkillTier,
                    max_tier: i.MaxSkillTier,
                },
                ranked: i.IsRanked,
                tournament: i.IsTournament,
                skill_disparity: this.createDisparityObject(i.PartySkillDisparityCompetitiveTiersCeilings),
                required_account_level: i.MinimumAccountLevelRequired,
                game_rules: {
                    overtime_win_by_two: i.GameRules.IsOvertimeWinByTwo == 'true' ?? false,
                    allow_lenient_surrender: i.GameRules.AllowLenientSurrender == 'true' ?? false,
                    allow_drop_out: i.GameRules.AllowDropOut == 'true' ?? false,
                    assign_random_agents: i.GameRules.AssignRandomAgents == 'true' ?? false,
                    skip_pregame: i.GameRules.SkipPregame == 'true' ?? false,
                    allow_overtime_draw_vote: i.GameRules.AllowOvertimeDrawVote == 'true' ?? false,
                    overtime_win_by_two_capped: i.GameRules.IsOvertimeWinByTwoCapped == 'true' ?? false,
                    premier_mode: i.GameRules.PremierTournamentMode == 'true' ?? false,
                },
                platforms: i.SupportedPlatformTypes,
                maps: this.createMaps(i.MapWeights),
            };
        });
    }
    createMaps(weights) {
        return weights.map(i => {
            const split = i.split(':');
            const map = this.maps.find(k => k.mapUrl.split('/')[4] == split[0]);
            return {
                map: {
                    id: map.uuid,
                    name: map.displayName,
                },
                enabled: split[1] == '1',
            };
        });
    }
    createDisparityObject(disparity) {
        const tiers = Object.keys(disparity);
        let matching = tiers.map((i, index) => {
            return {
                tier: Number(i),
                name: this.tiers[i],
                max_tier: {
                    id: disparity[index] < 0 ? 0 : Number(disparity[index]),
                    name: this.tiers[disparity[index] < 0 ? 0 : disparity[index]],
                },
            };
        });
        return matching;
    }
    toJSON() {
        return this.queues;
    }
}
