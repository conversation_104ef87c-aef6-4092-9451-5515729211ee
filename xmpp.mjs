import {default as Fastify} from "fastify";
const fastify = Fastify({logger: {level: "error"}})
import {ValorantSessionManager, Regions, XMPPRegions, Logger} from "./tests/valorant-session/src/valorant.js";
const delay = ms => new Promise(res => setTimeout(res, ms));
import moment from "moment"

const array = [{id: 0, use: false, timestamp: moment().unix()}, {id: 1, use: false, timestamp: moment().unix()}, {id: 2, use: false, timestamp: moment().unix()}, {id: 3, use: false, timestamp: moment().unix()}, {id: 4, use: false, timestamp: moment().unix()}, {id: 5, use: false, timestamp: moment().unix()}, {id: 6, use: false, timestamp: moment().unix()}, {id: 7, use: false, timestamp: moment().unix()}, {id: 8, use: false, timestamp: moment().unix()}]

const accounts = [
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.EuropeNordicAndEast,
        username: "<PERSON><PERSON>I1",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Turkey,
        username: "HenrikAPI2",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Turkey,
        username: "HenrikAPI3",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Russia,
        username: "HenrikAPI4",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Russia,
        username: "HenrikAPI5",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Russia,
        username: "HenrikAPI6",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.EuropeNordicAndEast,
        username: "HenrikAPI7",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Russia,
        username: "HenrikAPI8",
        password: "Duisburg02@"
    },
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Turkey,
        username: "HenrikAPI9",
        password: "Duisburg02@"
    }
]
const acc = [
    {
        region: Regions.Europe,
        xmppRegion: XMPPRegions.Turkey,
        username: "HenrikAPI10",
        password: "Duisburg02@"
    }
]

setInterval(() => {
    for(let i = 0; array.length > i; i++) {
        if(array[i].use && moment().unix() - array[i].timestamp > 10) array[i].use = false; array[i].timestamp = moment().unix()
    }
}, 30000)

let accountInstances = new Array();
let startup = false

fastify.get("/:name/:tag", async (req, res) => {
    if(startup == false) return res.code(500).send({error: "startup"})
    let instance = array.find(item => item.use == false)
    let index
    let data
    if(instance) {
        index = array.findIndex(item => item == instance)
        array[index].use = true
        data = await accountInstances[instance.id].xmpp.getPuuidFromName(req.params.name, req.params.tag)
        array[index].use = false
        array[index].timestamp = moment().unix()
    }
    const intervalcheck = async () => {
        return new Promise((resolve, reject) => {
            let i = 0
            const check = setInterval(() => {
                const find = array.find(item => item.use == false)
                i++
                if(find) {
                    clearInterval(check)
                    resolve(find)
                }
                if(i >= 100) {
                    clearInterval(check)
                    resolve(429)
                }
            }, 50)
        })
    }
    if(instance == undefined) {
        instance = await intervalcheck()
        if(instance == 429) return res.code(429).send("timeout")
        index = array.findIndex(item => item == instance)
        array[index].use = true
        data = await accountInstances[instance.id].xmpp.getPuuidFromName(req.params.name, req.params.tag)
        array[index].use = false
        array[index].timestamp = moment().unix()
    }
    console.log(data.raw?.error)
    const codes = {
        "Not Found": 404,
        "Server Timeout": 409
    }
    codes[data] != undefined ? res.code(codes[data]).send(data) : res.code(200).send(data)
})

fastify.get("/decline/:id", async (req, res) => {
    if(startup == false) return 500
    const data = await accountInstances[0].xmpp.declineFriendRequestPUUID(req.params.id)
    const codes = {
        "Not Found": 404,
        "Server Timeout": 409
    }
    codes[data] != undefined ? res.code(codes[data]).send(data) : res.code(200).send(data)
})

fastify.get("/status", async (req, res) => {
    res.send(array)
})

fastify.get("/getfriends", async (req, res) => {
    if(startup == false) return 500
    const data = await accountInstances[0].xmpp.fetchFriendsList()
    console.log(data)
    const codes = {
        "Not Found": 404,
        "Server Timeout": 409
    }
    codes[data] != undefined ? res.code(codes[data]).send(data) : res.code(200).send(data)
})

fastify.listen(3700, "0.0.0.0", async () => {
    await Promise.all(accounts.map(async a => {
        const {region, xmppRegion, username, password} = a;
        const vsm = new ValorantSessionManager();
        vsm.login(region, xmppRegion, username, password)
        vsm.on('ready', async (auth, xmpp) => {
            accountInstances.push({auth, xmpp})
            startup = true
        })
    }))
    console.log("API Server Ready")
    console.log(`Example app listening at http://localhost:3700`)
})