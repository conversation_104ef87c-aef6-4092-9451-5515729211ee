mod main_v2;

use axum::body::Body;
use axum::http::{method, HeaderMap, Method, StatusCode, Uri};
use axum::response::Response;
use axum::routing::post;
use axum::{<PERSON><PERSON>, Router};
use log::info;
use mimalloc::MiMalloc;
use netdev::get_interfaces;
use rand::Rng;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{IpAddr, SocketAddr, SocketAddrV6};
use std::str::FromStr;

#[global_allocator]
static GLOBAL: MiMalloc = MiMalloc;

#[derive(Debug, Serialize, Deserialize)]
struct ProxyRequest {
    url: String,
    method: String,
    headers: HashMap<String, String>,
    body: serde_json::Value,
}

static DEFAULT_IP_RANGE: &str = "2a01:4f8:1c17:607b";
static RIOT_PVP_NET_ADDRESS: &str = "2606:4700:4400::6812:22D2";
static RIOT_ACCOUNTS_NET_ADDRESS: &str = "2606:4700:4400::6812:23D2";
static AUTH_NET_ADDRESS: &str = "2606:4700:4400::6810:7732";

fn get_random_between(min: i32, max: i32) -> i32 {
    let mut rng = rand::thread_rng();
    rng.gen_range(min..max)
}

// Handler for proxying requests
async fn proxy_handler(Json(req): Json<ProxyRequest>) -> Response {
    if cfg!(debug_assertions) {
        let client = Client::builder().build().unwrap();

        // Determine the HTTP method
        let method = match req.method.to_lowercase().as_str() {
            "get" => reqwest::Method::GET,
            "post" => reqwest::Method::POST,
            "put" => reqwest::Method::PUT,
            "delete" => reqwest::Method::DELETE,
            _ => reqwest::Method::GET, // Default to GET if method is unknown
        };
        println!("method: {:?}", method);

        // Prepare the outgoing request
        let mut request_builder = client.request(method, &req.url);
        println!("request_builder: {:?}", request_builder);

        // Add headers to the request
        for (key, value) in req.headers.iter() {
            request_builder = request_builder.header(key, value);
        }

        // Set the body if provided
        if let serde_json::Value::Null = req.body {
        } else {
            request_builder = request_builder.json(&req.body);
        }

        // Send the request
        match request_builder.send().await {
            Ok(response) => {
                let status = response.status();
                let headers = response.headers().clone();
                match response.bytes().await {
                    Ok(bytes) => {
                        let mut resp = Response::builder()
                            .status(StatusCode::from_u16(status.as_u16()).unwrap());
                        let _ = resp
                            .headers_mut()
                            .unwrap()
                            .append("IP-Used", "test".parse().unwrap());
                        for (key, value) in headers.iter() {
                            let _ = resp
                                .headers_mut()
                                .unwrap()
                                .append(key.clone(), value.clone());
                        }
                        resp.body(Body::from(bytes)).unwrap()
                    }
                    Err(e) => Response::builder()
                        .status(StatusCode::INTERNAL_SERVER_ERROR)
                        .body(Body::from(format!("Failed to read response body: {}", e)))
                        .unwrap(),
                }
            }
            Err(e) => {
                println!("Request failed: {}", e);
                Response::builder()
                    .status(StatusCode::INTERNAL_SERVER_ERROR)
                    .body(Body::from(format!("Request failed: {}", e)))
                    .unwrap()
            }
        }
    } else {
        let interfaces = get_interfaces();

        let available_interfaces = interfaces
            .iter()
            .find(|interface| interface.ipv6.len() > 10);
        if available_interfaces.is_none() {
            return Response::builder()
                .status(StatusCode::INTERNAL_SERVER_ERROR)
                .body(Body::from("No suitable interface found"))
                .unwrap();
        }
        let interface = available_interfaces.unwrap();
        let ips = interface
            .ipv6
            .iter()
            .filter(|ip| ip.addr.to_string().starts_with(DEFAULT_IP_RANGE))
            .collect::<Vec<_>>();
        if ips.len() == 0 {
            return Response::builder()
                .status(StatusCode::INTERNAL_SERVER_ERROR)
                .body(Body::from("No suitable IP found"))
                .unwrap();
        }
        let amount_ips = ips.len();
        let random_ip = get_random_between(0, amount_ips as i32);

        let ip = ips[random_ip as usize];

        let client = Client::builder()
            .resolve(
                "pd.eu.a.pvp.net",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "pd.na.a.pvp.net",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "pd.kr.a.pvp.net",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "pd.ap.a.pvp.net",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "api.account.riotgames.com",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "eu.api.account.riotgames.com",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "us.api.account.riotgames.com",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "sea.api.account.riotgames.com",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "ap.api.account.riotgames.com",
                SocketAddr::from(SocketAddrV6::new(
                    RIOT_PVP_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "auth.riotgames.com",
                SocketAddr::from(SocketAddrV6::new(
                    AUTH_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .resolve(
                "authenticate.riotgames.com",
                SocketAddr::from(SocketAddrV6::new(
                    AUTH_NET_ADDRESS.parse().unwrap(),
                    443,
                    0,
                    0,
                )),
            )
            .local_address(IpAddr::from_str(&ip.addr.to_string().as_str()).unwrap())
            .build()
            .unwrap();
        // Determine the HTTP method
        let method = match req.method.to_lowercase().as_str() {
            "get" => reqwest::Method::GET,
            "post" => reqwest::Method::POST,
            "put" => reqwest::Method::PUT,
            "delete" => reqwest::Method::DELETE,
            _ => reqwest::Method::GET, // Default to GET if method is unknown
        };

        // Prepare the outgoing request
        let mut request_builder = client.request(method, &req.url);

        // Add headers to the request
        for (key, value) in req.headers.iter() {
            request_builder = request_builder.header(key, value);
        }

        // Set the body if provided
        if let serde_json::Value::Null = req.body {
        } else {
            request_builder = request_builder.json(&req.body);
        }

        // Send the request
        match request_builder.send().await {
            Ok(response) => {
                let status = response.status();
                let headers = response.headers().clone();
                match response.bytes().await {
                    Ok(bytes) => {
                        let mut resp = Response::builder()
                            .status(StatusCode::from_u16(status.as_u16()).unwrap());
                        resp.headers_mut()
                            .unwrap()
                            .append("IP-Used", ip.addr.to_string().parse().unwrap());
                        resp.headers_mut()
                            .unwrap()
                            .append("URL", req.url.as_str().parse().unwrap());
                        resp.body(Body::from(bytes)).unwrap()
                    }
                    Err(e) => Response::builder()
                        .status(StatusCode::INTERNAL_SERVER_ERROR)
                        .body(Body::from(format!("Failed to read response body: {}", e)))
                        .unwrap(),
                }
            }
            Err(e) => {
                println!("Request failed: {}", e);
                Response::builder()
                    .status(StatusCode::INTERNAL_SERVER_ERROR)
                    .body(Body::from(format!("Request failed: {}", e)))
                    .unwrap()
            }
        }
    }
}

#[tokio::main]
async fn main() {
    println!("Starting proxy server");
    let listener = tokio::net::TcpListener::bind("0.0.0.0:7000").await.unwrap();
    let app = Router::new().route("/proxy", post(proxy_handler));

    axum::serve(listener, app).await.unwrap();
}
