use hyper::service::{ make_service_fn, service_fn };
use hyper::{ Body, Request, Response, Server, StatusCode };
use log::{ error, info, warn };
use mimalloc::MiMalloc;
use reqwest::{ header::{ Header<PERSON><PERSON>, HeaderValue }, Client };
use std::collections::{ HashMap, HashSet };
use std::convert::Infallible;
use std::env;
use std::net::{ IpAddr, Ipv6Addr, SocketAddr, SocketAddrV6 };
use std::str::FromStr;
use std::sync::{ Arc, Mutex };
use std::time::{ Duration, Instant };
use tokio::sync::RwLock;
use dotenv::dotenv;

#[global_allocator]
static GLOBAL: MiMalloc = MiMalloc;

// Proxy configuration
struct ProxyConfig {
	ip_range: String,
	riot_pvp_net_address: Ipv6Addr,
	riot_accounts_net_address: Ipv6Addr,
	auth_net_address: Ipv6Add<PERSON>,
	blocked_ips: HashSet<String>,
	blocked_domains: HashSet<String>,
	request_counter: HashMap<String, u32>,
	request_timestamps: HashMap<String, Instant>,
	auth_system: AuthSystem,
}

struct Credentials {
	username: String,
	password: String,
}

struct AuthSystem {
	valid_credentials: Vec<Credentials>,
}

impl AuthSystem {
	fn new() -> Self {
		Self {
			valid_credentials: vec![
				Credentials {
					username: "user1".to_string(),
					password: "pass1".to_string(),
				},
				Credentials {
					username: "user2".to_string(),
					password: "pass2".to_string(),
				}
			],
		}
	}

	fn is_valid(&self, auth_header: Option<&hyper::http::HeaderValue>) -> bool {
		if let Some(auth) = auth_header {
			if let Ok(auth_str) = auth.to_str() {
				if auth_str.starts_with("Basic ") {
					let encoded = &auth_str["Basic ".len()..];
					if let Ok(decoded) = base64::decode(encoded) {
						if let Ok(auth_string) = String::from_utf8(decoded) {
							if let Some(separator_pos) = auth_string.find(':') {
								let username = &auth_string[..separator_pos];
								let password = &auth_string[separator_pos + 1..];

								return self.valid_credentials.iter().any(|c| c.username == username && c.password == password);
							}
						}
					}
				}
			}
		}
		false
	}
}

impl ProxyConfig {
	fn new() -> Self {
		Self {
			ip_range: "2a01:4f8:1c17:607b".to_string(),
			riot_pvp_net_address: "2606:4700:4400::6812:22D2".parse().unwrap(),
			riot_accounts_net_address: "2606:4700:4400::6812:23D2".parse().unwrap(),
			auth_net_address: "2606:4700:4400::6810:7732".parse().unwrap(),
			blocked_ips: HashSet::new(),
			blocked_domains: HashSet::new(),
			request_counter: HashMap::new(),
			request_timestamps: HashMap::new(),
			auth_system: AuthSystem::new(),
		}
	}

	fn is_domain_blocked(&self, domain: &str) -> bool {
		self.blocked_domains.contains(domain)
	}

	fn is_ip_blocked(&self, ip: &str) -> bool {
		self.blocked_ips.contains(ip)
	}

	fn record_request(&mut self, client_ip: &str) -> bool {
		let now = Instant::now();

		// Clean up old entries (older than 1 minute)
		self.request_timestamps.retain(|_, timestamp| now.duration_since(*timestamp) < Duration::from_secs(60));

		// Reset counters for IPs that haven't been seen in a while
		let ips_to_reset: Vec<String> = self.request_counter
			.keys()
			.filter(|ip| !self.request_timestamps.contains_key(*ip))
			.cloned()
			.collect();

		for ip in ips_to_reset {
			self.request_counter.remove(&ip);
		}

		// Record this request
		let count = self.request_counter.entry(client_ip.to_string()).or_insert(0);
		*count += 1;
		self.request_timestamps.insert(client_ip.to_string(), now);

		// Rate limit: 100 requests per minute per IP
		*count <= 100
	}
}

// Select a random IP address from the available IPv6 addresses
fn select_random_ip(config: &ProxyConfig) -> Result<String, &'static str> {
	let interfaces = netdev::get_interfaces();

	// Find an interface with IPv6 addresses
	let interface = interfaces
		.iter()
		.find(|interface| interface.ipv6.len() > 10)
		.ok_or("No suitable interface found")?;

	// Filter IP addresses by the configured range
	let ips = interface.ipv6
		.iter()
		.filter(|ip| ip.addr.to_string().starts_with(&config.ip_range))
		.collect::<Vec<_>>();

	if ips.is_empty() {
		return Err("No suitable IP found");
	}

	// Choose a random IP
	let amount_ips = ips.len();
	let random_ip_index = rand::random::<usize>() % amount_ips;
	let ip = &ips[random_ip_index];

	Ok(ip.addr.to_string())
}

// Create a reqwest client with IPv6 local address binding and custom DNS resolution
fn create_client(ip_str: &str, config: &ProxyConfig) -> Result<Client, Box<dyn std::error::Error>> {
	let ip_addr = IpAddr::from_str(ip_str)?;

	let mut client_builder = Client::builder()
		// Riot PVP.NET addresses
		.resolve("pd.eu.a.pvp.net", SocketAddr::from(SocketAddrV6::new(config.riot_pvp_net_address, 443, 0, 0)))
		.resolve("pd.na.a.pvp.net", SocketAddr::from(SocketAddrV6::new(config.riot_pvp_net_address, 443, 0, 0)))
		.resolve("pd.kr.a.pvp.net", SocketAddr::from(SocketAddrV6::new(config.riot_pvp_net_address, 443, 0, 0)))
		.resolve("pd.ap.a.pvp.net", SocketAddr::from(SocketAddrV6::new(config.riot_pvp_net_address, 443, 0, 0)))
		// Riot Account API addresses
		.resolve("api.account.riotgames.com", SocketAddr::from(SocketAddrV6::new(config.riot_accounts_net_address, 443, 0, 0)))
		.resolve("eu.api.account.riotgames.com", SocketAddr::from(SocketAddrV6::new(config.riot_accounts_net_address, 443, 0, 0)))
		.resolve("us.api.account.riotgames.com", SocketAddr::from(SocketAddrV6::new(config.riot_accounts_net_address, 443, 0, 0)))
		.resolve("sea.api.account.riotgames.com", SocketAddr::from(SocketAddrV6::new(config.riot_accounts_net_address, 443, 0, 0)))
		.resolve("ap.api.account.riotgames.com", SocketAddr::from(SocketAddrV6::new(config.riot_accounts_net_address, 443, 0, 0)))
		// Auth addresses
		.resolve("auth.riotgames.com", SocketAddr::from(SocketAddrV6::new(config.auth_net_address, 443, 0, 0)))
		.resolve("authenticate.riotgames.com", SocketAddr::from(SocketAddrV6::new(config.auth_net_address, 443, 0, 0)))
		.local_address(ip_addr);

	Ok(client_builder.build()?)
}

async fn handle_connect(req: Request<Body>, config: Arc<RwLock<ProxyConfig>>) -> Result<Response<Body>, hyper::Error> {
	// 1) We parse out the host:port
	let config_guard = config.read().await;
	let authority = match req.uri().authority() {
		Some(a) => a.clone(),
		None => {
			let mut response = Response::new(Body::from("Missing authority in CONNECT request"));
			*response.status_mut() = StatusCode::BAD_REQUEST;
			return Ok(response);
		}
	};

	let host = authority.host().to_string();
	let port = authority.port_u16().unwrap_or(443);

	// 2) Currently, you do:
	// let target_addr = format!("{}:{}", host, port);
	// let outbound = match tokio::net::TcpStream::connect(&target_addr).await { ... };

	// --- REPLACE THAT WITH FORCED-IPv6 LOGIC:

	// a) pick random local IPv6 from your interface
	let local_ip_str = match select_random_ip(&config_guard) {
		Ok(ip) => ip,
		Err(e) => {
			let mut r = Response::new(Body::from(e));
			*r.status_mut() = StatusCode::INTERNAL_SERVER_ERROR;
			return Ok(r);
		}
	};
	let local_v6 = match local_ip_str.parse::<std::net::Ipv6Addr>() {
		Ok(v6) => v6,
		Err(e) => {
			let mut r = Response::new(Body::from(format!("Invalid IPv6: {}", e)));
			*r.status_mut() = StatusCode::INTERNAL_SERVER_ERROR;
			return Ok(r);
		}
	};

	// b) resolve the remote domain to an IPv6 address
	//    if you want to do the same "hard-coded" override as code #2:
	let remote_v6 = if host == "pd.eu.a.pvp.net" || host == "pd.na.a.pvp.net" || host == "pd.kr.a.pvp.net" || host == "pd.ap.a.pvp.net" {
		// use your RIOT_PVP_NET_ADDRESS
		config_guard.riot_pvp_net_address
	} else if
		host == "api.account.riotgames.com" ||
		host == "eu.api.account.riotgames.com" ||
		host == "us.api.account.riotgames.com" ||
		host == "sea.api.account.riotgames.com" ||
		host == "ap.api.account.riotgames.com"
	{
		config_guard.riot_accounts_net_address
	} else if host == "auth.riotgames.com" || host == "authenticate.riotgames.com" {
		config_guard.riot_accounts_net_address
	} else {
		// fallback: do a AAAA lookup with tokio
		match tokio::net::lookup_host((host.as_str(), port)).await {
			Ok(addrs) => {
				// find the first IPv6 address
				if let Some(std::net::SocketAddr::V6(v6sock)) = addrs.into_iter().find(|a| a.is_ipv6()) {
					*v6sock.ip()
				} else {
					// if no AAAA record, you can fail or do some fallback to IPv4
					let mut r = Response::new(Body::from(format!("No AAAA record for {}", host)));
					*r.status_mut() = StatusCode::BAD_GATEWAY;
					return Ok(r);
				}
			}
			Err(e) => {
				let mut r = Response::new(Body::from(format!("DNS fail for {}: {}", host, e)));
				*r.status_mut() = StatusCode::BAD_GATEWAY;
				return Ok(r);
			}
		}
	};

	// c) create a new IPv6 socket, bind, then connect
	let socket = tokio::net::TcpSocket
		::new_v6()
		.map_err(|e| {
			eprintln!("Socket error: {}", e);
		})
		.unwrap();

	socket
		.bind(std::net::SocketAddr::V6(std::net::SocketAddrV6::new(local_v6, 0, 0, 0)))
		.map_err(|e| {
			eprintln!("Bind error: {}", e);
		})
		.unwrap();

	let outbound = socket.connect(std::net::SocketAddr::V6(std::net::SocketAddrV6::new(remote_v6, port, 0, 0))).await;

	let outbound = match outbound {
		Ok(s) => s,
		Err(e) => {
			let mut r = Response::new(Body::from(format!("Failed to connect IPv6: {e}")));
			*r.status_mut() = StatusCode::BAD_GATEWAY;
			return Ok(r);
		}
	};

	// d) 200 OK + set up the upgrade tunnel as you already had
	let mut response = Response::new(Body::empty());
	*response.status_mut() = StatusCode::OK;
	response.headers_mut().insert("X-Proxy-IPv6", local_ip_str.parse().unwrap());

	// spawn tunnel
	tokio::task::spawn(async move {
		match hyper::upgrade::on(req).await {
			Ok(mut upgraded) => {
				let (mut client_read, mut client_write) = tokio::io::split(upgraded);
				let (mut target_read, mut target_write) = tokio::io::split(outbound);

				let copy1 = tokio::io::copy(&mut client_read, &mut target_write);
				let copy2 = tokio::io::copy(&mut target_read, &mut client_write);
				let _ = futures::future::try_join(copy1, copy2).await;
			}
			Err(e) => eprintln!("Upgrade error: {}", e),
		}
	});

	Ok(response)
}

// Main proxy handler function
async fn proxy(req: Request<Body>, config: Arc<RwLock<ProxyConfig>>) -> Result<Response<Body>, hyper::Error> {
	// Use Proxy-Authorization header for authentication
	let config_guard = config.read().await;
	let auth_header = req.headers().get("Proxy-Authorization");
	let is_authenticated = config_guard.auth_system.is_valid(auth_header);

	//println!("Request: {:?}", req);

	if !is_authenticated {
		let mut response = Response::new(Body::from("Authentication required"));
		*response.status_mut() = StatusCode::UNAUTHORIZED;
		response.headers_mut().insert(hyper::header::WWW_AUTHENTICATE, hyper::http::HeaderValue::from_static("Basic realm=\"Proxy Authentication\""));
		return Ok(response);
	}

	// Handle CONNECT method for HTTPS tunneling
	if req.method() == hyper::Method::CONNECT {
		return handle_connect(req, config.clone()).await;
	}

	// Get the requested URI
	let uri = req.uri().clone();
	let host = uri.host().unwrap_or_default();

	// Check if the domain is blocked
	{
		if config_guard.is_domain_blocked(host) {
			let mut response = Response::new(Body::from("Domain is blocked"));
			*response.status_mut() = StatusCode::FORBIDDEN;
			return Ok(response);
		}
	}

	// Select a random IP to use
	let ip_str = match select_random_ip(&config_guard) {
		Ok(ip) => ip,
		Err(e) => {
			let mut response = Response::new(Body::from(e));
			*response.status_mut() = StatusCode::INTERNAL_SERVER_ERROR;
			return Ok(response);
		}
	};

	//println!("Selected IP: {}", ip_str);

	// Check if the IP is blocked
	{
		if config_guard.is_ip_blocked(&ip_str) {
			let mut response = Response::new(Body::from("Selected IP is blocked"));
			*response.status_mut() = StatusCode::FORBIDDEN;
			return Ok(response);
		}
	}

	// Create a reqwest client with the selected IP
	let client = match create_client(&ip_str, &config_guard) {
		Ok(client) => client,
		Err(e) => {
			let mut response = Response::new(Body::from(format!("Failed to create client: {}", e)));
			*response.status_mut() = StatusCode::INTERNAL_SERVER_ERROR;
			return Ok(response);
		}
	};

	// Convert hyper request to reqwest request
	let (parts, body_bytes) = req.into_parts();

	// Read body bytes
	let body_bytes = match hyper::body::to_bytes(body_bytes).await {
		Ok(bytes) => bytes,
		Err(e) => {
			let mut response = Response::new(Body::from(format!("Failed to read request body: {}", e)));
			*response.status_mut() = StatusCode::INTERNAL_SERVER_ERROR;
			return Ok(response);
		}
	};

	// Create a new reqwest request

	let mut reqwest_request = client.request(parts.method, parts.uri.to_string()).body(body_bytes);

	// Copy all headers
	{
		for (k, v) in parts.headers.iter() {
			reqwest_request = reqwest_request.header(k, v.clone());
		}
	}

	// Send the request using reqwest
	let resp = match reqwest_request.send().await {
		Ok(resp) => resp,
		Err(e) => {
			let mut response = Response::new(Body::from(format!("Request error: {}", e)));
			*response.status_mut() = StatusCode::BAD_GATEWAY;
			return Ok(response);
		}
	};

	// Convert reqwest response to hyper response
	let status = resp.status();
	let mut builder = Response::builder().status(status);

	// Copy headers
	{
		let headers = builder.headers_mut().expect("Failed to get headers");
		for (k, v) in resp.headers().iter() {
			headers.insert(k, v.clone());
		}
	}

	// Get response body
	let resp_body = match resp.bytes().await {
		Ok(bytes) => bytes,
		Err(e) => {
			let mut response = Response::new(Body::from(format!("Failed to read response body: {}", e)));
			*response.status_mut() = StatusCode::BAD_GATEWAY;
			return Ok(response);
		}
	};

	// Build final response
	let mut hyper_response = builder.body(Body::from(resp_body)).unwrap_or_else(|_| {
		let mut response = Response::new(Body::from("Failed to build response"));
		*response.status_mut() = StatusCode::INTERNAL_SERVER_ERROR;
		response
	});

	// Hier den Header setzen
	hyper_response.headers_mut().insert("X-Proxy-IPv6", ip_str.parse().unwrap());

	Ok(hyper_response)
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
	// Initialize logger
	dotenv().ok();
	env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));

	// Create proxy configuration
	let config = Arc::new(RwLock::new(ProxyConfig::new()));

	// Create the service
	let make_service = make_service_fn(move |_| {
		let config = config.clone();

		async move { Ok::<_, Infallible>(service_fn(move |req| proxy(req, config.clone()))) }
	});

	// Create the server
	let addr = SocketAddr::from((
		[0, 0, 0, 0],
		env
			::var("PORT")
			.unwrap_or_else(|_| "59123".to_string())
			.parse::<u16>()
			.unwrap(),
	));
	let server = Server::bind(&addr).serve(make_service);

	println!("{}", format!("Proxy server running on http://0.0.0.0:{}", addr.port()));

	// Run the server
	if let Err(e) = server.await {
		eprintln!("Server error: {}", e);
	}

	Ok(())
}
