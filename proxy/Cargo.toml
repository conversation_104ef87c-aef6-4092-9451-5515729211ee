[package]
name = "proxy"
version = "0.1.0"
edition = "2021"


# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
axum = "0.7.5"
reqwest = { version = "0.11", features = ["json"] }
tokio = { version = "1", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
netdev = "0.25"
rand = "0.8.5"
mimalloc = "0.1.43"
log = "0.4.21"
hyper = { version = "0.14", features = ["http1", "http2", "client", "server", "tcp"] }
hyper-tls = "0.5.0"
futures = "0.3.31"
env_logger = "0.11.7"
base64 = "0.22.1"
dotenv = "0.15.0"

[profile.dev]
overflow-checks = true

[profile.release]
lto = "thin"
strip = true
debug = false
opt-level = 2
