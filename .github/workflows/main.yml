# .github/workflows/build.yml
name: Build & Deploy Staging

on:
  push:
    branches:
      - 'rust_rewrite_axum'

jobs:
  build:
    runs-on: blacksmith-4vcpu-ubuntu-2204

    steps:
      - name: Setup GitLab credentials
        uses: de-vri-es/setup-git-credentials@v2
        with:
          credentials: ${{secrets.GITLAB_CREDENTIALS}}

      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Set up Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          target: x86_64-unknown-linux-musl
          override: true

      - name: Install dependencies
        run: sudo apt-get install -y build-essential && sudo apt-get install pkg-config libssl-dev

      - name: Build
        run: cargo build --release --target x86_64-unknown-linux-gnu

      - name: Upload files
        run: |
          mkdir -p ~/.ssh
          echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
          rsync -avz -e "ssh -p 30" ./target/x86_64-unknown-linux-gnu/release/api_server uploader@***************:/home/<USER>/API/staging/api_server
