name: Build Docker Image (ARM & x64 Optional)

on:
  workflow_dispatch:
    inputs:
      arch:
        description: 'Architecture to build (leave empty for both)'
        required: false
        default: ''
        type: choice
        options:
          - ''
          - amd64

jobs:
  init:
    name: Init Build Metadata
    runs-on: ubuntu-latest
    outputs:
      image_name: ${{ steps.vars.outputs.image_name }}
      tag: ${{ steps.vars.outputs.tag }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Extract version and tag
        id: vars
        run: |
          IMAGE_NAME=ghcr.io/henrik-3/hdev-api-rust
          VERSION=$(cargo metadata --no-deps --format-version 1 | jq -r '.packages[] | select(.name == "HenrikDevAPI") | .version')
          [ -z "$VERSION" ] || [ "$VERSION" = "null" ] && VERSION="0.0.0"
          TIMESTAMP=$(date +%Y%m%d-%H%M)
          TAG="${VERSION}-${TIMESTAMP}"
          echo "image_name=$IMAGE_NAME" >> "$GITHUB_OUTPUT"
          echo "tag=$TAG" >> "$GITHUB_OUTPUT"

  build:
    name: Build and Push Image (${{ matrix.arch }})
    needs: init
    runs-on:
      - self-hosted
      - ${{ matrix.runner }}

    strategy:
      matrix:
        arch: [ amd64 ]
        include:
          - arch: amd64
            runner: X64
            platform: linux/amd64

    steps:
      - name: Skip if arch doesn't match input
        if: ${{ github.event.inputs.arch != '' && github.event.inputs.arch != matrix.arch }}
        run: echo "Skipping build for ${{ matrix.arch }} because input was ${{ github.event.inputs.arch }}"

      - name: Checkout code
        if: ${{ github.event.inputs.arch == '' || github.event.inputs.arch == matrix.arch }}
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        if: ${{ github.event.inputs.arch == '' || github.event.inputs.arch == matrix.arch }}
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: stable
          components: cargo

      - name: Log in to GitHub Container Registry
        if: ${{ github.event.inputs.arch == '' || github.event.inputs.arch == matrix.arch }}
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: Henrik-3
          password: ${{ secrets.GH_DOCKER_REGISTRY_TOKEN }}

      - name: Build and push image (${{ matrix.arch }})
        if: ${{ github.event.inputs.arch == '' || github.event.inputs.arch == matrix.arch }}
        run: |
          docker build \
            --build-arg GIT_CREDENTIALS=${{ secrets.GITLAB_CREDENTIALS }} \
            -t ${{ needs.init.outputs.image_name }}:${{ needs.init.outputs.tag }}-${{ matrix.arch }} \
            --platform ${{ matrix.platform }} .
          docker push ${{ needs.init.outputs.image_name }}:${{ needs.init.outputs.tag }}-${{ matrix.arch }}

  manifest:
    name: Create and Push Multi-Arch Manifest
    if: github.event.inputs.arch == ''  # Only if building both
    needs: [ init, build ]
    runs-on:
      - self-hosted
      - X64

    steps:
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: Henrik-3
          password: ${{ secrets.GH_DOCKER_REGISTRY_TOKEN }}

      - name: Create and push multi-arch manifest
        run: |
          docker manifest create ${{ needs.init.outputs.image_name }}:${{ needs.init.outputs.tag }} \
            --amend ${{ needs.init.outputs.image_name }}:${{ needs.init.outputs.tag }}-amd64
          docker manifest push ${{ needs.init.outputs.image_name }}:${{ needs.init.outputs.tag }}

          docker manifest create ${{ needs.init.outputs.image_name }}:latest \
            --amend ${{ needs.init.outputs.image_name }}:${{ needs.init.outputs.tag }}-amd64
          docker manifest push ${{ needs.init.outputs.image_name }}:latest